(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var Zi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ld(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Od={exports:{}},rs={},Md={exports:{}},ve={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oi=Symbol.for("react.element"),Hh=Symbol.for("react.portal"),Wh=Symbol.for("react.fragment"),qh=Symbol.for("react.strict_mode"),Vh=Symbol.for("react.profiler"),Kh=Symbol.for("react.provider"),Qh=Symbol.for("react.context"),Gh=Symbol.for("react.forward_ref"),Yh=Symbol.for("react.suspense"),Xh=Symbol.for("react.memo"),Jh=Symbol.for("react.lazy"),xc=Symbol.iterator;function Zh(e){return e===null||typeof e!="object"?null:(e=xc&&e[xc]||e["@@iterator"],typeof e=="function"?e:null)}var jd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ud=Object.assign,Dd={};function Ao(e,t,n){this.props=e,this.context=t,this.refs=Dd,this.updater=n||jd}Ao.prototype.isReactComponent={};Ao.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Ao.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Rd(){}Rd.prototype=Ao.prototype;function tu(e,t,n){this.props=e,this.context=t,this.refs=Dd,this.updater=n||jd}var nu=tu.prototype=new Rd;nu.constructor=tu;Ud(nu,Ao.prototype);nu.isPureReactComponent=!0;var Sc=Array.isArray,zd=Object.prototype.hasOwnProperty,ru={current:null},$d={key:!0,ref:!0,__self:!0,__source:!0};function Fd(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)zd.call(t,r)&&!$d.hasOwnProperty(r)&&(o[r]=t[r]);var u=arguments.length-2;if(u===1)o.children=n;else if(1<u){for(var d=Array(u),f=0;f<u;f++)d[f]=arguments[f+2];o.children=d}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)o[r]===void 0&&(o[r]=u[r]);return{$$typeof:Oi,type:e,key:i,ref:s,props:o,_owner:ru.current}}function em(e,t){return{$$typeof:Oi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ou(e){return typeof e=="object"&&e!==null&&e.$$typeof===Oi}function tm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Cc=/\/+/g;function Ls(e,t){return typeof e=="object"&&e!==null&&e.key!=null?tm(""+e.key):t.toString(36)}function wl(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Oi:case Hh:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Ls(s,0):r,Sc(o)?(n="",e!=null&&(n=e.replace(Cc,"$&/")+"/"),wl(o,t,n,"",function(f){return f})):o!=null&&(ou(o)&&(o=em(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Cc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Sc(e))for(var u=0;u<e.length;u++){i=e[u];var d=r+Ls(i,u);s+=wl(i,t,n,d,o)}else if(d=Zh(e),typeof d=="function")for(e=d.call(e),u=0;!(i=e.next()).done;)i=i.value,d=r+Ls(i,u++),s+=wl(i,t,n,d,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function el(e,t,n){if(e==null)return e;var r=[],o=0;return wl(e,r,"","",function(i){return t.call(n,i,o++)}),r}function nm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var kt={current:null},kl={transition:null},rm={ReactCurrentDispatcher:kt,ReactCurrentBatchConfig:kl,ReactCurrentOwner:ru};function Bd(){throw Error("act(...) is not supported in production builds of React.")}ve.Children={map:el,forEach:function(e,t,n){el(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return el(e,function(){t++}),t},toArray:function(e){return el(e,function(t){return t})||[]},only:function(e){if(!ou(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ve.Component=Ao;ve.Fragment=Wh;ve.Profiler=Vh;ve.PureComponent=tu;ve.StrictMode=qh;ve.Suspense=Yh;ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rm;ve.act=Bd;ve.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ud({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=ru.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(d in t)zd.call(t,d)&&!$d.hasOwnProperty(d)&&(r[d]=t[d]===void 0&&u!==void 0?u[d]:t[d])}var d=arguments.length-2;if(d===1)r.children=n;else if(1<d){u=Array(d);for(var f=0;f<d;f++)u[f]=arguments[f+2];r.children=u}return{$$typeof:Oi,type:e.type,key:o,ref:i,props:r,_owner:s}};ve.createContext=function(e){return e={$$typeof:Qh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Kh,_context:e},e.Consumer=e};ve.createElement=Fd;ve.createFactory=function(e){var t=Fd.bind(null,e);return t.type=e,t};ve.createRef=function(){return{current:null}};ve.forwardRef=function(e){return{$$typeof:Gh,render:e}};ve.isValidElement=ou;ve.lazy=function(e){return{$$typeof:Jh,_payload:{_status:-1,_result:e},_init:nm}};ve.memo=function(e,t){return{$$typeof:Xh,type:e,compare:t===void 0?null:t}};ve.startTransition=function(e){var t=kl.transition;kl.transition={};try{e()}finally{kl.transition=t}};ve.unstable_act=Bd;ve.useCallback=function(e,t){return kt.current.useCallback(e,t)};ve.useContext=function(e){return kt.current.useContext(e)};ve.useDebugValue=function(){};ve.useDeferredValue=function(e){return kt.current.useDeferredValue(e)};ve.useEffect=function(e,t){return kt.current.useEffect(e,t)};ve.useId=function(){return kt.current.useId()};ve.useImperativeHandle=function(e,t,n){return kt.current.useImperativeHandle(e,t,n)};ve.useInsertionEffect=function(e,t){return kt.current.useInsertionEffect(e,t)};ve.useLayoutEffect=function(e,t){return kt.current.useLayoutEffect(e,t)};ve.useMemo=function(e,t){return kt.current.useMemo(e,t)};ve.useReducer=function(e,t,n){return kt.current.useReducer(e,t,n)};ve.useRef=function(e){return kt.current.useRef(e)};ve.useState=function(e){return kt.current.useState(e)};ve.useSyncExternalStore=function(e,t,n){return kt.current.useSyncExternalStore(e,t,n)};ve.useTransition=function(){return kt.current.useTransition()};ve.version="18.3.1";Md.exports=ve;var Z=Md.exports;const Hd=Ld(Z);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var om=Z,im=Symbol.for("react.element"),lm=Symbol.for("react.fragment"),sm=Object.prototype.hasOwnProperty,am=om.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,um={key:!0,ref:!0,__self:!0,__source:!0};function Wd(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)sm.call(t,r)&&!um.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:im,type:e,key:i,ref:s,props:o,_owner:am.current}}rs.Fragment=lm;rs.jsx=Wd;rs.jsxs=Wd;Od.exports=rs;var iu=Od.exports;const cm=iu.Fragment,E=iu.jsx,z=iu.jsxs;var Al={exports:{}};/*! For license information please see lsplugin.user.js.LICENSE.txt */Al.exports;(function(e,t){(function(n,r){e.exports=r()})(self,()=>(()=>{var n={227:(s,u,d)=>{var f=d(155);u.formatArgs=function(N){if(N[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+N[0]+(this.useColors?"%c ":" ")+"+"+s.exports.humanize(this.diff),!this.useColors)return;const _="color: "+this.color;N.splice(1,0,_,"color: inherit");let S=0,w=0;N[0].replace(/%[a-zA-Z%]/g,C=>{C!=="%%"&&(S++,C==="%c"&&(w=S))}),N.splice(w,0,_)},u.save=function(N){try{N?u.storage.setItem("debug",N):u.storage.removeItem("debug")}catch{}},u.load=function(){let N;try{N=u.storage.getItem("debug")}catch{}return!N&&f!==void 0&&"env"in f&&(N=f.env.DEBUG),N},u.useColors=function(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},u.storage=function(){try{return localStorage}catch{}}(),u.destroy=(()=>{let N=!1;return()=>{N||(N=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),u.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],u.log=console.debug||console.log||(()=>{}),s.exports=d(447)(u);const{formatters:v}=s.exports;v.j=function(N){try{return JSON.stringify(N)}catch(_){return"[UnexpectedJSONParseError]: "+_.message}}},447:(s,u,d)=>{s.exports=function(f){function v(S){let w,C,P,h=null;function p(...y){if(!p.enabled)return;const k=p,T=Number(new Date),A=T-(w||T);k.diff=A,k.prev=w,k.curr=T,w=T,y[0]=v.coerce(y[0]),typeof y[0]!="string"&&y.unshift("%O");let m=0;y[0]=y[0].replace(/%([a-zA-Z%])/g,(x,O)=>{if(x==="%%")return"%";m++;const U=v.formatters[O];if(typeof U=="function"){const ne=y[m];x=U.call(k,ne),y.splice(m,1),m--}return x}),v.formatArgs.call(k,y),(k.log||v.log).apply(k,y)}return p.namespace=S,p.useColors=v.useColors(),p.color=v.selectColor(S),p.extend=N,p.destroy=v.destroy,Object.defineProperty(p,"enabled",{enumerable:!0,configurable:!1,get:()=>h!==null?h:(C!==v.namespaces&&(C=v.namespaces,P=v.enabled(S)),P),set:y=>{h=y}}),typeof v.init=="function"&&v.init(p),p}function N(S,w){const C=v(this.namespace+(w===void 0?":":w)+S);return C.log=this.log,C}function _(S){return S.toString().substring(2,S.toString().length-2).replace(/\.\*\?$/,"*")}return v.debug=v,v.default=v,v.coerce=function(S){return S instanceof Error?S.stack||S.message:S},v.disable=function(){const S=[...v.names.map(_),...v.skips.map(_).map(w=>"-"+w)].join(",");return v.enable(""),S},v.enable=function(S){let w;v.save(S),v.namespaces=S,v.names=[],v.skips=[];const C=(typeof S=="string"?S:"").split(/[\s,]+/),P=C.length;for(w=0;w<P;w++)C[w]&&((S=C[w].replace(/\*/g,".*?"))[0]==="-"?v.skips.push(new RegExp("^"+S.slice(1)+"$")):v.names.push(new RegExp("^"+S+"$")))},v.enabled=function(S){if(S[S.length-1]==="*")return!0;let w,C;for(w=0,C=v.skips.length;w<C;w++)if(v.skips[w].test(S))return!1;for(w=0,C=v.names.length;w<C;w++)if(v.names[w].test(S))return!0;return!1},v.humanize=d(824),v.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(f).forEach(S=>{v[S]=f[S]}),v.names=[],v.skips=[],v.formatters={},v.selectColor=function(S){let w=0;for(let C=0;C<S.length;C++)w=(w<<5)-w+S.charCodeAt(C),w|=0;return v.colors[Math.abs(w)%v.colors.length]},v.enable(v.load()),v}},996:s=>{var u=function(P){return function(h){return!!h&&typeof h=="object"}(P)&&!function(h){var p=Object.prototype.toString.call(h);return p==="[object RegExp]"||p==="[object Date]"||function(y){return y.$$typeof===d}(h)}(P)},d=typeof Symbol=="function"&&Symbol.for?Symbol.for("react.element"):60103;function f(P,h){return h.clone!==!1&&h.isMergeableObject(P)?w((p=P,Array.isArray(p)?[]:{}),P,h):P;var p}function v(P,h,p){return P.concat(h).map(function(y){return f(y,p)})}function N(P){return Object.keys(P).concat(function(h){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(h).filter(function(p){return Object.propertyIsEnumerable.call(h,p)}):[]}(P))}function _(P,h){try{return h in P}catch{return!1}}function S(P,h,p){var y={};return p.isMergeableObject(P)&&N(P).forEach(function(k){y[k]=f(P[k],p)}),N(h).forEach(function(k){(function(T,A){return _(T,A)&&!(Object.hasOwnProperty.call(T,A)&&Object.propertyIsEnumerable.call(T,A))})(P,k)||(_(P,k)&&p.isMergeableObject(h[k])?y[k]=function(T,A){if(!A.customMerge)return w;var m=A.customMerge(T);return typeof m=="function"?m:w}(k,p)(P[k],h[k],p):y[k]=f(h[k],p))}),y}function w(P,h,p){(p=p||{}).arrayMerge=p.arrayMerge||v,p.isMergeableObject=p.isMergeableObject||u,p.cloneUnlessOtherwiseSpecified=f;var y=Array.isArray(h);return y===Array.isArray(P)?y?p.arrayMerge(P,h,p):S(P,h,p):f(h,p)}w.all=function(P,h){if(!Array.isArray(P))throw new Error("first argument should be an array");return P.reduce(function(p,y){return w(p,y,h)},{})};var C=w;s.exports=C},856:function(s){s.exports=function(){function u(V){return u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},u(V)}function d(V,J){return d=Object.setPrototypeOf||function(ie,me){return ie.__proto__=me,ie},d(V,J)}function f(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function v(V,J,ie){return v=f()?Reflect.construct:function(me,Ue,Lt){var Bt=[null];Bt.push.apply(Bt,Ue);var hn=new(Function.bind.apply(me,Bt));return Lt&&d(hn,Lt.prototype),hn},v.apply(null,arguments)}function N(V){return _(V)||S(V)||w(V)||P()}function _(V){if(Array.isArray(V))return C(V)}function S(V){if(typeof Symbol<"u"&&V[Symbol.iterator]!=null||V["@@iterator"]!=null)return Array.from(V)}function w(V,J){if(V){if(typeof V=="string")return C(V,J);var ie=Object.prototype.toString.call(V).slice(8,-1);return ie==="Object"&&V.constructor&&(ie=V.constructor.name),ie==="Map"||ie==="Set"?Array.from(V):ie==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(ie)?C(V,J):void 0}}function C(V,J){(J==null||J>V.length)&&(J=V.length);for(var ie=0,me=new Array(J);ie<J;ie++)me[ie]=V[ie];return me}function P(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var h=Object.hasOwnProperty,p=Object.setPrototypeOf,y=Object.isFrozen,k=Object.getPrototypeOf,T=Object.getOwnPropertyDescriptor,A=Object.freeze,m=Object.seal,x=Object.create,O=typeof Reflect<"u"&&Reflect,U=O.apply,ne=O.construct;U||(U=function(V,J,ie){return V.apply(J,ie)}),A||(A=function(V){return V}),m||(m=function(V){return V}),ne||(ne=function(V,J){return v(V,N(J))});var ce=se(Array.prototype.forEach),fe=se(Array.prototype.pop),pe=se(Array.prototype.push),W=se(String.prototype.toLowerCase),X=se(String.prototype.match),ee=se(String.prototype.replace),H=se(String.prototype.indexOf),I=se(String.prototype.trim),j=se(RegExp.prototype.test),B=je(TypeError);function se(V){return function(J){for(var ie=arguments.length,me=new Array(ie>1?ie-1:0),Ue=1;Ue<ie;Ue++)me[Ue-1]=arguments[Ue];return U(V,J,me)}}function je(V){return function(){for(var J=arguments.length,ie=new Array(J),me=0;me<J;me++)ie[me]=arguments[me];return ne(V,ie)}}function G(V,J){p&&p(V,null);for(var ie=J.length;ie--;){var me=J[ie];if(typeof me=="string"){var Ue=W(me);Ue!==me&&(y(J)||(J[ie]=Ue),me=Ue)}V[me]=!0}return V}function ue(V){var J,ie=x(null);for(J in V)U(h,V,[J])&&(ie[J]=V[J]);return ie}function ye(V,J){for(;V!==null;){var ie=T(V,J);if(ie){if(ie.get)return se(ie.get);if(typeof ie.value=="function")return se(ie.value)}V=k(V)}function me(Ue){return console.warn("fallback value for",Ue),null}return me}var $e=A(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ge=A(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),lt=A(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Jt=A(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),_e=A(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),Pe=A(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),tt=A(["#text"]),st=A(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),St=A(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Zt=A(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),gt=A(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),nt=m(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Ft=m(/<%[\w\W]*|[\w\W]*%>/gm),Mo=m(/^data-[\-\w.\u00B7-\uFFFF]/),Ct=m(/^aria-[\-\w]+$/),_t=m(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),gr=m(/^(?:\w+script|data):/i),yr=m(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),jo=m(/^html$/i),en=function(){return typeof window>"u"?null:window},vr=function(V,J){if(u(V)!=="object"||typeof V.createPolicy!="function")return null;var ie=null,me="data-tt-policy-suffix";J.currentScript&&J.currentScript.hasAttribute(me)&&(ie=J.currentScript.getAttribute(me));var Ue="dompurify"+(ie?"#"+ie:"");try{return V.createPolicy(Ue,{createHTML:function(Lt){return Lt}})}catch{return console.warn("TrustedTypes policy "+Ue+" could not be created."),null}};function Vr(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:en(),J=function(b){return Vr(b)};if(J.version="2.3.8",J.removed=[],!V||!V.document||V.document.nodeType!==9)return J.isSupported=!1,J;var ie=V.document,me=V.document,Ue=V.DocumentFragment,Lt=V.HTMLTemplateElement,Bt=V.Node,hn=V.Element,wr=V.NodeFilter,kr=V.NamedNodeMap,tn=kr===void 0?V.NamedNodeMap||V.MozNamedAttrMap:kr,Uo=V.HTMLFormElement,Ot=V.DOMParser,Kr=V.trustedTypes,Dn=hn.prototype,Do=ye(Dn,"cloneNode"),xr=ye(Dn,"nextSibling"),Qr=ye(Dn,"childNodes"),Sr=ye(Dn,"parentNode");if(typeof Lt=="function"){var Mt=me.createElement("template");Mt.content&&Mt.content.ownerDocument&&(me=Mt.content.ownerDocument)}var bt=vr(Kr,ie),mn=bt?bt.createHTML(""):"",Rn=me,Ht=Rn.implementation,gn=Rn.createNodeIterator,Gr=Rn.createDocumentFragment,Yr=Rn.getElementsByTagName,Ro=ie.importNode,Xr={};try{Xr=ue(me).documentMode?me.documentMode:{}}catch{}var yt={};J.isSupported=typeof Sr=="function"&&Ht&&Ht.createHTMLDocument!==void 0&&Xr!==9;var $,D,he=nt,ge=Ft,we=Mo,Ce=Ct,De=gr,Ke=yr,ke=_t,Te=null,zn=G({},[].concat(N($e),N(Ge),N(lt),N(_e),N(tt))),qe=null,$n=G({},[].concat(N(st),N(St),N(Zt),N(gt))),Re=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Fn=null,Jr=null,zo=!0,$o=!0,Ri=!1,Bn=!1,_n=!1,Fo=!1,Bo=!1,Hn=!1,Zr=!1,Wn=!1,zi=!0,Ho=!0,qn=!1,nn={},Vn=null,$i=G({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Fi=null,Bi=G({},["audio","video","img","source","image","track"]),Wo=null,bn=G({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Kn="http://www.w3.org/1998/Math/MathML",qo="http://www.w3.org/2000/svg",yn="http://www.w3.org/1999/xhtml",eo=yn,Hi=!1,Cr=["application/xhtml+xml","text/html"],_r="text/html",Qn=null,ws=me.createElement("form"),Wi=function(b){return b instanceof RegExp||b instanceof Function},Vo=function(b){Qn&&Qn===b||(b&&u(b)==="object"||(b={}),b=ue(b),Te="ALLOWED_TAGS"in b?G({},b.ALLOWED_TAGS):zn,qe="ALLOWED_ATTR"in b?G({},b.ALLOWED_ATTR):$n,Wo="ADD_URI_SAFE_ATTR"in b?G(ue(bn),b.ADD_URI_SAFE_ATTR):bn,Fi="ADD_DATA_URI_TAGS"in b?G(ue(Bi),b.ADD_DATA_URI_TAGS):Bi,Vn="FORBID_CONTENTS"in b?G({},b.FORBID_CONTENTS):$i,Fn="FORBID_TAGS"in b?G({},b.FORBID_TAGS):{},Jr="FORBID_ATTR"in b?G({},b.FORBID_ATTR):{},nn="USE_PROFILES"in b&&b.USE_PROFILES,zo=b.ALLOW_ARIA_ATTR!==!1,$o=b.ALLOW_DATA_ATTR!==!1,Ri=b.ALLOW_UNKNOWN_PROTOCOLS||!1,Bn=b.SAFE_FOR_TEMPLATES||!1,_n=b.WHOLE_DOCUMENT||!1,Hn=b.RETURN_DOM||!1,Zr=b.RETURN_DOM_FRAGMENT||!1,Wn=b.RETURN_TRUSTED_TYPE||!1,Bo=b.FORCE_BODY||!1,zi=b.SANITIZE_DOM!==!1,Ho=b.KEEP_CONTENT!==!1,qn=b.IN_PLACE||!1,ke=b.ALLOWED_URI_REGEXP||ke,eo=b.NAMESPACE||yn,b.CUSTOM_ELEMENT_HANDLING&&Wi(b.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Re.tagNameCheck=b.CUSTOM_ELEMENT_HANDLING.tagNameCheck),b.CUSTOM_ELEMENT_HANDLING&&Wi(b.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Re.attributeNameCheck=b.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),b.CUSTOM_ELEMENT_HANDLING&&typeof b.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(Re.allowCustomizedBuiltInElements=b.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),$=$=Cr.indexOf(b.PARSER_MEDIA_TYPE)===-1?_r:b.PARSER_MEDIA_TYPE,D=$==="application/xhtml+xml"?function(Y){return Y}:W,Bn&&($o=!1),Zr&&(Hn=!0),nn&&(Te=G({},N(tt)),qe=[],nn.html===!0&&(G(Te,$e),G(qe,st)),nn.svg===!0&&(G(Te,Ge),G(qe,St),G(qe,gt)),nn.svgFilters===!0&&(G(Te,lt),G(qe,St),G(qe,gt)),nn.mathMl===!0&&(G(Te,_e),G(qe,Zt),G(qe,gt))),b.ADD_TAGS&&(Te===zn&&(Te=ue(Te)),G(Te,b.ADD_TAGS)),b.ADD_ATTR&&(qe===$n&&(qe=ue(qe)),G(qe,b.ADD_ATTR)),b.ADD_URI_SAFE_ATTR&&G(Wo,b.ADD_URI_SAFE_ATTR),b.FORBID_CONTENTS&&(Vn===$i&&(Vn=ue(Vn)),G(Vn,b.FORBID_CONTENTS)),Ho&&(Te["#text"]=!0),_n&&G(Te,["html","head","body"]),Te.table&&(G(Te,["tbody"]),delete Fn.tbody),A&&A(b),Qn=b)},qi=G({},["mi","mo","mn","ms","mtext"]),Vi=G({},["foreignobject","desc","title","annotation-xml"]),ks=G({},["title","style","font","a","script"]),rn=G({},Ge);G(rn,lt),G(rn,Jt);var to=G({},_e);G(to,Pe);var xs=function(b){var Y=Sr(b);Y&&Y.tagName||(Y={namespaceURI:yn,tagName:"template"});var K=W(b.tagName),Ee=W(Y.tagName);return b.namespaceURI===qo?Y.namespaceURI===yn?K==="svg":Y.namespaceURI===Kn?K==="svg"&&(Ee==="annotation-xml"||qi[Ee]):!!rn[K]:b.namespaceURI===Kn?Y.namespaceURI===yn?K==="math":Y.namespaceURI===qo?K==="math"&&Vi[Ee]:!!to[K]:b.namespaceURI===yn&&!(Y.namespaceURI===qo&&!Vi[Ee])&&!(Y.namespaceURI===Kn&&!qi[Ee])&&!to[K]&&(ks[K]||!rn[K])},on=function(b){pe(J.removed,{element:b});try{b.parentNode.removeChild(b)}catch{try{b.outerHTML=mn}catch{b.remove()}}},br=function(b,Y){try{pe(J.removed,{attribute:Y.getAttributeNode(b),from:Y})}catch{pe(J.removed,{attribute:null,from:Y})}if(Y.removeAttribute(b),b==="is"&&!qe[b])if(Hn||Zr)try{on(Y)}catch{}else try{Y.setAttribute(b,"")}catch{}},Ki=function(b){var Y,K;if(Bo)b="<remove></remove>"+b;else{var Ee=X(b,/^[\r\n\t ]+/);K=Ee&&Ee[0]}$==="application/xhtml+xml"&&(b='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+b+"</body></html>");var Fe=bt?bt.createHTML(b):b;if(eo===yn)try{Y=new Ot().parseFromString(Fe,$)}catch{}if(!Y||!Y.documentElement){Y=Ht.createDocument(eo,"template",null);try{Y.documentElement.innerHTML=Hi?"":Fe}catch{}}var rt=Y.body||Y.documentElement;return b&&K&&rt.insertBefore(me.createTextNode(K),rt.childNodes[0]||null),eo===yn?Yr.call(Y,_n?"html":"body")[0]:_n?Y.documentElement:rt},Qi=function(b){return gn.call(b.ownerDocument||b,b,wr.SHOW_ELEMENT|wr.SHOW_COMMENT|wr.SHOW_TEXT,null,!1)},Ss=function(b){return b instanceof Uo&&(typeof b.nodeName!="string"||typeof b.textContent!="string"||typeof b.removeChild!="function"||!(b.attributes instanceof tn)||typeof b.removeAttribute!="function"||typeof b.setAttribute!="function"||typeof b.namespaceURI!="string"||typeof b.insertBefore!="function")},Er=function(b){return u(Bt)==="object"?b instanceof Bt:b&&u(b)==="object"&&typeof b.nodeType=="number"&&typeof b.nodeName=="string"},dt=function(b,Y,K){yt[b]&&ce(yt[b],function(Ee){Ee.call(J,Y,K,Qn)})},Nr=function(b){var Y;if(dt("beforeSanitizeElements",b,null),Ss(b)||j(/[\u0080-\uFFFF]/,b.nodeName))return on(b),!0;var K=D(b.nodeName);if(dt("uponSanitizeElement",b,{tagName:K,allowedTags:Te}),b.hasChildNodes()&&!Er(b.firstElementChild)&&(!Er(b.content)||!Er(b.content.firstElementChild))&&j(/<[/\w]/g,b.innerHTML)&&j(/<[/\w]/g,b.textContent)||K==="select"&&j(/<template/i,b.innerHTML))return on(b),!0;if(!Te[K]||Fn[K]){if(!Fn[K]&&En(K)&&(Re.tagNameCheck instanceof RegExp&&j(Re.tagNameCheck,K)||Re.tagNameCheck instanceof Function&&Re.tagNameCheck(K)))return!1;if(Ho&&!Vn[K]){var Ee=Sr(b)||b.parentNode,Fe=Qr(b)||b.childNodes;if(Fe&&Ee)for(var rt=Fe.length-1;rt>=0;--rt)Ee.insertBefore(Do(Fe[rt],!0),xr(b))}return on(b),!0}return b instanceof hn&&!xs(b)?(on(b),!0):K!=="noscript"&&K!=="noembed"||!j(/<\/no(script|embed)/i,b.innerHTML)?(Bn&&b.nodeType===3&&(Y=b.textContent,Y=ee(Y,he," "),Y=ee(Y,ge," "),b.textContent!==Y&&(pe(J.removed,{element:b.cloneNode()}),b.textContent=Y)),dt("afterSanitizeElements",b,null),!1):(on(b),!0)},Gi=function(b,Y,K){if(zi&&(Y==="id"||Y==="name")&&(K in me||K in ws))return!1;if(!($o&&!Jr[Y]&&j(we,Y))){if(!(zo&&j(Ce,Y))){if(!qe[Y]||Jr[Y]){if(!(En(b)&&(Re.tagNameCheck instanceof RegExp&&j(Re.tagNameCheck,b)||Re.tagNameCheck instanceof Function&&Re.tagNameCheck(b))&&(Re.attributeNameCheck instanceof RegExp&&j(Re.attributeNameCheck,Y)||Re.attributeNameCheck instanceof Function&&Re.attributeNameCheck(Y))||Y==="is"&&Re.allowCustomizedBuiltInElements&&(Re.tagNameCheck instanceof RegExp&&j(Re.tagNameCheck,K)||Re.tagNameCheck instanceof Function&&Re.tagNameCheck(K))))return!1}else if(!Wo[Y]){if(!j(ke,ee(K,Ke,""))){if((Y!=="src"&&Y!=="xlink:href"&&Y!=="href"||b==="script"||H(K,"data:")!==0||!Fi[b])&&!(Ri&&!j(De,ee(K,Ke,"")))){if(K)return!1}}}}}return!0},En=function(b){return b.indexOf("-")>0},Ir=function(b){var Y,K,Ee,Fe;dt("beforeSanitizeAttributes",b,null);var rt=b.attributes;if(rt){var Ye={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:qe};for(Fe=rt.length;Fe--;){var Pr=Y=rt[Fe],vn=Pr.name,Et=Pr.namespaceURI;if(K=vn==="value"?Y.value:I(Y.value),Ee=D(vn),Ye.attrName=Ee,Ye.attrValue=K,Ye.keepAttr=!0,Ye.forceKeepAttr=void 0,dt("uponSanitizeAttribute",b,Ye),K=Ye.attrValue,!Ye.forceKeepAttr&&(br(vn,b),Ye.keepAttr))if(j(/\/>/i,K))br(vn,b);else{Bn&&(K=ee(K,he," "),K=ee(K,ge," "));var Ko=D(b.nodeName);if(Gi(Ko,Ee,K))try{Et?b.setAttributeNS(Et,vn,K):b.setAttribute(vn,K),fe(J.removed)}catch{}}}dt("afterSanitizeAttributes",b,null)}},Cs=function b(Y){var K,Ee=Qi(Y);for(dt("beforeSanitizeShadowDOM",Y,null);K=Ee.nextNode();)dt("uponSanitizeShadowNode",K,null),Nr(K)||(K.content instanceof Ue&&b(K.content),Ir(K));dt("afterSanitizeShadowDOM",Y,null)};return J.sanitize=function(b,Y){var K,Ee,Fe,rt,Ye;if((Hi=!b)&&(b="<!-->"),typeof b!="string"&&!Er(b)){if(typeof b.toString!="function")throw B("toString is not a function");if(typeof(b=b.toString())!="string")throw B("dirty is not a string, aborting")}if(!J.isSupported){if(u(V.toStaticHTML)==="object"||typeof V.toStaticHTML=="function"){if(typeof b=="string")return V.toStaticHTML(b);if(Er(b))return V.toStaticHTML(b.outerHTML)}return b}if(Fo||Vo(Y),J.removed=[],typeof b=="string"&&(qn=!1),qn){if(b.nodeName){var Pr=D(b.nodeName);if(!Te[Pr]||Fn[Pr])throw B("root node is forbidden and cannot be sanitized in-place")}}else if(b instanceof Bt)(Ee=(K=Ki("<!---->")).ownerDocument.importNode(b,!0)).nodeType===1&&Ee.nodeName==="BODY"||Ee.nodeName==="HTML"?K=Ee:K.appendChild(Ee);else{if(!Hn&&!Bn&&!_n&&b.indexOf("<")===-1)return bt&&Wn?bt.createHTML(b):b;if(!(K=Ki(b)))return Hn?null:Wn?mn:""}K&&Bo&&on(K.firstChild);for(var vn=Qi(qn?b:K);Fe=vn.nextNode();)Fe.nodeType===3&&Fe===rt||Nr(Fe)||(Fe.content instanceof Ue&&Cs(Fe.content),Ir(Fe),rt=Fe);if(rt=null,qn)return b;if(Hn){if(Zr)for(Ye=Gr.call(K.ownerDocument);K.firstChild;)Ye.appendChild(K.firstChild);else Ye=K;return qe.shadowroot&&(Ye=Ro.call(ie,Ye,!0)),Ye}var Et=_n?K.outerHTML:K.innerHTML;return _n&&Te["!doctype"]&&K.ownerDocument&&K.ownerDocument.doctype&&K.ownerDocument.doctype.name&&j(jo,K.ownerDocument.doctype.name)&&(Et="<!DOCTYPE "+K.ownerDocument.doctype.name+`>
`+Et),Bn&&(Et=ee(Et,he," "),Et=ee(Et,ge," ")),bt&&Wn?bt.createHTML(Et):Et},J.setConfig=function(b){Vo(b),Fo=!0},J.clearConfig=function(){Qn=null,Fo=!1},J.isValidAttribute=function(b,Y,K){Qn||Vo({});var Ee=D(b),Fe=D(Y);return Gi(Ee,Fe,K)},J.addHook=function(b,Y){typeof Y=="function"&&(yt[b]=yt[b]||[],pe(yt[b],Y))},J.removeHook=function(b){if(yt[b])return fe(yt[b])},J.removeHooks=function(b){yt[b]&&(yt[b]=[])},J.removeAllHooks=function(){yt={}},J}return Vr()}()},729:s=>{var u=Object.prototype.hasOwnProperty,d="~";function f(){}function v(w,C,P){this.fn=w,this.context=C,this.once=P||!1}function N(w,C,P,h,p){if(typeof P!="function")throw new TypeError("The listener must be a function");var y=new v(P,h||w,p),k=d?d+C:C;return w._events[k]?w._events[k].fn?w._events[k]=[w._events[k],y]:w._events[k].push(y):(w._events[k]=y,w._eventsCount++),w}function _(w,C){--w._eventsCount==0?w._events=new f:delete w._events[C]}function S(){this._events=new f,this._eventsCount=0}Object.create&&(f.prototype=Object.create(null),new f().__proto__||(d=!1)),S.prototype.eventNames=function(){var w,C,P=[];if(this._eventsCount===0)return P;for(C in w=this._events)u.call(w,C)&&P.push(d?C.slice(1):C);return Object.getOwnPropertySymbols?P.concat(Object.getOwnPropertySymbols(w)):P},S.prototype.listeners=function(w){var C=d?d+w:w,P=this._events[C];if(!P)return[];if(P.fn)return[P.fn];for(var h=0,p=P.length,y=new Array(p);h<p;h++)y[h]=P[h].fn;return y},S.prototype.listenerCount=function(w){var C=d?d+w:w,P=this._events[C];return P?P.fn?1:P.length:0},S.prototype.emit=function(w,C,P,h,p,y){var k=d?d+w:w;if(!this._events[k])return!1;var T,A,m=this._events[k],x=arguments.length;if(m.fn){switch(m.once&&this.removeListener(w,m.fn,void 0,!0),x){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,C),!0;case 3:return m.fn.call(m.context,C,P),!0;case 4:return m.fn.call(m.context,C,P,h),!0;case 5:return m.fn.call(m.context,C,P,h,p),!0;case 6:return m.fn.call(m.context,C,P,h,p,y),!0}for(A=1,T=new Array(x-1);A<x;A++)T[A-1]=arguments[A];m.fn.apply(m.context,T)}else{var O,U=m.length;for(A=0;A<U;A++)switch(m[A].once&&this.removeListener(w,m[A].fn,void 0,!0),x){case 1:m[A].fn.call(m[A].context);break;case 2:m[A].fn.call(m[A].context,C);break;case 3:m[A].fn.call(m[A].context,C,P);break;case 4:m[A].fn.call(m[A].context,C,P,h);break;default:if(!T)for(O=1,T=new Array(x-1);O<x;O++)T[O-1]=arguments[O];m[A].fn.apply(m[A].context,T)}}return!0},S.prototype.on=function(w,C,P){return N(this,w,C,P,!1)},S.prototype.once=function(w,C,P){return N(this,w,C,P,!0)},S.prototype.removeListener=function(w,C,P,h){var p=d?d+w:w;if(!this._events[p])return this;if(!C)return _(this,p),this;var y=this._events[p];if(y.fn)y.fn!==C||h&&!y.once||P&&y.context!==P||_(this,p);else{for(var k=0,T=[],A=y.length;k<A;k++)(y[k].fn!==C||h&&!y[k].once||P&&y[k].context!==P)&&T.push(y[k]);T.length?this._events[p]=T.length===1?T[0]:T:_(this,p)}return this},S.prototype.removeAllListeners=function(w){var C;return w?(C=d?d+w:w,this._events[C]&&_(this,C)):(this._events=new f,this._eventsCount=0),this},S.prototype.off=S.prototype.removeListener,S.prototype.addListener=S.prototype.on,S.prefixed=d,S.EventEmitter=S,s.exports=S},717:s=>{typeof Object.create=="function"?s.exports=function(u,d){u.super_=d,u.prototype=Object.create(d.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}})}:s.exports=function(u,d){u.super_=d;var f=function(){};f.prototype=d.prototype,u.prototype=new f,u.prototype.constructor=u}},824:s=>{var u=1e3,d=60*u,f=60*d,v=24*f,N=7*v,_=365.25*v;function S(w,C,P,h){var p=C>=1.5*P;return Math.round(w/P)+" "+h+(p?"s":"")}s.exports=function(w,C){C=C||{};var P=typeof w;if(P==="string"&&w.length>0)return function(h){if(!((h=String(h)).length>100)){var p=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(h);if(p){var y=parseFloat(p[1]);switch((p[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return y*_;case"weeks":case"week":case"w":return y*N;case"days":case"day":case"d":return y*v;case"hours":case"hour":case"hrs":case"hr":case"h":return y*f;case"minutes":case"minute":case"mins":case"min":case"m":return y*d;case"seconds":case"second":case"secs":case"sec":case"s":return y*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return y;default:return}}}}(w);if(P==="number"&&isFinite(w))return C.long?function(h){var p=Math.abs(h);return p>=v?S(h,p,v,"day"):p>=f?S(h,p,f,"hour"):p>=d?S(h,p,d,"minute"):p>=u?S(h,p,u,"second"):h+" ms"}(w):function(h){var p=Math.abs(h);return p>=v?Math.round(h/v)+"d":p>=f?Math.round(h/f)+"h":p>=d?Math.round(h/d)+"m":p>=u?Math.round(h/u)+"s":h+"ms"}(w);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(w))}},520:(s,u,d)=>{var f=d(155),v=f.platform==="win32",N=d(539);function _(m,x){for(var O=[],U=0;U<m.length;U++){var ne=m[U];ne&&ne!=="."&&(ne===".."?O.length&&O[O.length-1]!==".."?O.pop():x&&O.push(".."):O.push(ne))}return O}function S(m){for(var x=m.length-1,O=0;O<=x&&!m[O];O++);for(var U=x;U>=0&&!m[U];U--);return O===0&&U===x?m:O>U?[]:m.slice(O,U+1)}var w=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,C=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,P={};function h(m){var x=w.exec(m),O=(x[1]||"")+(x[2]||""),U=x[3]||"",ne=C.exec(U);return[O,ne[1],ne[2],ne[3]]}function p(m){var x=w.exec(m),O=x[1]||"",U=!!O&&O[1]!==":";return{device:O,isUnc:U,isAbsolute:U||!!x[2],tail:x[3]}}function y(m){return"\\\\"+m.replace(/^[\\\/]+/,"").replace(/[\\\/]+/g,"\\")}P.resolve=function(){for(var m="",x="",O=!1,U=arguments.length-1;U>=-1;U--){var ne;if(U>=0?ne=arguments[U]:m?(ne=f.env["="+m])&&ne.substr(0,3).toLowerCase()===m.toLowerCase()+"\\"||(ne=m+"\\"):ne=f.cwd(),!N.isString(ne))throw new TypeError("Arguments to path.resolve must be strings");if(ne){var ce=p(ne),fe=ce.device,pe=ce.isUnc,W=ce.isAbsolute,X=ce.tail;if((!fe||!m||fe.toLowerCase()===m.toLowerCase())&&(m||(m=fe),O||(x=X+"\\"+x,O=W),m&&O))break}}return pe&&(m=y(m)),m+(O?"\\":"")+(x=_(x.split(/[\\\/]+/),!O).join("\\"))||"."},P.normalize=function(m){var x=p(m),O=x.device,U=x.isUnc,ne=x.isAbsolute,ce=x.tail,fe=/[\\\/]$/.test(ce);return(ce=_(ce.split(/[\\\/]+/),!ne).join("\\"))||ne||(ce="."),ce&&fe&&(ce+="\\"),U&&(O=y(O)),O+(ne?"\\":"")+ce},P.isAbsolute=function(m){return p(m).isAbsolute},P.join=function(){for(var m=[],x=0;x<arguments.length;x++){var O=arguments[x];if(!N.isString(O))throw new TypeError("Arguments to path.join must be strings");O&&m.push(O)}var U=m.join("\\");return/^[\\\/]{2}[^\\\/]/.test(m[0])||(U=U.replace(/^[\\\/]{2,}/,"\\")),P.normalize(U)},P.relative=function(m,x){m=P.resolve(m),x=P.resolve(x);for(var O=m.toLowerCase(),U=x.toLowerCase(),ne=S(x.split("\\")),ce=S(O.split("\\")),fe=S(U.split("\\")),pe=Math.min(ce.length,fe.length),W=pe,X=0;X<pe;X++)if(ce[X]!==fe[X]){W=X;break}if(W==0)return x;var ee=[];for(X=W;X<ce.length;X++)ee.push("..");return(ee=ee.concat(ne.slice(W))).join("\\")},P._makeLong=function(m){if(!N.isString(m))return m;if(!m)return"";var x=P.resolve(m);return/^[a-zA-Z]\:\\/.test(x)?"\\\\?\\"+x:/^\\\\[^?.]/.test(x)?"\\\\?\\UNC\\"+x.substring(2):m},P.dirname=function(m){var x=h(m),O=x[0],U=x[1];return O||U?(U&&(U=U.substr(0,U.length-1)),O+U):"."},P.basename=function(m,x){var O=h(m)[2];return x&&O.substr(-1*x.length)===x&&(O=O.substr(0,O.length-x.length)),O},P.extname=function(m){return h(m)[3]},P.format=function(m){if(!N.isObject(m))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof m);var x=m.root||"";if(!N.isString(x))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof m.root);var O=m.dir,U=m.base||"";return O?O[O.length-1]===P.sep?O+U:O+P.sep+U:U},P.parse=function(m){if(!N.isString(m))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof m);var x=h(m);if(!x||x.length!==4)throw new TypeError("Invalid path '"+m+"'");return{root:x[0],dir:x[0]+x[1].slice(0,-1),base:x[2],ext:x[3],name:x[2].slice(0,x[2].length-x[3].length)}},P.sep="\\",P.delimiter=";";var k=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,T={};function A(m){return k.exec(m).slice(1)}T.resolve=function(){for(var m="",x=!1,O=arguments.length-1;O>=-1&&!x;O--){var U=O>=0?arguments[O]:f.cwd();if(!N.isString(U))throw new TypeError("Arguments to path.resolve must be strings");U&&(m=U+"/"+m,x=U[0]==="/")}return(x?"/":"")+(m=_(m.split("/"),!x).join("/"))||"."},T.normalize=function(m){var x=T.isAbsolute(m),O=m&&m[m.length-1]==="/";return(m=_(m.split("/"),!x).join("/"))||x||(m="."),m&&O&&(m+="/"),(x?"/":"")+m},T.isAbsolute=function(m){return m.charAt(0)==="/"},T.join=function(){for(var m="",x=0;x<arguments.length;x++){var O=arguments[x];if(!N.isString(O))throw new TypeError("Arguments to path.join must be strings");O&&(m+=m?"/"+O:O)}return T.normalize(m)},T.relative=function(m,x){m=T.resolve(m).substr(1),x=T.resolve(x).substr(1);for(var O=S(m.split("/")),U=S(x.split("/")),ne=Math.min(O.length,U.length),ce=ne,fe=0;fe<ne;fe++)if(O[fe]!==U[fe]){ce=fe;break}var pe=[];for(fe=ce;fe<O.length;fe++)pe.push("..");return(pe=pe.concat(U.slice(ce))).join("/")},T._makeLong=function(m){return m},T.dirname=function(m){var x=A(m),O=x[0],U=x[1];return O||U?(U&&(U=U.substr(0,U.length-1)),O+U):"."},T.basename=function(m,x){var O=A(m)[2];return x&&O.substr(-1*x.length)===x&&(O=O.substr(0,O.length-x.length)),O},T.extname=function(m){return A(m)[3]},T.format=function(m){if(!N.isObject(m))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof m);var x=m.root||"";if(!N.isString(x))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof m.root);return(m.dir?m.dir+T.sep:"")+(m.base||"")},T.parse=function(m){if(!N.isString(m))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof m);var x=A(m);if(!x||x.length!==4)throw new TypeError("Invalid path '"+m+"'");return x[1]=x[1]||"",x[2]=x[2]||"",x[3]=x[3]||"",{root:x[0],dir:x[0]+x[1].slice(0,-1),base:x[2],ext:x[3],name:x[2].slice(0,x[2].length-x[3].length)}},T.sep="/",T.delimiter=":",s.exports=v?P:T,s.exports.posix=T,s.exports.win32=P},155:s=>{var u,d,f=s.exports={};function v(){throw new Error("setTimeout has not been defined")}function N(){throw new Error("clearTimeout has not been defined")}function _(T){if(u===setTimeout)return setTimeout(T,0);if((u===v||!u)&&setTimeout)return u=setTimeout,setTimeout(T,0);try{return u(T,0)}catch{try{return u.call(null,T,0)}catch{return u.call(this,T,0)}}}(function(){try{u=typeof setTimeout=="function"?setTimeout:v}catch{u=v}try{d=typeof clearTimeout=="function"?clearTimeout:N}catch{d=N}})();var S,w=[],C=!1,P=-1;function h(){C&&S&&(C=!1,S.length?w=S.concat(w):P=-1,w.length&&p())}function p(){if(!C){var T=_(h);C=!0;for(var A=w.length;A;){for(S=w,w=[];++P<A;)S&&S[P].run();P=-1,A=w.length}S=null,C=!1,function(m){if(d===clearTimeout)return clearTimeout(m);if((d===N||!d)&&clearTimeout)return d=clearTimeout,clearTimeout(m);try{d(m)}catch{try{return d.call(null,m)}catch{return d.call(this,m)}}}(T)}}function y(T,A){this.fun=T,this.array=A}function k(){}f.nextTick=function(T){var A=new Array(arguments.length-1);if(arguments.length>1)for(var m=1;m<arguments.length;m++)A[m-1]=arguments[m];w.push(new y(T,A)),w.length!==1||C||_(p)},y.prototype.run=function(){this.fun.apply(null,this.array)},f.title="browser",f.browser=!0,f.env={},f.argv=[],f.version="",f.versions={},f.on=k,f.addListener=k,f.once=k,f.off=k,f.removeListener=k,f.removeAllListeners=k,f.emit=k,f.prependListener=k,f.prependOnceListener=k,f.listeners=function(T){return[]},f.binding=function(T){throw new Error("process.binding is not supported")},f.cwd=function(){return"/"},f.chdir=function(T){throw new Error("process.chdir is not supported")},f.umask=function(){return 0}},384:s=>{s.exports=function(u){return u&&typeof u=="object"&&typeof u.copy=="function"&&typeof u.fill=="function"&&typeof u.readUInt8=="function"}},539:(s,u,d)=>{var f=d(155),v=/%[sdj%]/g;u.format=function(I){if(!m(I)){for(var j=[],B=0;B<arguments.length;B++)j.push(S(arguments[B]));return j.join(" ")}B=1;for(var se=arguments,je=se.length,G=String(I).replace(v,function(ye){if(ye==="%%")return"%";if(B>=je)return ye;switch(ye){case"%s":return String(se[B++]);case"%d":return Number(se[B++]);case"%j":try{return JSON.stringify(se[B++])}catch{return"[Circular]"}default:return ye}}),ue=se[B];B<je;ue=se[++B])T(ue)||!U(ue)?G+=" "+ue:G+=" "+S(ue);return G},u.deprecate=function(I,j){if(x(d.g.process))return function(){return u.deprecate(I,j).apply(this,arguments)};if(f.noDeprecation===!0)return I;var B=!1;return function(){if(!B){if(f.throwDeprecation)throw new Error(j);f.traceDeprecation?console.trace(j):console.error(j),B=!0}return I.apply(this,arguments)}};var N,_={};function S(I,j){var B={seen:[],stylize:C};return arguments.length>=3&&(B.depth=arguments[2]),arguments.length>=4&&(B.colors=arguments[3]),k(j)?B.showHidden=j:j&&u._extend(B,j),x(B.showHidden)&&(B.showHidden=!1),x(B.depth)&&(B.depth=2),x(B.colors)&&(B.colors=!1),x(B.customInspect)&&(B.customInspect=!0),B.colors&&(B.stylize=w),P(B,I,B.depth)}function w(I,j){var B=S.styles[j];return B?"\x1B["+S.colors[B][0]+"m"+I+"\x1B["+S.colors[B][1]+"m":I}function C(I,j){return I}function P(I,j,B){if(I.customInspect&&j&&fe(j.inspect)&&j.inspect!==u.inspect&&(!j.constructor||j.constructor.prototype!==j)){var se=j.inspect(B,I);return m(se)||(se=P(I,se,B)),se}var je=function(_e,Pe){if(x(Pe))return _e.stylize("undefined","undefined");if(m(Pe)){var tt="'"+JSON.stringify(Pe).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return _e.stylize(tt,"string")}if(A(Pe))return _e.stylize(""+Pe,"number");if(k(Pe))return _e.stylize(""+Pe,"boolean");if(T(Pe))return _e.stylize("null","null")}(I,j);if(je)return je;var G=Object.keys(j),ue=function(_e){var Pe={};return _e.forEach(function(tt,st){Pe[tt]=!0}),Pe}(G);if(I.showHidden&&(G=Object.getOwnPropertyNames(j)),ce(j)&&(G.indexOf("message")>=0||G.indexOf("description")>=0))return h(j);if(G.length===0){if(fe(j)){var ye=j.name?": "+j.name:"";return I.stylize("[Function"+ye+"]","special")}if(O(j))return I.stylize(RegExp.prototype.toString.call(j),"regexp");if(ne(j))return I.stylize(Date.prototype.toString.call(j),"date");if(ce(j))return h(j)}var $e,Ge="",lt=!1,Jt=["{","}"];return y(j)&&(lt=!0,Jt=["[","]"]),fe(j)&&(Ge=" [Function"+(j.name?": "+j.name:"")+"]"),O(j)&&(Ge=" "+RegExp.prototype.toString.call(j)),ne(j)&&(Ge=" "+Date.prototype.toUTCString.call(j)),ce(j)&&(Ge=" "+h(j)),G.length!==0||lt&&j.length!=0?B<0?O(j)?I.stylize(RegExp.prototype.toString.call(j),"regexp"):I.stylize("[Object]","special"):(I.seen.push(j),$e=lt?function(_e,Pe,tt,st,St){for(var Zt=[],gt=0,nt=Pe.length;gt<nt;++gt)H(Pe,String(gt))?Zt.push(p(_e,Pe,tt,st,String(gt),!0)):Zt.push("");return St.forEach(function(Ft){Ft.match(/^\d+$/)||Zt.push(p(_e,Pe,tt,st,Ft,!0))}),Zt}(I,j,B,ue,G):G.map(function(_e){return p(I,j,B,ue,_e,lt)}),I.seen.pop(),function(_e,Pe,tt){return _e.reduce(function(st,St){return St.indexOf(`
`)>=0,st+St.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?tt[0]+(Pe===""?"":Pe+`
 `)+" "+_e.join(`,
  `)+" "+tt[1]:tt[0]+Pe+" "+_e.join(", ")+" "+tt[1]}($e,Ge,Jt)):Jt[0]+Ge+Jt[1]}function h(I){return"["+Error.prototype.toString.call(I)+"]"}function p(I,j,B,se,je,G){var ue,ye,$e;if(($e=Object.getOwnPropertyDescriptor(j,je)||{value:j[je]}).get?ye=$e.set?I.stylize("[Getter/Setter]","special"):I.stylize("[Getter]","special"):$e.set&&(ye=I.stylize("[Setter]","special")),H(se,je)||(ue="["+je+"]"),ye||(I.seen.indexOf($e.value)<0?(ye=T(B)?P(I,$e.value,null):P(I,$e.value,B-1)).indexOf(`
`)>-1&&(ye=G?ye.split(`
`).map(function(Ge){return"  "+Ge}).join(`
`).substr(2):`
`+ye.split(`
`).map(function(Ge){return"   "+Ge}).join(`
`)):ye=I.stylize("[Circular]","special")),x(ue)){if(G&&je.match(/^\d+$/))return ye;(ue=JSON.stringify(""+je)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(ue=ue.substr(1,ue.length-2),ue=I.stylize(ue,"name")):(ue=ue.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),ue=I.stylize(ue,"string"))}return ue+": "+ye}function y(I){return Array.isArray(I)}function k(I){return typeof I=="boolean"}function T(I){return I===null}function A(I){return typeof I=="number"}function m(I){return typeof I=="string"}function x(I){return I===void 0}function O(I){return U(I)&&pe(I)==="[object RegExp]"}function U(I){return typeof I=="object"&&I!==null}function ne(I){return U(I)&&pe(I)==="[object Date]"}function ce(I){return U(I)&&(pe(I)==="[object Error]"||I instanceof Error)}function fe(I){return typeof I=="function"}function pe(I){return Object.prototype.toString.call(I)}function W(I){return I<10?"0"+I.toString(10):I.toString(10)}u.debuglog=function(I){if(x(N)&&(N=f.env.NODE_DEBUG||""),I=I.toUpperCase(),!_[I])if(new RegExp("\\b"+I+"\\b","i").test(N)){var j=f.pid;_[I]=function(){var B=u.format.apply(u,arguments);console.error("%s %d: %s",I,j,B)}}else _[I]=function(){};return _[I]},u.inspect=S,S.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},S.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},u.isArray=y,u.isBoolean=k,u.isNull=T,u.isNullOrUndefined=function(I){return I==null},u.isNumber=A,u.isString=m,u.isSymbol=function(I){return typeof I=="symbol"},u.isUndefined=x,u.isRegExp=O,u.isObject=U,u.isDate=ne,u.isError=ce,u.isFunction=fe,u.isPrimitive=function(I){return I===null||typeof I=="boolean"||typeof I=="number"||typeof I=="string"||typeof I=="symbol"||I===void 0},u.isBuffer=d(384);var X=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function ee(){var I=new Date,j=[W(I.getHours()),W(I.getMinutes()),W(I.getSeconds())].join(":");return[I.getDate(),X[I.getMonth()],j].join(" ")}function H(I,j){return Object.prototype.hasOwnProperty.call(I,j)}u.log=function(){console.log("%s - %s",ee(),u.format.apply(u,arguments))},u.inherits=d(717),u._extend=function(I,j){if(!j||!U(j))return I;for(var B=Object.keys(j),se=B.length;se--;)I[B[se]]=j[B[se]];return I}}},r={};function o(s){var u=r[s];if(u!==void 0)return u.exports;var d=r[s]={exports:{}};return n[s].call(d.exports,d,d.exports,o),d.exports}o.n=s=>{var u=s&&s.__esModule?()=>s.default:()=>s;return o.d(u,{a:u}),u},o.d=(s,u)=>{for(var d in u)o.o(u,d)&&!o.o(s,d)&&Object.defineProperty(s,d,{enumerable:!0,get:u[d]})},o.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}(),o.o=(s,u)=>Object.prototype.hasOwnProperty.call(s,u),o.r=s=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})};var i={};return(()=>{o.r(i),o.d(i,{LSPluginUser:()=>As,setupPluginUserInstance:()=>kc});var s=o(520),u=(o(856),o(996)),d=o.n(u),f=function(){return f=Object.assign||function(a){for(var l,c=1,g=arguments.length;c<g;c++)for(var L in l=arguments[c])Object.prototype.hasOwnProperty.call(l,L)&&(a[L]=l[L]);return a},f.apply(this,arguments)};function v(a){return a.toLowerCase()}var N=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],_=/[^A-Z0-9]+/gi;function S(a,l,c){return l instanceof RegExp?a.replace(l,c):l.reduce(function(g,L){return g.replace(L,c)},a)}function w(a,l){return l===void 0&&(l={}),function(c,g){g===void 0&&(g={});for(var L=g.splitRegexp,M=L===void 0?N:L,R=g.stripRegexp,F=R===void 0?_:R,Q=g.transform,oe=Q===void 0?v:Q,le=g.delimiter,re=le===void 0?" ":le,ae=S(S(c,M,"$1\0$2"),F,"\0"),xe=0,de=ae.length;ae.charAt(xe)==="\0";)xe++;for(;ae.charAt(de-1)==="\0";)de--;return ae.slice(xe,de).split("\0").map(oe).join(re)}(a,f({delimiter:"."},l))}var C=o(729),P=o.n(C);function h(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}const p=navigator.platform.toLowerCase()==="win32"?s.win32:s.posix,y=function(a,l){return l===void 0&&(l={}),w(a,f({delimiter:"_"},l))};class k extends P(){constructor(l,c){super(),h(this,"_tag",void 0),h(this,"_opts",void 0),h(this,"_logs",[]),this._tag=l,this._opts=c}write(l,c,g){var L;c!=null&&c.length&&c[c.length-1]===!0&&(g=!0,c.pop());const M=c.reduce((F,Q)=>(Q&&Q instanceof Error?F+=`${Q.message} ${Q.stack}`:F+=Q.toString(),F),`[${this._tag}][${new Date().toLocaleTimeString()}] `);var R;this._logs.push([l,M]),(g||(L=this._opts)!==null&&L!==void 0&&L.console)&&((R=console)===null||R===void 0||R[l==="ERROR"?"error":"debug"](`${l}: ${M}`)),this.emit("change")}clear(){this._logs=[],this.emit("change")}info(...l){this.write("INFO",l)}error(...l){this.write("ERROR",l)}warn(...l){this.write("WARN",l)}setTag(l){this._tag=l}toJSON(){return this._logs}}function T(a,...l){try{const c=new URL(a);if(!c.origin)throw new Error(null);const g=p.join(a.substr(c.origin.length),...l);return c.origin+g}catch{return p.join(a,...l)}}function A(a,l){let c,g,L=!1;const M=F=>Q=>{a&&clearTimeout(a),F(Q),L=!0},R=new Promise((F,Q)=>{c=M(F),g=M(Q),a&&(a=setTimeout(()=>g(new Error(`[deferred timeout] ${l}`)),a))});return{created:Date.now(),setTag:F=>l=F,resolve:c,reject:g,promise:R,get settled(){return L}}}const m=new Map;window.__injectedUIEffects=m;var x=o(227),O=o.n(x);function U(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}const ne="application/x-postmate-v1+json";let ce=0;const fe={handshake:1,"handshake-reply":1,call:1,emit:1,reply:1,request:1},pe=(a,l)=>(typeof l!="string"||a.origin===l)&&!!a.data&&(typeof a.data!="object"||"postmate"in a.data)&&a.data.type===ne&&!!fe[a.data.postmate];class W{constructor(l){U(this,"parent",void 0),U(this,"frame",void 0),U(this,"child",void 0),U(this,"events",{}),U(this,"childOrigin",void 0),U(this,"listener",void 0),this.parent=l.parent,this.frame=l.frame,this.child=l.child,this.childOrigin=l.childOrigin,this.listener=c=>{if(!pe(c,this.childOrigin))return!1;const{data:g,name:L}=((c||{}).data||{}).value||{};c.data.postmate==="emit"&&L in this.events&&this.events[L].forEach(M=>{M.call(this,g)})},this.parent.addEventListener("message",this.listener,!1)}get(l,...c){return new Promise((g,L)=>{const M=++ce,R=F=>{F.data.uid===M&&F.data.postmate==="reply"&&(this.parent.removeEventListener("message",R,!1),F.data.error?L(F.data.error):g(F.data.value))};this.parent.addEventListener("message",R,!1),this.child.postMessage({postmate:"request",type:ne,property:l,args:c,uid:M},this.childOrigin)})}call(l,c){this.child.postMessage({postmate:"call",type:ne,property:l,data:c},this.childOrigin)}on(l,c){this.events[l]||(this.events[l]=[]),this.events[l].push(c)}destroy(){window.removeEventListener("message",this.listener,!1),this.frame.parentNode.removeChild(this.frame)}}class X{constructor(l){U(this,"model",void 0),U(this,"parent",void 0),U(this,"parentOrigin",void 0),U(this,"child",void 0),this.model=l.model,this.parent=l.parent,this.parentOrigin=l.parentOrigin,this.child=l.child,this.child.addEventListener("message",c=>{if(!pe(c,this.parentOrigin))return;const{property:g,uid:L,data:M,args:R}=c.data;c.data.postmate!=="call"?((F,Q,oe)=>{const le=typeof F[Q]=="function"?F[Q].apply(null,oe):F[Q];return Promise.resolve(le)})(this.model,g,R).then(F=>{c.source.postMessage({property:g,postmate:"reply",type:ne,uid:L,value:F},c.origin)}).catch(F=>{c.source.postMessage({property:g,postmate:"reply",type:ne,uid:L,error:F},c.origin)}):g in this.model&&typeof this.model[g]=="function"&&this.model[g](M)})}emit(l,c){this.parent.postMessage({postmate:"emit",type:ne,value:{name:l,data:c}},this.parentOrigin)}}class ee{constructor(l){U(this,"container",void 0),U(this,"parent",void 0),U(this,"frame",void 0),U(this,"child",void 0),U(this,"childOrigin",void 0),U(this,"url",void 0),U(this,"model",void 0),this.container=l.container,this.url=l.url,this.parent=window,this.frame=document.createElement("iframe"),l.id&&(this.frame.id=l.id),l.name&&(this.frame.name=l.name),this.frame.classList.add.apply(this.frame.classList,l.classListArray||[]),this.container.appendChild(this.frame),this.child=this.frame.contentWindow,this.model=l.model||{}}sendHandshake(l){const c=(M=>{const R=document.createElement("a");R.href=M;const F=R.protocol.length>4?R.protocol:window.location.protocol,Q=R.host.length?R.port==="80"||R.port==="443"?R.hostname:R.host:window.location.host;return R.origin||`${F}//${Q}`})(l=l||this.url);let g,L=0;return new Promise((M,R)=>{const F=oe=>!!pe(oe,c)&&(oe.data.postmate==="handshake-reply"?(clearInterval(g),this.parent.removeEventListener("message",F,!1),this.childOrigin=oe.origin,M(new W(this))):R("Failed handshake"));this.parent.addEventListener("message",F,!1);const Q=()=>{L++,this.child.postMessage({postmate:"handshake",type:ne,model:this.model},c),L===5&&clearInterval(g)};this.frame.addEventListener("load",()=>{Q(),g=setInterval(Q,500)}),this.frame.src=l})}destroy(){this.frame.parentNode.removeChild(this.frame)}}U(ee,"debug",!1),U(ee,"Model",void 0);class H{constructor(l){U(this,"child",void 0),U(this,"model",void 0),U(this,"parent",void 0),U(this,"parentOrigin",void 0),this.child=window,this.model=l,this.parent=this.child.parent}sendHandshakeReply(){return new Promise((l,c)=>{const g=L=>{if(L.data.postmate){if(L.data.postmate==="handshake"){this.child.removeEventListener("message",g,!1),L.source.postMessage({postmate:"handshake-reply",type:ne},L.origin),this.parentOrigin=L.origin;const M=L.data.model;return M&&Object.keys(M).forEach(R=>{this.model[R]=M[R]}),l(new X(this))}return c("Handshake Reply Failed")}};this.child.addEventListener("message",g,!1)})}}function I(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}const{importHTML:j,createSandboxContainer:B}=window.QSandbox||{};function se(a,l){return a.startsWith("http")?fetch(a,l):(a=a.replace("file://",""),new Promise(async(c,g)=>{try{const L=await window.apis.doAction(["readFile",a]);c({text:()=>L})}catch(L){console.error(L),g(L)}}))}class je extends P(){constructor(l){super(),I(this,"_pluginLocal",void 0),I(this,"_frame",void 0),I(this,"_root",void 0),I(this,"_loaded",!1),I(this,"_unmountFns",[]),this._pluginLocal=l,l._dispose(()=>{this._unmount()})}async load(){const{name:l,entry:c}=this._pluginLocal.options;if(this.loaded||!c)return;const{template:g,execScripts:L}=await j(c,{fetch:se});this._mount(g,document.body);const M=B(l,{elementGetter:()=>{var F;return(F=this._root)===null||F===void 0?void 0:F.firstChild}}).instance.proxy;M.__shadow_mode__=!0,M.LSPluginLocal=this._pluginLocal,M.LSPluginShadow=this,M.LSPluginUser=M.logseq=new As(this._pluginLocal.toJSON(),this._pluginLocal.caller);const R=await L(M,!0);this._unmountFns.push(R.unmount),this._loaded=!0}_mount(l,c){const g=this._frame=document.createElement("div");g.classList.add("lsp-shadow-sandbox"),g.id=this._pluginLocal.id,this._root=g.attachShadow({mode:"open"}),this._root.innerHTML=`<div>${l}</div>`,c.appendChild(g),this.emit("mounted")}_unmount(){for(const l of this._unmountFns)l&&l.call(null)}destroy(){var l,c;(l=this.frame)===null||l===void 0||(c=l.parentNode)===null||c===void 0||c.removeChild(this.frame)}get loaded(){return this._loaded}get document(){var l;return(l=this._root)===null||l===void 0?void 0:l.firstChild}get frame(){return this._frame}}function G(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}const ue=O()("LSPlugin:caller"),ye="#await#response#",$e="#lspmsg#",Ge="#lspmsg#error#",lt=a=>`#lspmsg#${a}`;class Jt extends P(){constructor(l){super(),G(this,"_pluginLocal",void 0),G(this,"_connected",!1),G(this,"_parent",void 0),G(this,"_child",void 0),G(this,"_shadow",void 0),G(this,"_status",void 0),G(this,"_userModel",{}),G(this,"_call",void 0),G(this,"_callUserModel",void 0),G(this,"_debugTag",""),this._pluginLocal=l,l&&(this._debugTag=l.debugTag)}async connectToChild(){if(this._connected)return;const{shadow:l}=this._pluginLocal;l?await this._setupShadowSandbox():await this._setupIframeSandbox()}async connectToParent(l={}){if(this._connected)return;const c=this,g=this._pluginLocal!=null;let L=0;const M=new Map,R=A(6e4),F=this._extendUserModel({"#lspmsg#ready#":async le=>{F[lt(le?.pid)]=({type:re,payload:ae})=>{ue(`[host (_call) -> *user] ${this._debugTag}`,re,ae),c.emit(re,ae)},await R.resolve()},"#lspmsg#beforeunload#":async le=>{const re=A(1e4);c.emit("beforeunload",Object.assign({actor:re},le)),await re.promise},"#lspmsg#settings#":async({type:le,payload:re})=>{c.emit("settings:changed",re)},[$e]:async({ns:le,type:re,payload:ae})=>{ue(`[host (async) -> *user] ${this._debugTag} ns=${le} type=${re}`,ae),le&&le.startsWith("hook")?c.emit(`${le}:${re}`,ae):c.emit(re,ae)},"#lspmsg#reply#":({_sync:le,result:re})=>{if(ue(`[sync host -> *user] #${le}`,re),M.has(le)){const ae=M.get(le);ae&&(re!=null&&re.hasOwnProperty(Ge)?ae.reject(re[Ge]):ae.resolve(re),M.delete(le))}},...l});var Q;if(g)return await R.promise,JSON.parse(JSON.stringify((Q=this._pluginLocal)===null||Q===void 0?void 0:Q.toJSON()));const oe=new H(F).sendHandshakeReply();return this._status="pending",await oe.then(le=>{this._child=le,this._connected=!0,this._call=async(re,ae={},xe)=>{if(xe){const de=++L;M.set(de,xe),ae._sync=de,xe.setTag(`async call #${de}`),ue(`async call #${de}`)}return le.emit(lt(F.baseInfo.id),{type:re,payload:ae}),xe?.promise},this._callUserModel=async(re,ae)=>{try{F[re](ae)}catch{ue(`[model method] #${re} not existed`)}},setInterval(()=>{if(M.size>100)for(const[re,ae]of M)ae.settled&&M.delete(re)},18e5)}).finally(()=>{this._status=void 0}),await R.promise,F.baseInfo}async call(l,c={}){var g;return(g=this._call)===null||g===void 0?void 0:g.call(this,l,c)}async callAsync(l,c={}){var g;const L=A(1e4);return(g=this._call)===null||g===void 0?void 0:g.call(this,l,c,L)}async callUserModel(l,...c){var g;return(g=this._callUserModel)===null||g===void 0?void 0:g.apply(this,[l,...c])}async callUserModelAsync(l,...c){var g;return l=`${ye}${l}`,(g=this._callUserModel)===null||g===void 0?void 0:g.apply(this,[l,...c])}async _setupIframeSandbox(){const l=this._pluginLocal,c=l.id,g=`${c}_lsp_main`,L=new URL(l.options.entry);L.searchParams.set("__v__",l.options.version);const M=document.querySelector(`#${g}`);M&&M.parentElement.removeChild(M);const R=document.createElement("div");R.classList.add("lsp-iframe-sandbox-container"),R.id=g,R.dataset.pid=c;try{var F;const re=(F=await this._pluginLocal._loadLayoutsData())===null||F===void 0?void 0:F.$$0;if(re){R.dataset.inited_layout="true";let{width:ae,height:xe,left:de,top:Ne,vw:Ae,vh:Xe}=re;de=Math.max(de,0),de=typeof Ae=="number"?`${Math.min(100*de/Ae,99)}%`:`${de}px`,Ne=Math.max(Ne,45),Ne=typeof Xe=="number"?`${Math.min(100*Ne/Xe,99)}%`:`${Ne}px`,Object.assign(R.style,{width:ae+"px",height:xe+"px",left:de,top:Ne})}}catch(re){console.error("[Restore Layout Error]",re)}document.body.appendChild(R);const Q=new ee({id:c+"_iframe",container:R,url:L.href,classListArray:["lsp-iframe-sandbox"],model:{baseInfo:JSON.parse(JSON.stringify(l.toJSON()))}});let oe,le=Q.sendHandshake();return this._status="pending",new Promise((re,ae)=>{oe=setTimeout(()=>{ae(new Error("handshake Timeout")),Q.destroy()},4e3),le.then(xe=>{this._parent=xe,this._connected=!0,this.emit("connected"),xe.on(lt(l.id),({type:de,payload:Ne})=>{var Ae,Xe;ue("[user -> *host] ",de,Ne),(Ae=this._pluginLocal)===null||Ae===void 0||Ae.emit(de,Ne||{}),(Xe=this._pluginLocal)===null||Xe===void 0||Xe.caller.emit(de,Ne||{})}),this._call=async(...de)=>{await xe.call(lt(l.id),{type:de[0],payload:Object.assign(de[1]||{},{$$pid:l.id})})},this._callUserModel=async(de,...Ne)=>{if(de.startsWith(ye))return await xe.get(de.replace(ye,""),...Ne);xe.call(de,Ne?.[0])},re(null)}).catch(xe=>{ae(xe)}).finally(()=>{clearTimeout(oe)})}).catch(re=>{throw ue("[iframe sandbox] error",re),re}).finally(()=>{this._status=void 0})}async _setupShadowSandbox(){const l=this._pluginLocal,c=this._shadow=new je(l);try{this._status="pending",await c.load(),this._connected=!0,this.emit("connected"),this._call=async(g,L={},M)=>{var R;return M&&(L.actor=M),(R=this._pluginLocal)===null||R===void 0||R.emit(g,Object.assign(L,{$$pid:l.id})),M?.promise},this._callUserModel=async(...g)=>{var L;let M=g[0];(L=M)!==null&&L!==void 0&&L.startsWith(ye)&&(M=M.replace(ye,""));const R=g[1]||{},F=this._userModel[M];typeof F=="function"&&await F.call(null,R)}}catch(g){throw ue("[shadow sandbox] error",g),g}finally{this._status=void 0}}_extendUserModel(l){return Object.assign(this._userModel,l)}_getSandboxIframeContainer(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxShadowContainer(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxIframeRoot(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame}_getSandboxShadowRoot(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame}set debugTag(l){this._debugTag=l}async destroy(){var l;let c=null;this._parent&&(c=this._getSandboxIframeContainer(),await this._parent.destroy()),this._shadow&&(c=this._getSandboxShadowContainer(),this._shadow.destroy()),(l=c)===null||l===void 0||l.parentNode.removeChild(c)}}function _e(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}class Pe{constructor(l,c){_e(this,"ctx",void 0),_e(this,"opts",void 0),this.ctx=l,this.opts=c}get ctxId(){return this.ctx.baseInfo.id}setItem(l,c){var g;return this.ctx.caller.callAsync("api:call",{method:"write-plugin-storage-file",args:[this.ctxId,l,c,(g=this.opts)===null||g===void 0?void 0:g.assets]})}getItem(l){var c;return this.ctx.caller.callAsync("api:call",{method:"read-plugin-storage-file",args:[this.ctxId,l,(c=this.opts)===null||c===void 0?void 0:c.assets]})}removeItem(l){var c;return this.ctx.caller.call("api:call",{method:"unlink-plugin-storage-file",args:[this.ctxId,l,(c=this.opts)===null||c===void 0?void 0:c.assets]})}allKeys(){var l;return this.ctx.caller.callAsync("api:call",{method:"list-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}clear(){var l;return this.ctx.caller.call("api:call",{method:"clear-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}hasItem(l){var c;return this.ctx.caller.callAsync("api:call",{method:"exist-plugin-storage-file",args:[this.ctxId,l,(c=this.opts)===null||c===void 0?void 0:c.assets]})}}class tt{constructor(l){var c,g,L;L=void 0,(g="ctx")in(c=this)?Object.defineProperty(c,g,{value:L,enumerable:!0,configurable:!0,writable:!0}):c[g]=L,this.ctx=l}get React(){return this.ensureHostScope().React}get ReactDOM(){return this.ensureHostScope().ReactDOM}get pluginLocal(){return this.ensureHostScope().LSPluginCore.ensurePlugin(this.ctx.baseInfo.id)}invokeExperMethod(l,...c){var g,L;const M=this.ensureHostScope();return l=(g=y(l))===null||g===void 0?void 0:g.toLowerCase(),(L=M.logseq.api["exper_"+l])===null||L===void 0?void 0:L.apply(M,c)}async loadScripts(...l){(l=l.map(c=>c!=null&&c.startsWith("http")?c:this.ctx.resolveResourceFullUrl(c))).unshift(this.ctx.baseInfo.id),await this.invokeExperMethod("loadScripts",...l)}registerFencedCodeRenderer(l,c){return this.ensureHostScope().logseq.api.exper_register_fenced_code_renderer(this.ctx.baseInfo.id,l,c)}registerExtensionsEnhancer(l,c){const g=this.ensureHostScope();return l==="katex"&&g.katex&&c(g.katex).catch(console.error),g.logseq.api.exper_register_extensions_enhancer(this.ctx.baseInfo.id,l,c)}ensureHostScope(){if(window===top)throw new Error("Can not access host scope!");return top}}function st(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}const St=a=>`task_callback_${a}`;class Zt{constructor(l,c,g={}){st(this,"_client",void 0),st(this,"_requestId",void 0),st(this,"_requestOptions",void 0),st(this,"_promise",void 0),st(this,"_aborted",!1),this._client=l,this._requestId=c,this._requestOptions=g,this._promise=new Promise((F,Q)=>{if(!this._requestId)return Q(null);this._client.once(St(this._requestId),oe=>{oe&&oe instanceof Error?Q(oe):F(oe)})});const{success:L,fail:M,final:R}=this._requestOptions;this._promise.then(F=>{L?.(F)}).catch(F=>{M?.(F)}).finally(()=>{R?.()})}abort(){this._requestOptions.abortable&&!this._aborted&&(this._client.ctx._execCallableAPI("http_request_abort",this._requestId),this._aborted=!0)}get promise(){return this._promise}get client(){return this._client}get requestId(){return this._requestId}}class gt extends C.EventEmitter{constructor(l){super(),st(this,"_ctx",void 0),this._ctx=l,this.ctx.caller.on("#lsp#request#callback",c=>{const g=c?.requestId;g&&this.emit(St(g),c?.payload)})}static createRequestTask(l,c,g){return new Zt(l,c,g)}async _request(l){const c=this.ctx.baseInfo.id,{success:g,fail:L,final:M,...R}=l,F=this.ctx.Experiments.invokeExperMethod("request",c,R),Q=gt.createRequestTask(this.ctx.Request,F,l);return R.abortable?Q:Q.promise}get ctx(){return this._ctx}}const nt=Array.isArray,Ft=typeof Zi=="object"&&Zi&&Zi.Object===Object&&Zi;var Mo=typeof self=="object"&&self&&self.Object===Object&&self;const Ct=Ft||Mo||Function("return this")(),_t=Ct.Symbol;var gr=Object.prototype,yr=gr.hasOwnProperty,jo=gr.toString,en=_t?_t.toStringTag:void 0;const vr=function(a){var l=yr.call(a,en),c=a[en];try{a[en]=void 0;var g=!0}catch{}var L=jo.call(a);return g&&(l?a[en]=c:delete a[en]),L};var Vr=Object.prototype.toString;const V=function(a){return Vr.call(a)};var J=_t?_t.toStringTag:void 0;const ie=function(a){return a==null?a===void 0?"[object Undefined]":"[object Null]":J&&J in Object(a)?vr(a):V(a)},me=function(a){var l=typeof a;return a!=null&&(l=="object"||l=="function")},Ue=function(a){if(!me(a))return!1;var l=ie(a);return l=="[object Function]"||l=="[object GeneratorFunction]"||l=="[object AsyncFunction]"||l=="[object Proxy]"},Lt=Ct["__core-js_shared__"];var Bt,hn=(Bt=/[^.]+$/.exec(Lt&&Lt.keys&&Lt.keys.IE_PROTO||""))?"Symbol(src)_1."+Bt:"";const wr=function(a){return!!hn&&hn in a};var kr=Function.prototype.toString;const tn=function(a){if(a!=null){try{return kr.call(a)}catch{}try{return a+""}catch{}}return""};var Uo=/^\[object .+?Constructor\]$/,Ot=Function.prototype,Kr=Object.prototype,Dn=Ot.toString,Do=Kr.hasOwnProperty,xr=RegExp("^"+Dn.call(Do).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const Qr=function(a){return!(!me(a)||wr(a))&&(Ue(a)?xr:Uo).test(tn(a))},Sr=function(a,l){return a?.[l]},Mt=function(a,l){var c=Sr(a,l);return Qr(c)?c:void 0},bt=function(){try{var a=Mt(Object,"defineProperty");return a({},"",{}),a}catch{}}(),mn=function(a,l,c){l=="__proto__"&&bt?bt(a,l,{configurable:!0,enumerable:!0,value:c,writable:!0}):a[l]=c},Rn=function(a){return function(l,c,g){for(var L=-1,M=Object(l),R=g(l),F=R.length;F--;){var Q=R[a?F:++L];if(c(M[Q],Q,M)===!1)break}return l}}(),Ht=function(a,l){for(var c=-1,g=Array(a);++c<a;)g[c]=l(c);return g},gn=function(a){return a!=null&&typeof a=="object"},Gr=function(a){return gn(a)&&ie(a)=="[object Arguments]"};var Yr=Object.prototype,Ro=Yr.hasOwnProperty,Xr=Yr.propertyIsEnumerable;const yt=Gr(function(){return arguments}())?Gr:function(a){return gn(a)&&Ro.call(a,"callee")&&!Xr.call(a,"callee")},$=function(){return!1};var D=t&&!t.nodeType&&t,he=D&&!0&&e&&!e.nodeType&&e,ge=he&&he.exports===D?Ct.Buffer:void 0;const we=(ge?ge.isBuffer:void 0)||$;var Ce=/^(?:0|[1-9]\d*)$/;const De=function(a,l){var c=typeof a;return!!(l=l??9007199254740991)&&(c=="number"||c!="symbol"&&Ce.test(a))&&a>-1&&a%1==0&&a<l},Ke=function(a){return typeof a=="number"&&a>-1&&a%1==0&&a<=9007199254740991};var ke={};ke["[object Float32Array]"]=ke["[object Float64Array]"]=ke["[object Int8Array]"]=ke["[object Int16Array]"]=ke["[object Int32Array]"]=ke["[object Uint8Array]"]=ke["[object Uint8ClampedArray]"]=ke["[object Uint16Array]"]=ke["[object Uint32Array]"]=!0,ke["[object Arguments]"]=ke["[object Array]"]=ke["[object ArrayBuffer]"]=ke["[object Boolean]"]=ke["[object DataView]"]=ke["[object Date]"]=ke["[object Error]"]=ke["[object Function]"]=ke["[object Map]"]=ke["[object Number]"]=ke["[object Object]"]=ke["[object RegExp]"]=ke["[object Set]"]=ke["[object String]"]=ke["[object WeakMap]"]=!1;const Te=function(a){return gn(a)&&Ke(a.length)&&!!ke[ie(a)]},zn=function(a){return function(l){return a(l)}};var qe=t&&!t.nodeType&&t,$n=qe&&!0&&e&&!e.nodeType&&e,Re=$n&&$n.exports===qe&&Ft.process,Fn=function(){try{var a=$n&&$n.require&&$n.require("util").types;return a||Re&&Re.binding&&Re.binding("util")}catch{}}(),Jr=Fn&&Fn.isTypedArray;const zo=Jr?zn(Jr):Te;var $o=Object.prototype.hasOwnProperty;const Ri=function(a,l){var c=nt(a),g=!c&&yt(a),L=!c&&!g&&we(a),M=!c&&!g&&!L&&zo(a),R=c||g||L||M,F=R?Ht(a.length,String):[],Q=F.length;for(var oe in a)!l&&!$o.call(a,oe)||R&&(oe=="length"||L&&(oe=="offset"||oe=="parent")||M&&(oe=="buffer"||oe=="byteLength"||oe=="byteOffset")||De(oe,Q))||F.push(oe);return F};var Bn=Object.prototype;const _n=function(a){var l=a&&a.constructor;return a===(typeof l=="function"&&l.prototype||Bn)},Fo=function(a,l){return function(c){return a(l(c))}}(Object.keys,Object);var Bo=Object.prototype.hasOwnProperty;const Hn=function(a){if(!_n(a))return Fo(a);var l=[];for(var c in Object(a))Bo.call(a,c)&&c!="constructor"&&l.push(c);return l},Zr=function(a){return a!=null&&Ke(a.length)&&!Ue(a)},Wn=function(a){return Zr(a)?Ri(a):Hn(a)},zi=function(a,l){return a&&Rn(a,l,Wn)},Ho=function(){this.__data__=[],this.size=0},qn=function(a,l){return a===l||a!=a&&l!=l},nn=function(a,l){for(var c=a.length;c--;)if(qn(a[c][0],l))return c;return-1};var Vn=Array.prototype.splice;const $i=function(a){var l=this.__data__,c=nn(l,a);return!(c<0)&&(c==l.length-1?l.pop():Vn.call(l,c,1),--this.size,!0)},Fi=function(a){var l=this.__data__,c=nn(l,a);return c<0?void 0:l[c][1]},Bi=function(a){return nn(this.__data__,a)>-1},Wo=function(a,l){var c=this.__data__,g=nn(c,a);return g<0?(++this.size,c.push([a,l])):c[g][1]=l,this};function bn(a){var l=-1,c=a==null?0:a.length;for(this.clear();++l<c;){var g=a[l];this.set(g[0],g[1])}}bn.prototype.clear=Ho,bn.prototype.delete=$i,bn.prototype.get=Fi,bn.prototype.has=Bi,bn.prototype.set=Wo;const Kn=bn,qo=function(){this.__data__=new Kn,this.size=0},yn=function(a){var l=this.__data__,c=l.delete(a);return this.size=l.size,c},eo=function(a){return this.__data__.get(a)},Hi=function(a){return this.__data__.has(a)},Cr=Mt(Ct,"Map"),_r=Mt(Object,"create"),Qn=function(){this.__data__=_r?_r(null):{},this.size=0},ws=function(a){var l=this.has(a)&&delete this.__data__[a];return this.size-=l?1:0,l};var Wi=Object.prototype.hasOwnProperty;const Vo=function(a){var l=this.__data__;if(_r){var c=l[a];return c==="__lodash_hash_undefined__"?void 0:c}return Wi.call(l,a)?l[a]:void 0};var qi=Object.prototype.hasOwnProperty;const Vi=function(a){var l=this.__data__;return _r?l[a]!==void 0:qi.call(l,a)},ks=function(a,l){var c=this.__data__;return this.size+=this.has(a)?0:1,c[a]=_r&&l===void 0?"__lodash_hash_undefined__":l,this};function rn(a){var l=-1,c=a==null?0:a.length;for(this.clear();++l<c;){var g=a[l];this.set(g[0],g[1])}}rn.prototype.clear=Qn,rn.prototype.delete=ws,rn.prototype.get=Vo,rn.prototype.has=Vi,rn.prototype.set=ks;const to=rn,xs=function(){this.size=0,this.__data__={hash:new to,map:new(Cr||Kn),string:new to}},on=function(a){var l=typeof a;return l=="string"||l=="number"||l=="symbol"||l=="boolean"?a!=="__proto__":a===null},br=function(a,l){var c=a.__data__;return on(l)?c[typeof l=="string"?"string":"hash"]:c.map},Ki=function(a){var l=br(this,a).delete(a);return this.size-=l?1:0,l},Qi=function(a){return br(this,a).get(a)},Ss=function(a){return br(this,a).has(a)},Er=function(a,l){var c=br(this,a),g=c.size;return c.set(a,l),this.size+=c.size==g?0:1,this};function dt(a){var l=-1,c=a==null?0:a.length;for(this.clear();++l<c;){var g=a[l];this.set(g[0],g[1])}}dt.prototype.clear=xs,dt.prototype.delete=Ki,dt.prototype.get=Qi,dt.prototype.has=Ss,dt.prototype.set=Er;const Nr=dt,Gi=function(a,l){var c=this.__data__;if(c instanceof Kn){var g=c.__data__;if(!Cr||g.length<199)return g.push([a,l]),this.size=++c.size,this;c=this.__data__=new Nr(g)}return c.set(a,l),this.size=c.size,this};function En(a){var l=this.__data__=new Kn(a);this.size=l.size}En.prototype.clear=qo,En.prototype.delete=yn,En.prototype.get=eo,En.prototype.has=Hi,En.prototype.set=Gi;const Ir=En,Cs=function(a){return this.__data__.set(a,"__lodash_hash_undefined__"),this},b=function(a){return this.__data__.has(a)};function Y(a){var l=-1,c=a==null?0:a.length;for(this.__data__=new Nr;++l<c;)this.add(a[l])}Y.prototype.add=Y.prototype.push=Cs,Y.prototype.has=b;const K=Y,Ee=function(a,l){for(var c=-1,g=a==null?0:a.length;++c<g;)if(l(a[c],c,a))return!0;return!1},Fe=function(a,l){return a.has(l)},rt=function(a,l,c,g,L,M){var R=1&c,F=a.length,Q=l.length;if(F!=Q&&!(R&&Q>F))return!1;var oe=M.get(a),le=M.get(l);if(oe&&le)return oe==l&&le==a;var re=-1,ae=!0,xe=2&c?new K:void 0;for(M.set(a,l),M.set(l,a);++re<F;){var de=a[re],Ne=l[re];if(g)var Ae=R?g(Ne,de,re,l,a,M):g(de,Ne,re,a,l,M);if(Ae!==void 0){if(Ae)continue;ae=!1;break}if(xe){if(!Ee(l,function(Xe,Wt){if(!Fe(xe,Wt)&&(de===Xe||L(de,Xe,c,g,M)))return xe.push(Wt)})){ae=!1;break}}else if(de!==Ne&&!L(de,Ne,c,g,M)){ae=!1;break}}return M.delete(a),M.delete(l),ae},Ye=Ct.Uint8Array,Pr=function(a){var l=-1,c=Array(a.size);return a.forEach(function(g,L){c[++l]=[L,g]}),c},vn=function(a){var l=-1,c=Array(a.size);return a.forEach(function(g){c[++l]=g}),c};var Et=_t?_t.prototype:void 0,Ko=Et?Et.valueOf:void 0;const Yu=function(a,l,c,g,L,M,R){switch(c){case"[object DataView]":if(a.byteLength!=l.byteLength||a.byteOffset!=l.byteOffset)return!1;a=a.buffer,l=l.buffer;case"[object ArrayBuffer]":return!(a.byteLength!=l.byteLength||!M(new Ye(a),new Ye(l)));case"[object Boolean]":case"[object Date]":case"[object Number]":return qn(+a,+l);case"[object Error]":return a.name==l.name&&a.message==l.message;case"[object RegExp]":case"[object String]":return a==l+"";case"[object Map]":var F=Pr;case"[object Set]":var Q=1&g;if(F||(F=vn),a.size!=l.size&&!Q)return!1;var oe=R.get(a);if(oe)return oe==l;g|=2,R.set(a,l);var le=rt(F(a),F(l),g,L,M,R);return R.delete(a),le;case"[object Symbol]":if(Ko)return Ko.call(a)==Ko.call(l)}return!1},Xp=function(a,l){for(var c=-1,g=l.length,L=a.length;++c<g;)a[L+c]=l[c];return a},Jp=function(a,l,c){var g=l(a);return nt(a)?g:Xp(g,c(a))},Zp=function(a,l){for(var c=-1,g=a==null?0:a.length,L=0,M=[];++c<g;){var R=a[c];l(R,c,a)&&(M[L++]=R)}return M},eh=function(){return[]};var th=Object.prototype.propertyIsEnumerable,Xu=Object.getOwnPropertySymbols;const nh=Xu?function(a){return a==null?[]:(a=Object(a),Zp(Xu(a),function(l){return th.call(a,l)}))}:eh,Ju=function(a){return Jp(a,Wn,nh)};var rh=Object.prototype.hasOwnProperty;const oh=function(a,l,c,g,L,M){var R=1&c,F=Ju(a),Q=F.length;if(Q!=Ju(l).length&&!R)return!1;for(var oe=Q;oe--;){var le=F[oe];if(!(R?le in l:rh.call(l,le)))return!1}var re=M.get(a),ae=M.get(l);if(re&&ae)return re==l&&ae==a;var xe=!0;M.set(a,l),M.set(l,a);for(var de=R;++oe<Q;){var Ne=a[le=F[oe]],Ae=l[le];if(g)var Xe=R?g(Ae,Ne,le,l,a,M):g(Ne,Ae,le,a,l,M);if(!(Xe===void 0?Ne===Ae||L(Ne,Ae,c,g,M):Xe)){xe=!1;break}de||(de=le=="constructor")}if(xe&&!de){var Wt=a.constructor,Ar=l.constructor;Wt==Ar||!("constructor"in a)||!("constructor"in l)||typeof Wt=="function"&&Wt instanceof Wt&&typeof Ar=="function"&&Ar instanceof Ar||(xe=!1)}return M.delete(a),M.delete(l),xe},_s=Mt(Ct,"DataView"),bs=Mt(Ct,"Promise"),Es=Mt(Ct,"Set"),Ns=Mt(Ct,"WeakMap");var Zu="[object Map]",ec="[object Promise]",tc="[object Set]",nc="[object WeakMap]",rc="[object DataView]",ih=tn(_s),lh=tn(Cr),sh=tn(bs),ah=tn(Es),uh=tn(Ns),Tr=ie;(_s&&Tr(new _s(new ArrayBuffer(1)))!=rc||Cr&&Tr(new Cr)!=Zu||bs&&Tr(bs.resolve())!=ec||Es&&Tr(new Es)!=tc||Ns&&Tr(new Ns)!=nc)&&(Tr=function(a){var l=ie(a),c=l=="[object Object]"?a.constructor:void 0,g=c?tn(c):"";if(g)switch(g){case ih:return rc;case lh:return Zu;case sh:return ec;case ah:return tc;case uh:return nc}return l});const oc=Tr;var ic="[object Arguments]",lc="[object Array]",Yi="[object Object]",sc=Object.prototype.hasOwnProperty;const ch=function(a,l,c,g,L,M){var R=nt(a),F=nt(l),Q=R?lc:oc(a),oe=F?lc:oc(l),le=(Q=Q==ic?Yi:Q)==Yi,re=(oe=oe==ic?Yi:oe)==Yi,ae=Q==oe;if(ae&&we(a)){if(!we(l))return!1;R=!0,le=!1}if(ae&&!le)return M||(M=new Ir),R||zo(a)?rt(a,l,c,g,L,M):Yu(a,l,Q,c,g,L,M);if(!(1&c)){var xe=le&&sc.call(a,"__wrapped__"),de=re&&sc.call(l,"__wrapped__");if(xe||de){var Ne=xe?a.value():a,Ae=de?l.value():l;return M||(M=new Ir),L(Ne,Ae,c,g,M)}}return!!ae&&(M||(M=new Ir),oh(a,l,c,g,L,M))},ac=function a(l,c,g,L,M){return l===c||(l==null||c==null||!gn(l)&&!gn(c)?l!=l&&c!=c:ch(l,c,g,L,a,M))},dh=function(a,l,c,g){var L=c.length,M=L,R=!g;if(a==null)return!M;for(a=Object(a);L--;){var F=c[L];if(R&&F[2]?F[1]!==a[F[0]]:!(F[0]in a))return!1}for(;++L<M;){var Q=(F=c[L])[0],oe=a[Q],le=F[1];if(R&&F[2]){if(oe===void 0&&!(Q in a))return!1}else{var re=new Ir;if(g)var ae=g(oe,le,Q,a,l,re);if(!(ae===void 0?ac(le,oe,3,g,re):ae))return!1}}return!0},uc=function(a){return a==a&&!me(a)},fh=function(a){for(var l=Wn(a),c=l.length;c--;){var g=l[c],L=a[g];l[c]=[g,L,uc(L)]}return l},cc=function(a,l){return function(c){return c!=null&&c[a]===l&&(l!==void 0||a in Object(c))}},ph=function(a){var l=fh(a);return l.length==1&&l[0][2]?cc(l[0][0],l[0][1]):function(c){return c===a||dh(c,a,l)}},Is=function(a){return typeof a=="symbol"||gn(a)&&ie(a)=="[object Symbol]"};var hh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,mh=/^\w*$/;const Ps=function(a,l){if(nt(a))return!1;var c=typeof a;return!(c!="number"&&c!="symbol"&&c!="boolean"&&a!=null&&!Is(a))||mh.test(a)||!hh.test(a)||l!=null&&a in Object(l)};function Ts(a,l){if(typeof a!="function"||l!=null&&typeof l!="function")throw new TypeError("Expected a function");var c=function(){var g=arguments,L=l?l.apply(this,g):g[0],M=c.cache;if(M.has(L))return M.get(L);var R=a.apply(this,g);return c.cache=M.set(L,R)||M,R};return c.cache=new(Ts.Cache||Nr),c}Ts.Cache=Nr;const gh=Ts;var yh=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,vh=/\\(\\)?/g;const wh=function(a){var l=gh(a,function(g){return c.size===500&&c.clear(),g}),c=l.cache;return l}(function(a){var l=[];return a.charCodeAt(0)===46&&l.push(""),a.replace(yh,function(c,g,L,M){l.push(L?M.replace(vh,"$1"):g||c)}),l}),kh=function(a,l){for(var c=-1,g=a==null?0:a.length,L=Array(g);++c<g;)L[c]=l(a[c],c,a);return L};var dc=_t?_t.prototype:void 0,fc=dc?dc.toString:void 0;const xh=function a(l){if(typeof l=="string")return l;if(nt(l))return kh(l,a)+"";if(Is(l))return fc?fc.call(l):"";var c=l+"";return c=="0"&&1/l==-1/0?"-0":c},Sh=function(a){return a==null?"":xh(a)},pc=function(a,l){return nt(a)?a:Ps(a,l)?[a]:wh(Sh(a))},Xi=function(a){if(typeof a=="string"||Is(a))return a;var l=a+"";return l=="0"&&1/a==-1/0?"-0":l},hc=function(a,l){for(var c=0,g=(l=pc(l,a)).length;a!=null&&c<g;)a=a[Xi(l[c++])];return c&&c==g?a:void 0},Ch=function(a,l,c){var g=a==null?void 0:hc(a,l);return g===void 0?c:g},_h=function(a,l){return a!=null&&l in Object(a)},bh=function(a,l,c){for(var g=-1,L=(l=pc(l,a)).length,M=!1;++g<L;){var R=Xi(l[g]);if(!(M=a!=null&&c(a,R)))break;a=a[R]}return M||++g!=L?M:!!(L=a==null?0:a.length)&&Ke(L)&&De(R,L)&&(nt(a)||yt(a))},Eh=function(a,l){return a!=null&&bh(a,l,_h)},Nh=function(a,l){return Ps(a)&&uc(l)?cc(Xi(a),l):function(c){var g=Ch(c,a);return g===void 0&&g===l?Eh(c,a):ac(l,g,3)}},Ih=function(a){return a},Ph=function(a){return function(l){return l?.[a]}},Th=function(a){return function(l){return hc(l,a)}},Ah=function(a){return Ps(a)?Ph(Xi(a)):Th(a)},Lh=function(a){return typeof a=="function"?a:a==null?Ih:typeof a=="object"?nt(a)?Nh(a[0],a[1]):ph(a):Ah(a)},Oh=function(a,l){var c={};return l=Lh(l),zi(a,function(g,L,M){mn(c,l(g,L,M),g)}),c};function mc(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}class Mh{constructor(l,c){mc(this,"ctx",void 0),mc(this,"serviceHooks",void 0),this.ctx=l,this.serviceHooks=c,l._execCallableAPI("register-search-service",l.baseInfo.id,c.name,c.options),Object.entries({query:{f:"onQuery",args:["graph","q",!0],reply:!0,transformOutput:g=>(nt(g?.blocks)&&(g.blocks=g.blocks.map(L=>L&&Oh(L,(M,R)=>`block/${R}`))),g)},rebuildBlocksIndice:{f:"onIndiceInit",args:["graph","blocks"]},transactBlocks:{f:"onBlocksChanged",args:["graph","data"]},truncateBlocks:{f:"onIndiceReset",args:["graph"]},removeDb:{f:"onGraph",args:["graph"]}}).forEach(([g,L])=>{const M=(R=>`service:search:${R}:${c.name}`)(g);l.caller.on(M,async R=>{if(Ue(c?.[L.f])){let F=null;try{F=await c[L.f].apply(c,(L.args||[]).map(Q=>{if(R){if(Q===!0)return R;if(R.hasOwnProperty(Q)){const oe=R[Q];return delete R[Q],oe}}})),L.transformOutput&&(F=L.transformOutput(F))}catch(Q){console.error("[SearchService] ",Q),F=Q}finally{L.reply&&l.caller.call(`${M}:reply`,F)}}})})}}function ln(a,l,c){return l in a?Object.defineProperty(a,l,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[l]=c,a}const jh=Symbol.for("proxy-continue"),Uh=O()("LSPlugin:user"),gc=new k("",{console:!0});function no(a,l,c){var g;const{key:L,label:M,desc:R,palette:F,keybinding:Q,extras:oe}=l;if(typeof c!="function")return this.logger.error(`${L||M}: command action should be function.`),!1;const le=function(ae){if(typeof ae=="string")return ae.trim().replace(/\s/g,"_").toLowerCase()}(L);if(!le)return this.logger.error(`${M}: command key is required.`),!1;const re=`SimpleCommandHook${le}${++wc}`;this.Editor["on"+re](c),(g=this.caller)===null||g===void 0||g.call("api:call",{method:"register-plugin-simple-command",args:[this.baseInfo.id,[{key:le,label:M,type:a,desc:R,keybinding:Q,extras:oe},["editor/hook",re]],F]})}function yc(a){return!(typeof(l=a)!="string"||l.length!==36||!/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi.test(l))||(gc.error(`#${a} is not a valid UUID string.`),!1);var l}let Ji=null,vc=new Map;const Dh={async getInfo(a){return Ji||(Ji=await this._execCallableAPIAsync("get-app-info")),typeof a=="string"?Ji[a]:Ji},registerCommand:no,registerSearchService(a){if(vc.has(a.name))throw new Error(`SearchService: #${a.name} has registered!`);vc.set(a.name,new Mh(this,a))},registerCommandPalette(a,l){const{key:c,label:g,keybinding:L}=a;return no.call(this,"$palette$",{key:c,label:g,palette:!0,keybinding:L},l)},registerCommandShortcut(a,l,c={}){typeof a=="string"&&(a={mode:"global",binding:a});const{binding:g}=a,L="$shortcut$",M=c.key||L+y(g?.toString());return no.call(this,L,{...c,key:M,palette:!1,keybinding:a},l)},registerUIItem(a,l){var c;const g=this.baseInfo.id;(c=this.caller)===null||c===void 0||c.call("api:call",{method:"register-plugin-ui-item",args:[g,a,l]})},registerPageMenuItem(a,l){if(typeof l!="function")return!1;const c=a+"_"+this.baseInfo.id,g=a;no.call(this,"page-menu-item",{key:c,label:g},l)},onBlockRendererSlotted(a,l){if(!yc(a))return;const c=this.baseInfo.id,g=`hook:editor:${y(`slot:${a}`)}`;return this.caller.on(g,l),this.App._installPluginHook(c,g),()=>{this.caller.off(g,l),this.App._uninstallPluginHook(c,g)}},invokeExternalPlugin(a,...l){var c;if(!(a=(c=a)===null||c===void 0?void 0:c.trim()))return;let[g,L]=a.split(".");if(!["models","commands"].includes(L?.toLowerCase()))throw new Error("Type only support '.models' or '.commands' currently.");const M=a.replace(`${g}.${L}.`,"");if(!g||!L||!M)throw new Error(`Illegal type of #${a} to invoke external plugin.`);return this._execCallableAPIAsync("invoke_external_plugin_cmd",g,L.toLowerCase(),M,l)},setFullScreen(a){const l=(...c)=>this._callWin("setFullScreen",...c);a==="toggle"?this._callWin("isFullScreen").then(c=>{c?l():l(!0)}):a?l(!0):l()}};let wc=0;const Rh={newBlockUUID(){return this._execCallableAPIAsync("new_block_uuid")},registerSlashCommand(a,l){var c;Uh("Register slash command #",this.baseInfo.id,a,l),typeof l=="function"&&(l=[["editor/clear-current-slash",!1],["editor/restore-saved-cursor"],["editor/hook",l]]),l=l.map(g=>{const[L,...M]=g;if(L==="editor/hook"){let R=M[0],F=()=>{var oe;(oe=this.caller)===null||oe===void 0||oe.callUserModel(R)};typeof R=="function"&&(F=R);const Q=`SlashCommandHook${L}${++wc}`;g[1]=Q,this.Editor["on"+Q](F)}return g}),(c=this.caller)===null||c===void 0||c.call("api:call",{method:"register-plugin-slash-command",args:[this.baseInfo.id,[a,l]]})},registerBlockContextMenuItem(a,l){if(typeof l!="function")return!1;const c=a+"_"+this.baseInfo.id;no.call(this,"block-context-menu-item",{key:c,label:a},l)},registerHighlightContextMenuItem(a,l,c){if(typeof l!="function")return!1;const g=a+"_"+this.baseInfo.id;no.call(this,"highlight-context-menu-item",{key:g,label:a,extras:c},l)},scrollToBlockInPage(a,l,c){const g="block-content-"+l;c!=null&&c.replaceState?this.App.replaceState("page",{name:a},{anchor:g}):this.App.pushState("page",{name:a},{anchor:g})}},zh={onBlockChanged(a,l){if(!yc(a))return;const c=this.baseInfo.id,g=`hook:db:${y(`block:${a}`)}`,L=({block:M,txData:R,txMeta:F})=>{M.uuid===a&&l(M,R,F)};return this.caller.on(g,L),this.App._installPluginHook(c,g),()=>{this.caller.off(g,L),this.App._uninstallPluginHook(c,g)}},datascriptQuery(a,...l){return l.pop(),l!=null&&l.some(c=>typeof c=="function")?this.Experiments.ensureHostScope().logseq.api.datascript_query(a,...l):this._execCallableAPIAsync("datascript_query",a,...l)}},$h={},Fh={},Bh={makeSandboxStorage(){return new Pe(this,{assets:!0})}};class As extends P(){constructor(l,c){super(),ln(this,"_baseInfo",void 0),ln(this,"_caller",void 0),ln(this,"_version","0.0.17"),ln(this,"_debugTag",""),ln(this,"_settingsSchema",void 0),ln(this,"_connected",!1),ln(this,"_ui",new Map),ln(this,"_mFileStorage",void 0),ln(this,"_mRequest",void 0),ln(this,"_mExperiments",void 0),ln(this,"_beforeunloadCallback",void 0),this._baseInfo=l,this._caller=c,c.on("sys:ui:visible",g=>{g!=null&&g.toggle&&this.toggleMainUI()}),c.on("settings:changed",g=>{const L=Object.assign({},this.settings),M=Object.assign(this._baseInfo.settings,g);this.emit("settings:changed",{...M},L)}),c.on("beforeunload",async g=>{const{actor:L,...M}=g,R=this._beforeunloadCallback;try{R&&await R(M),L?.resolve(null)}catch(F){this.logger.error("[beforeunload] ",F),L?.reject(F)}})}async ready(l,c){var g,L;if(!this._connected)try{var M;typeof l=="function"&&(c=l,l={});let R=await this._caller.connectToParent(l);this._connected=!0,g=this._baseInfo,L=R,R=d()(g,L,{arrayMerge:(F,Q)=>Q}),this._baseInfo=R,(M=R)!==null&&M!==void 0&&M.id&&(this._debugTag=this._caller.debugTag=`#${R.id} [${R.name}]`,this.logger.setTag(this._debugTag)),this._settingsSchema&&(R.settings=function(F,Q){const oe=(Q||[]).reduce((le,re)=>("default"in re&&(le[re.key]=re.default),le),{});return Object.assign(oe,F)}(R.settings,this._settingsSchema),await this.useSettingsSchema(this._settingsSchema));try{await this._execCallableAPIAsync("setSDKMetadata",{version:this._version})}catch(F){console.warn(F)}c&&c.call(this,R)}catch(R){console.error(`${this._debugTag} [Ready Error]`,R)}}ensureConnected(){if(!this._connected)throw new Error("not connected")}beforeunload(l){typeof l=="function"&&(this._beforeunloadCallback=l)}provideModel(l){return this.caller._extendUserModel(l),this}provideTheme(l){return this.caller.call("provider:theme",l),this}provideStyle(l){return this.caller.call("provider:style",l),this}provideUI(l){return this.caller.call("provider:ui",l),this}useSettingsSchema(l){return this.connected&&this.caller.call("settings:schema",{schema:l,isSync:!0}),this._settingsSchema=l,this}updateSettings(l){this.caller.call("settings:update",l)}onSettingsChanged(l){const c="settings:changed";return this.on(c,l),()=>this.off(c,l)}showSettingsUI(){this.caller.call("settings:visible:changed",{visible:!0})}hideSettingsUI(){this.caller.call("settings:visible:changed",{visible:!1})}setMainUIAttrs(l){this.caller.call("main-ui:attrs",l)}setMainUIInlineStyle(l){this.caller.call("main-ui:style",l)}hideMainUI(l){const c={key:0,visible:!1,cursor:l?.restoreEditingCursor};this.caller.call("main-ui:visible",c),this.emit("ui:visible:changed",c),this._ui.set(c.key,c)}showMainUI(l){const c={key:0,visible:!0,autoFocus:l?.autoFocus};this.caller.call("main-ui:visible",c),this.emit("ui:visible:changed",c),this._ui.set(c.key,c)}toggleMainUI(){const c=this._ui.get(0);c&&c.visible?this.hideMainUI():this.showMainUI()}get version(){return this._version}get isMainUIVisible(){const l=this._ui.get(0);return!!(l&&l.visible)}get connected(){return this._connected}get baseInfo(){return this._baseInfo}get effect(){return(l=this)&&(((c=l.baseInfo)===null||c===void 0?void 0:c.effect)||!((g=l.baseInfo)!==null&&g!==void 0&&g.iir));var l,c,g}get logger(){return gc}get settings(){var l;return(l=this.baseInfo)===null||l===void 0?void 0:l.settings}get caller(){return this._caller}resolveResourceFullUrl(l){if(this.ensureConnected(),l)return l=l.replace(/^[.\\/]+/,""),T(this._baseInfo.lsr,l)}_makeUserProxy(l,c){const g=this,L=this.caller;return new Proxy(l,{get(M,R,F){const Q=M[R];return function(...oe){if(Q){const re=Q.apply(g,oe.concat(c));if(re!==jh)return re}if(c){const re=R.toString().match(/^(once|off|on)/i);if(re!=null){const ae=re[0].toLowerCase(),xe=re.input,de=ae==="off",Ne=g.baseInfo.id;let Ae=xe.slice(ae.length),Xe=oe[0],Wt=oe[1];typeof Xe=="string"&&typeof Wt=="function"&&(Xe=Xe.replace(/^logseq./,":"),Ae=`${Ae}${Xe}`,Xe=Wt,Wt=oe[2]),Ae=`hook:${c}:${y(Ae)}`,L[ae](Ae,Xe);const Ar=()=>{L.off(Ae,Xe),L.listenerCount(Ae)||g.App._uninstallPluginHook(Ne,Ae)};return de?void Ar():(g.App._installPluginHook(Ne,Ae,Wt),Ar)}}let le=R;return["git","ui","assets"].includes(c)&&(le=c+"_"+le),L.callAsync("api:call",{tag:c,method:le,args:oe})}}})}_execCallableAPIAsync(l,...c){return this._caller.callAsync("api:call",{method:l,args:c})}_execCallableAPI(l,...c){this._caller.call("api:call",{method:l,args:c})}_callWin(...l){return this._execCallableAPIAsync("_callMainWin",...l)}get App(){return this._makeUserProxy(Dh,"app")}get Editor(){return this._makeUserProxy(Rh,"editor")}get DB(){return this._makeUserProxy(zh,"db")}get Git(){return this._makeUserProxy($h,"git")}get UI(){return this._makeUserProxy(Fh,"ui")}get Assets(){return this._makeUserProxy(Bh,"assets")}get FileStorage(){let l=this._mFileStorage;return l||(l=this._mFileStorage=new Pe(this)),l}get Request(){let l=this._mRequest;return l||(l=this._mRequest=new gt(this)),l}get Experiments(){let l=this._mExperiments;return l||(l=this._mExperiments=new tt(this)),l}}function kc(a,l){return new As(a,l)}if(window.__LSP__HOST__==null){const a=new Jt(null);window.logseq=kc({},a)}})(),i})())})(Al,Al.exports);Al.exports;var qd={exports:{}},zt={},Vd={exports:{}},Kd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(H,I){var j=H.length;H.push(I);e:for(;0<j;){var B=j-1>>>1,se=H[B];if(0<o(se,I))H[B]=I,H[j]=se,j=B;else break e}}function n(H){return H.length===0?null:H[0]}function r(H){if(H.length===0)return null;var I=H[0],j=H.pop();if(j!==I){H[0]=j;e:for(var B=0,se=H.length,je=se>>>1;B<je;){var G=2*(B+1)-1,ue=H[G],ye=G+1,$e=H[ye];if(0>o(ue,j))ye<se&&0>o($e,ue)?(H[B]=$e,H[ye]=j,B=ye):(H[B]=ue,H[G]=j,B=G);else if(ye<se&&0>o($e,j))H[B]=$e,H[ye]=j,B=ye;else break e}}return I}function o(H,I){var j=H.sortIndex-I.sortIndex;return j!==0?j:H.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,u=s.now();e.unstable_now=function(){return s.now()-u}}var d=[],f=[],v=1,N=null,_=3,S=!1,w=!1,C=!1,P=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(H){for(var I=n(f);I!==null;){if(I.callback===null)r(f);else if(I.startTime<=H)r(f),I.sortIndex=I.expirationTime,t(d,I);else break;I=n(f)}}function k(H){if(C=!1,y(H),!w)if(n(d)!==null)w=!0,X(T);else{var I=n(f);I!==null&&ee(k,I.startTime-H)}}function T(H,I){w=!1,C&&(C=!1,h(x),x=-1),S=!0;var j=_;try{for(y(I),N=n(d);N!==null&&(!(N.expirationTime>I)||H&&!ne());){var B=N.callback;if(typeof B=="function"){N.callback=null,_=N.priorityLevel;var se=B(N.expirationTime<=I);I=e.unstable_now(),typeof se=="function"?N.callback=se:N===n(d)&&r(d),y(I)}else r(d);N=n(d)}if(N!==null)var je=!0;else{var G=n(f);G!==null&&ee(k,G.startTime-I),je=!1}return je}finally{N=null,_=j,S=!1}}var A=!1,m=null,x=-1,O=5,U=-1;function ne(){return!(e.unstable_now()-U<O)}function ce(){if(m!==null){var H=e.unstable_now();U=H;var I=!0;try{I=m(!0,H)}finally{I?fe():(A=!1,m=null)}}else A=!1}var fe;if(typeof p=="function")fe=function(){p(ce)};else if(typeof MessageChannel<"u"){var pe=new MessageChannel,W=pe.port2;pe.port1.onmessage=ce,fe=function(){W.postMessage(null)}}else fe=function(){P(ce,0)};function X(H){m=H,A||(A=!0,fe())}function ee(H,I){x=P(function(){H(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(H){H.callback=null},e.unstable_continueExecution=function(){w||S||(w=!0,X(T))},e.unstable_forceFrameRate=function(H){0>H||125<H?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<H?Math.floor(1e3/H):5},e.unstable_getCurrentPriorityLevel=function(){return _},e.unstable_getFirstCallbackNode=function(){return n(d)},e.unstable_next=function(H){switch(_){case 1:case 2:case 3:var I=3;break;default:I=_}var j=_;_=I;try{return H()}finally{_=j}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(H,I){switch(H){case 1:case 2:case 3:case 4:case 5:break;default:H=3}var j=_;_=H;try{return I()}finally{_=j}},e.unstable_scheduleCallback=function(H,I,j){var B=e.unstable_now();switch(typeof j=="object"&&j!==null?(j=j.delay,j=typeof j=="number"&&0<j?B+j:B):j=B,H){case 1:var se=-1;break;case 2:se=250;break;case 5:se=**********;break;case 4:se=1e4;break;default:se=5e3}return se=j+se,H={id:v++,callback:I,priorityLevel:H,startTime:j,expirationTime:se,sortIndex:-1},j>B?(H.sortIndex=j,t(f,H),n(d)===null&&H===n(f)&&(C?(h(x),x=-1):C=!0,ee(k,j-B))):(H.sortIndex=se,t(d,H),w||S||(w=!0,X(T))),H},e.unstable_shouldYield=ne,e.unstable_wrapCallback=function(H){var I=_;return function(){var j=_;_=I;try{return H.apply(this,arguments)}finally{_=j}}}})(Kd);Vd.exports=Kd;var dm=Vd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fm=Z,Rt=dm;function q(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Qd=new Set,gi={};function Wr(e,t){_o(e,t),_o(e+"Capture",t)}function _o(e,t){for(gi[e]=t,e=0;e<t.length;e++)Qd.add(t[e])}var Ln=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),la=Object.prototype.hasOwnProperty,pm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,_c={},bc={};function hm(e){return la.call(bc,e)?!0:la.call(_c,e)?!1:pm.test(e)?bc[e]=!0:(_c[e]=!0,!1)}function mm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function gm(e,t,n,r){if(t===null||typeof t>"u"||mm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function xt(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var ct={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ct[e]=new xt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ct[t]=new xt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ct[e]=new xt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ct[e]=new xt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ct[e]=new xt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ct[e]=new xt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ct[e]=new xt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ct[e]=new xt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ct[e]=new xt(e,5,!1,e.toLowerCase(),null,!1,!1)});var lu=/[\-:]([a-z])/g;function su(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(lu,su);ct[t]=new xt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(lu,su);ct[t]=new xt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(lu,su);ct[t]=new xt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ct[e]=new xt(e,1,!1,e.toLowerCase(),null,!1,!1)});ct.xlinkHref=new xt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ct[e]=new xt(e,1,!1,e.toLowerCase(),null,!0,!0)});function au(e,t,n,r){var o=ct.hasOwnProperty(t)?ct[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(gm(t,n,o,r)&&(n=null),r||o===null?hm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Un=fm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,tl=Symbol.for("react.element"),oo=Symbol.for("react.portal"),io=Symbol.for("react.fragment"),uu=Symbol.for("react.strict_mode"),sa=Symbol.for("react.profiler"),Gd=Symbol.for("react.provider"),Yd=Symbol.for("react.context"),cu=Symbol.for("react.forward_ref"),aa=Symbol.for("react.suspense"),ua=Symbol.for("react.suspense_list"),du=Symbol.for("react.memo"),Yn=Symbol.for("react.lazy"),Xd=Symbol.for("react.offscreen"),Ec=Symbol.iterator;function Qo(e){return e===null||typeof e!="object"?null:(e=Ec&&e[Ec]||e["@@iterator"],typeof e=="function"?e:null)}var We=Object.assign,Os;function ni(e){if(Os===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Os=t&&t[1]||""}return`
`+Os+e}var Ms=!1;function js(e,t){if(!e||Ms)return"";Ms=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(f){var r=f}Reflect.construct(e,[],t)}else{try{t.call()}catch(f){r=f}e.call(t.prototype)}else{try{throw Error()}catch(f){r=f}e()}}catch(f){if(f&&r&&typeof f.stack=="string"){for(var o=f.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,u=i.length-1;1<=s&&0<=u&&o[s]!==i[u];)u--;for(;1<=s&&0<=u;s--,u--)if(o[s]!==i[u]){if(s!==1||u!==1)do if(s--,u--,0>u||o[s]!==i[u]){var d=`
`+o[s].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),d}while(1<=s&&0<=u);break}}}finally{Ms=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ni(e):""}function ym(e){switch(e.tag){case 5:return ni(e.type);case 16:return ni("Lazy");case 13:return ni("Suspense");case 19:return ni("SuspenseList");case 0:case 2:case 15:return e=js(e.type,!1),e;case 11:return e=js(e.type.render,!1),e;case 1:return e=js(e.type,!0),e;default:return""}}function ca(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case io:return"Fragment";case oo:return"Portal";case sa:return"Profiler";case uu:return"StrictMode";case aa:return"Suspense";case ua:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Yd:return(e.displayName||"Context")+".Consumer";case Gd:return(e._context.displayName||"Context")+".Provider";case cu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case du:return t=e.displayName||null,t!==null?t:ca(e.type)||"Memo";case Yn:t=e._payload,e=e._init;try{return ca(e(t))}catch{}}return null}function vm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ca(t);case 8:return t===uu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function dr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Jd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wm(e){var t=Jd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function nl(e){e._valueTracker||(e._valueTracker=wm(e))}function Zd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Jd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ll(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function da(e,t){var n=t.checked;return We({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Nc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=dr(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ef(e,t){t=t.checked,t!=null&&au(e,"checked",t,!1)}function fa(e,t){ef(e,t);var n=dr(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?pa(e,t.type,n):t.hasOwnProperty("defaultValue")&&pa(e,t.type,dr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ic(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function pa(e,t,n){(t!=="number"||Ll(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ri=Array.isArray;function yo(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+dr(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ha(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(q(91));return We({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(q(92));if(ri(n)){if(1<n.length)throw Error(q(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:dr(n)}}function tf(e,t){var n=dr(t.value),r=dr(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Tc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function nf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ma(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?nf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var rl,rf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(rl=rl||document.createElement("div"),rl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=rl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function yi(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var si={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},km=["Webkit","ms","Moz","O"];Object.keys(si).forEach(function(e){km.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),si[t]=si[e]})});function of(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||si.hasOwnProperty(e)&&si[e]?(""+t).trim():t+"px"}function lf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=of(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var xm=We({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ga(e,t){if(t){if(xm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(q(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(q(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(q(61))}if(t.style!=null&&typeof t.style!="object")throw Error(q(62))}}function ya(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var va=null;function fu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var wa=null,vo=null,wo=null;function Ac(e){if(e=Ui(e)){if(typeof wa!="function")throw Error(q(280));var t=e.stateNode;t&&(t=as(t),wa(e.stateNode,e.type,t))}}function sf(e){vo?wo?wo.push(e):wo=[e]:vo=e}function af(){if(vo){var e=vo,t=wo;if(wo=vo=null,Ac(e),t)for(e=0;e<t.length;e++)Ac(t[e])}}function uf(e,t){return e(t)}function cf(){}var Us=!1;function df(e,t,n){if(Us)return e(t,n);Us=!0;try{return uf(e,t,n)}finally{Us=!1,(vo!==null||wo!==null)&&(cf(),af())}}function vi(e,t){var n=e.stateNode;if(n===null)return null;var r=as(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(q(231,t,typeof n));return n}var ka=!1;if(Ln)try{var Go={};Object.defineProperty(Go,"passive",{get:function(){ka=!0}}),window.addEventListener("test",Go,Go),window.removeEventListener("test",Go,Go)}catch{ka=!1}function Sm(e,t,n,r,o,i,s,u,d){var f=Array.prototype.slice.call(arguments,3);try{t.apply(n,f)}catch(v){this.onError(v)}}var ai=!1,Ol=null,Ml=!1,xa=null,Cm={onError:function(e){ai=!0,Ol=e}};function _m(e,t,n,r,o,i,s,u,d){ai=!1,Ol=null,Sm.apply(Cm,arguments)}function bm(e,t,n,r,o,i,s,u,d){if(_m.apply(this,arguments),ai){if(ai){var f=Ol;ai=!1,Ol=null}else throw Error(q(198));Ml||(Ml=!0,xa=f)}}function qr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ff(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Lc(e){if(qr(e)!==e)throw Error(q(188))}function Em(e){var t=e.alternate;if(!t){if(t=qr(e),t===null)throw Error(q(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Lc(o),e;if(i===r)return Lc(o),t;i=i.sibling}throw Error(q(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,u=o.child;u;){if(u===n){s=!0,n=o,r=i;break}if(u===r){s=!0,r=o,n=i;break}u=u.sibling}if(!s){for(u=i.child;u;){if(u===n){s=!0,n=i,r=o;break}if(u===r){s=!0,r=i,n=o;break}u=u.sibling}if(!s)throw Error(q(189))}}if(n.alternate!==r)throw Error(q(190))}if(n.tag!==3)throw Error(q(188));return n.stateNode.current===n?e:t}function pf(e){return e=Em(e),e!==null?hf(e):null}function hf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=hf(e);if(t!==null)return t;e=e.sibling}return null}var mf=Rt.unstable_scheduleCallback,Oc=Rt.unstable_cancelCallback,Nm=Rt.unstable_shouldYield,Im=Rt.unstable_requestPaint,Qe=Rt.unstable_now,Pm=Rt.unstable_getCurrentPriorityLevel,pu=Rt.unstable_ImmediatePriority,gf=Rt.unstable_UserBlockingPriority,jl=Rt.unstable_NormalPriority,Tm=Rt.unstable_LowPriority,yf=Rt.unstable_IdlePriority,os=null,Sn=null;function Am(e){if(Sn&&typeof Sn.onCommitFiberRoot=="function")try{Sn.onCommitFiberRoot(os,e,void 0,(e.current.flags&128)===128)}catch{}}var dn=Math.clz32?Math.clz32:Mm,Lm=Math.log,Om=Math.LN2;function Mm(e){return e>>>=0,e===0?32:31-(Lm(e)/Om|0)|0}var ol=64,il=4194304;function oi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ul(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var u=s&~o;u!==0?r=oi(u):(i&=s,i!==0&&(r=oi(i)))}else s=n&~o,s!==0?r=oi(s):i!==0&&(r=oi(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-dn(t),o=1<<n,r|=e[n],t&=~o;return r}function jm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Um(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-dn(i),u=1<<s,d=o[s];d===-1?(!(u&n)||u&r)&&(o[s]=jm(u,t)):d<=t&&(e.expiredLanes|=u),i&=~u}}function Sa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function vf(){var e=ol;return ol<<=1,!(ol&4194240)&&(ol=64),e}function Ds(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Mi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-dn(t),e[t]=n}function Dm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-dn(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function hu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-dn(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var Ie=0;function wf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var kf,mu,xf,Sf,Cf,Ca=!1,ll=[],rr=null,or=null,ir=null,wi=new Map,ki=new Map,Jn=[],Rm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mc(e,t){switch(e){case"focusin":case"focusout":rr=null;break;case"dragenter":case"dragleave":or=null;break;case"mouseover":case"mouseout":ir=null;break;case"pointerover":case"pointerout":wi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ki.delete(t.pointerId)}}function Yo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Ui(t),t!==null&&mu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function zm(e,t,n,r,o){switch(t){case"focusin":return rr=Yo(rr,e,t,n,r,o),!0;case"dragenter":return or=Yo(or,e,t,n,r,o),!0;case"mouseover":return ir=Yo(ir,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return wi.set(i,Yo(wi.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,ki.set(i,Yo(ki.get(i)||null,e,t,n,r,o)),!0}return!1}function _f(e){var t=Mr(e.target);if(t!==null){var n=qr(t);if(n!==null){if(t=n.tag,t===13){if(t=ff(n),t!==null){e.blockedOn=t,Cf(e.priority,function(){xf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function xl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=_a(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);va=r,n.target.dispatchEvent(r),va=null}else return t=Ui(n),t!==null&&mu(t),e.blockedOn=n,!1;t.shift()}return!0}function jc(e,t,n){xl(e)&&n.delete(t)}function $m(){Ca=!1,rr!==null&&xl(rr)&&(rr=null),or!==null&&xl(or)&&(or=null),ir!==null&&xl(ir)&&(ir=null),wi.forEach(jc),ki.forEach(jc)}function Xo(e,t){e.blockedOn===t&&(e.blockedOn=null,Ca||(Ca=!0,Rt.unstable_scheduleCallback(Rt.unstable_NormalPriority,$m)))}function xi(e){function t(o){return Xo(o,e)}if(0<ll.length){Xo(ll[0],e);for(var n=1;n<ll.length;n++){var r=ll[n];r.blockedOn===e&&(r.blockedOn=null)}}for(rr!==null&&Xo(rr,e),or!==null&&Xo(or,e),ir!==null&&Xo(ir,e),wi.forEach(t),ki.forEach(t),n=0;n<Jn.length;n++)r=Jn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Jn.length&&(n=Jn[0],n.blockedOn===null);)_f(n),n.blockedOn===null&&Jn.shift()}var ko=Un.ReactCurrentBatchConfig,Dl=!0;function Fm(e,t,n,r){var o=Ie,i=ko.transition;ko.transition=null;try{Ie=1,gu(e,t,n,r)}finally{Ie=o,ko.transition=i}}function Bm(e,t,n,r){var o=Ie,i=ko.transition;ko.transition=null;try{Ie=4,gu(e,t,n,r)}finally{Ie=o,ko.transition=i}}function gu(e,t,n,r){if(Dl){var o=_a(e,t,n,r);if(o===null)Ks(e,t,r,Rl,n),Mc(e,r);else if(zm(o,e,t,n,r))r.stopPropagation();else if(Mc(e,r),t&4&&-1<Rm.indexOf(e)){for(;o!==null;){var i=Ui(o);if(i!==null&&kf(i),i=_a(e,t,n,r),i===null&&Ks(e,t,r,Rl,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ks(e,t,r,null,n)}}var Rl=null;function _a(e,t,n,r){if(Rl=null,e=fu(r),e=Mr(e),e!==null)if(t=qr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ff(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Rl=e,null}function bf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Pm()){case pu:return 1;case gf:return 4;case jl:case Tm:return 16;case yf:return 536870912;default:return 16}default:return 16}}var tr=null,yu=null,Sl=null;function Ef(){if(Sl)return Sl;var e,t=yu,n=t.length,r,o="value"in tr?tr.value:tr.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Sl=o.slice(e,1<r?1-r:void 0)}function Cl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function sl(){return!0}function Uc(){return!1}function $t(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(n=e[u],this[u]=n?n(i):i[u]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?sl:Uc,this.isPropagationStopped=Uc,this}return We(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=sl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=sl)},persist:function(){},isPersistent:sl}),t}var Lo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vu=$t(Lo),ji=We({},Lo,{view:0,detail:0}),Hm=$t(ji),Rs,zs,Jo,is=We({},ji,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Jo&&(Jo&&e.type==="mousemove"?(Rs=e.screenX-Jo.screenX,zs=e.screenY-Jo.screenY):zs=Rs=0,Jo=e),Rs)},movementY:function(e){return"movementY"in e?e.movementY:zs}}),Dc=$t(is),Wm=We({},is,{dataTransfer:0}),qm=$t(Wm),Vm=We({},ji,{relatedTarget:0}),$s=$t(Vm),Km=We({},Lo,{animationName:0,elapsedTime:0,pseudoElement:0}),Qm=$t(Km),Gm=We({},Lo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ym=$t(Gm),Xm=We({},Lo,{data:0}),Rc=$t(Xm),Jm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},eg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function tg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=eg[e])?!!t[e]:!1}function wu(){return tg}var ng=We({},ji,{key:function(e){if(e.key){var t=Jm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Cl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wu,charCode:function(e){return e.type==="keypress"?Cl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Cl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),rg=$t(ng),og=We({},is,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zc=$t(og),ig=We({},ji,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wu}),lg=$t(ig),sg=We({},Lo,{propertyName:0,elapsedTime:0,pseudoElement:0}),ag=$t(sg),ug=We({},is,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),cg=$t(ug),dg=[9,13,27,32],ku=Ln&&"CompositionEvent"in window,ui=null;Ln&&"documentMode"in document&&(ui=document.documentMode);var fg=Ln&&"TextEvent"in window&&!ui,Nf=Ln&&(!ku||ui&&8<ui&&11>=ui),$c=String.fromCharCode(32),Fc=!1;function If(e,t){switch(e){case"keyup":return dg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var lo=!1;function pg(e,t){switch(e){case"compositionend":return Pf(t);case"keypress":return t.which!==32?null:(Fc=!0,$c);case"textInput":return e=t.data,e===$c&&Fc?null:e;default:return null}}function hg(e,t){if(lo)return e==="compositionend"||!ku&&If(e,t)?(e=Ef(),Sl=yu=tr=null,lo=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nf&&t.locale!=="ko"?null:t.data;default:return null}}var mg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!mg[e.type]:t==="textarea"}function Tf(e,t,n,r){sf(r),t=zl(t,"onChange"),0<t.length&&(n=new vu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ci=null,Si=null;function gg(e){Ff(e,0)}function ls(e){var t=uo(e);if(Zd(t))return e}function yg(e,t){if(e==="change")return t}var Af=!1;if(Ln){var Fs;if(Ln){var Bs="oninput"in document;if(!Bs){var Hc=document.createElement("div");Hc.setAttribute("oninput","return;"),Bs=typeof Hc.oninput=="function"}Fs=Bs}else Fs=!1;Af=Fs&&(!document.documentMode||9<document.documentMode)}function Wc(){ci&&(ci.detachEvent("onpropertychange",Lf),Si=ci=null)}function Lf(e){if(e.propertyName==="value"&&ls(Si)){var t=[];Tf(t,Si,e,fu(e)),df(gg,t)}}function vg(e,t,n){e==="focusin"?(Wc(),ci=t,Si=n,ci.attachEvent("onpropertychange",Lf)):e==="focusout"&&Wc()}function wg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ls(Si)}function kg(e,t){if(e==="click")return ls(t)}function xg(e,t){if(e==="input"||e==="change")return ls(t)}function Sg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var pn=typeof Object.is=="function"?Object.is:Sg;function Ci(e,t){if(pn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!la.call(t,o)||!pn(e[o],t[o]))return!1}return!0}function qc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Vc(e,t){var n=qc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=qc(n)}}function Of(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Of(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Mf(){for(var e=window,t=Ll();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ll(e.document)}return t}function xu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Cg(e){var t=Mf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Of(n.ownerDocument.documentElement,n)){if(r!==null&&xu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Vc(n,i);var s=Vc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var _g=Ln&&"documentMode"in document&&11>=document.documentMode,so=null,ba=null,di=null,Ea=!1;function Kc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ea||so==null||so!==Ll(r)||(r=so,"selectionStart"in r&&xu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),di&&Ci(di,r)||(di=r,r=zl(ba,"onSelect"),0<r.length&&(t=new vu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=so)))}function al(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ao={animationend:al("Animation","AnimationEnd"),animationiteration:al("Animation","AnimationIteration"),animationstart:al("Animation","AnimationStart"),transitionend:al("Transition","TransitionEnd")},Hs={},jf={};Ln&&(jf=document.createElement("div").style,"AnimationEvent"in window||(delete ao.animationend.animation,delete ao.animationiteration.animation,delete ao.animationstart.animation),"TransitionEvent"in window||delete ao.transitionend.transition);function ss(e){if(Hs[e])return Hs[e];if(!ao[e])return e;var t=ao[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in jf)return Hs[e]=t[n];return e}var Uf=ss("animationend"),Df=ss("animationiteration"),Rf=ss("animationstart"),zf=ss("transitionend"),$f=new Map,Qc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function pr(e,t){$f.set(e,t),Wr(t,[e])}for(var Ws=0;Ws<Qc.length;Ws++){var qs=Qc[Ws],bg=qs.toLowerCase(),Eg=qs[0].toUpperCase()+qs.slice(1);pr(bg,"on"+Eg)}pr(Uf,"onAnimationEnd");pr(Df,"onAnimationIteration");pr(Rf,"onAnimationStart");pr("dblclick","onDoubleClick");pr("focusin","onFocus");pr("focusout","onBlur");pr(zf,"onTransitionEnd");_o("onMouseEnter",["mouseout","mouseover"]);_o("onMouseLeave",["mouseout","mouseover"]);_o("onPointerEnter",["pointerout","pointerover"]);_o("onPointerLeave",["pointerout","pointerover"]);Wr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Wr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Wr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Wr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Wr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Wr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ii="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ng=new Set("cancel close invalid load scroll toggle".split(" ").concat(ii));function Gc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,bm(r,t,void 0,e),e.currentTarget=null}function Ff(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var u=r[s],d=u.instance,f=u.currentTarget;if(u=u.listener,d!==i&&o.isPropagationStopped())break e;Gc(o,u,f),i=d}else for(s=0;s<r.length;s++){if(u=r[s],d=u.instance,f=u.currentTarget,u=u.listener,d!==i&&o.isPropagationStopped())break e;Gc(o,u,f),i=d}}}if(Ml)throw e=xa,Ml=!1,xa=null,e}function Oe(e,t){var n=t[Aa];n===void 0&&(n=t[Aa]=new Set);var r=e+"__bubble";n.has(r)||(Bf(t,e,2,!1),n.add(r))}function Vs(e,t,n){var r=0;t&&(r|=4),Bf(n,e,r,t)}var ul="_reactListening"+Math.random().toString(36).slice(2);function _i(e){if(!e[ul]){e[ul]=!0,Qd.forEach(function(n){n!=="selectionchange"&&(Ng.has(n)||Vs(n,!1,e),Vs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ul]||(t[ul]=!0,Vs("selectionchange",!1,t))}}function Bf(e,t,n,r){switch(bf(t)){case 1:var o=Fm;break;case 4:o=Bm;break;default:o=gu}n=o.bind(null,t,n,e),o=void 0,!ka||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ks(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var u=r.stateNode.containerInfo;if(u===o||u.nodeType===8&&u.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var d=s.tag;if((d===3||d===4)&&(d=s.stateNode.containerInfo,d===o||d.nodeType===8&&d.parentNode===o))return;s=s.return}for(;u!==null;){if(s=Mr(u),s===null)return;if(d=s.tag,d===5||d===6){r=i=s;continue e}u=u.parentNode}}r=r.return}df(function(){var f=i,v=fu(n),N=[];e:{var _=$f.get(e);if(_!==void 0){var S=vu,w=e;switch(e){case"keypress":if(Cl(n)===0)break e;case"keydown":case"keyup":S=rg;break;case"focusin":w="focus",S=$s;break;case"focusout":w="blur",S=$s;break;case"beforeblur":case"afterblur":S=$s;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=Dc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=qm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=lg;break;case Uf:case Df:case Rf:S=Qm;break;case zf:S=ag;break;case"scroll":S=Hm;break;case"wheel":S=cg;break;case"copy":case"cut":case"paste":S=Ym;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=zc}var C=(t&4)!==0,P=!C&&e==="scroll",h=C?_!==null?_+"Capture":null:_;C=[];for(var p=f,y;p!==null;){y=p;var k=y.stateNode;if(y.tag===5&&k!==null&&(y=k,h!==null&&(k=vi(p,h),k!=null&&C.push(bi(p,k,y)))),P)break;p=p.return}0<C.length&&(_=new S(_,w,null,n,v),N.push({event:_,listeners:C}))}}if(!(t&7)){e:{if(_=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",_&&n!==va&&(w=n.relatedTarget||n.fromElement)&&(Mr(w)||w[On]))break e;if((S||_)&&(_=v.window===v?v:(_=v.ownerDocument)?_.defaultView||_.parentWindow:window,S?(w=n.relatedTarget||n.toElement,S=f,w=w?Mr(w):null,w!==null&&(P=qr(w),w!==P||w.tag!==5&&w.tag!==6)&&(w=null)):(S=null,w=f),S!==w)){if(C=Dc,k="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(C=zc,k="onPointerLeave",h="onPointerEnter",p="pointer"),P=S==null?_:uo(S),y=w==null?_:uo(w),_=new C(k,p+"leave",S,n,v),_.target=P,_.relatedTarget=y,k=null,Mr(v)===f&&(C=new C(h,p+"enter",w,n,v),C.target=y,C.relatedTarget=P,k=C),P=k,S&&w)t:{for(C=S,h=w,p=0,y=C;y;y=ro(y))p++;for(y=0,k=h;k;k=ro(k))y++;for(;0<p-y;)C=ro(C),p--;for(;0<y-p;)h=ro(h),y--;for(;p--;){if(C===h||h!==null&&C===h.alternate)break t;C=ro(C),h=ro(h)}C=null}else C=null;S!==null&&Yc(N,_,S,C,!1),w!==null&&P!==null&&Yc(N,P,w,C,!0)}}e:{if(_=f?uo(f):window,S=_.nodeName&&_.nodeName.toLowerCase(),S==="select"||S==="input"&&_.type==="file")var T=yg;else if(Bc(_))if(Af)T=xg;else{T=wg;var A=vg}else(S=_.nodeName)&&S.toLowerCase()==="input"&&(_.type==="checkbox"||_.type==="radio")&&(T=kg);if(T&&(T=T(e,f))){Tf(N,T,n,v);break e}A&&A(e,_,f),e==="focusout"&&(A=_._wrapperState)&&A.controlled&&_.type==="number"&&pa(_,"number",_.value)}switch(A=f?uo(f):window,e){case"focusin":(Bc(A)||A.contentEditable==="true")&&(so=A,ba=f,di=null);break;case"focusout":di=ba=so=null;break;case"mousedown":Ea=!0;break;case"contextmenu":case"mouseup":case"dragend":Ea=!1,Kc(N,n,v);break;case"selectionchange":if(_g)break;case"keydown":case"keyup":Kc(N,n,v)}var m;if(ku)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else lo?If(e,n)&&(x="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(x="onCompositionStart");x&&(Nf&&n.locale!=="ko"&&(lo||x!=="onCompositionStart"?x==="onCompositionEnd"&&lo&&(m=Ef()):(tr=v,yu="value"in tr?tr.value:tr.textContent,lo=!0)),A=zl(f,x),0<A.length&&(x=new Rc(x,e,null,n,v),N.push({event:x,listeners:A}),m?x.data=m:(m=Pf(n),m!==null&&(x.data=m)))),(m=fg?pg(e,n):hg(e,n))&&(f=zl(f,"onBeforeInput"),0<f.length&&(v=new Rc("onBeforeInput","beforeinput",null,n,v),N.push({event:v,listeners:f}),v.data=m))}Ff(N,t)})}function bi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function zl(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=vi(e,n),i!=null&&r.unshift(bi(e,i,o)),i=vi(e,t),i!=null&&r.push(bi(e,i,o))),e=e.return}return r}function ro(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Yc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var u=n,d=u.alternate,f=u.stateNode;if(d!==null&&d===r)break;u.tag===5&&f!==null&&(u=f,o?(d=vi(n,i),d!=null&&s.unshift(bi(n,d,u))):o||(d=vi(n,i),d!=null&&s.push(bi(n,d,u)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Ig=/\r\n?/g,Pg=/\u0000|\uFFFD/g;function Xc(e){return(typeof e=="string"?e:""+e).replace(Ig,`
`).replace(Pg,"")}function cl(e,t,n){if(t=Xc(t),Xc(e)!==t&&n)throw Error(q(425))}function $l(){}var Na=null,Ia=null;function Pa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ta=typeof setTimeout=="function"?setTimeout:void 0,Tg=typeof clearTimeout=="function"?clearTimeout:void 0,Jc=typeof Promise=="function"?Promise:void 0,Ag=typeof queueMicrotask=="function"?queueMicrotask:typeof Jc<"u"?function(e){return Jc.resolve(null).then(e).catch(Lg)}:Ta;function Lg(e){setTimeout(function(){throw e})}function Qs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),xi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);xi(t)}function lr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Zc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Oo=Math.random().toString(36).slice(2),xn="__reactFiber$"+Oo,Ei="__reactProps$"+Oo,On="__reactContainer$"+Oo,Aa="__reactEvents$"+Oo,Og="__reactListeners$"+Oo,Mg="__reactHandles$"+Oo;function Mr(e){var t=e[xn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[On]||n[xn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Zc(e);e!==null;){if(n=e[xn])return n;e=Zc(e)}return t}e=n,n=e.parentNode}return null}function Ui(e){return e=e[xn]||e[On],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function uo(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(q(33))}function as(e){return e[Ei]||null}var La=[],co=-1;function hr(e){return{current:e}}function Me(e){0>co||(e.current=La[co],La[co]=null,co--)}function Le(e,t){co++,La[co]=e.current,e.current=t}var fr={},mt=hr(fr),Pt=hr(!1),zr=fr;function bo(e,t){var n=e.type.contextTypes;if(!n)return fr;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Tt(e){return e=e.childContextTypes,e!=null}function Fl(){Me(Pt),Me(mt)}function ed(e,t,n){if(mt.current!==fr)throw Error(q(168));Le(mt,t),Le(Pt,n)}function Hf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(q(108,vm(e)||"Unknown",o));return We({},n,r)}function Bl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||fr,zr=mt.current,Le(mt,e),Le(Pt,Pt.current),!0}function td(e,t,n){var r=e.stateNode;if(!r)throw Error(q(169));n?(e=Hf(e,t,zr),r.__reactInternalMemoizedMergedChildContext=e,Me(Pt),Me(mt),Le(mt,e)):Me(Pt),Le(Pt,n)}var In=null,us=!1,Gs=!1;function Wf(e){In===null?In=[e]:In.push(e)}function jg(e){us=!0,Wf(e)}function mr(){if(!Gs&&In!==null){Gs=!0;var e=0,t=Ie;try{var n=In;for(Ie=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}In=null,us=!1}catch(o){throw In!==null&&(In=In.slice(e+1)),mf(pu,mr),o}finally{Ie=t,Gs=!1}}return null}var fo=[],po=0,Hl=null,Wl=0,qt=[],Vt=0,$r=null,Pn=1,Tn="";function Lr(e,t){fo[po++]=Wl,fo[po++]=Hl,Hl=e,Wl=t}function qf(e,t,n){qt[Vt++]=Pn,qt[Vt++]=Tn,qt[Vt++]=$r,$r=e;var r=Pn;e=Tn;var o=32-dn(r)-1;r&=~(1<<o),n+=1;var i=32-dn(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Pn=1<<32-dn(t)+o|n<<o|r,Tn=i+e}else Pn=1<<i|n<<o|r,Tn=e}function Su(e){e.return!==null&&(Lr(e,1),qf(e,1,0))}function Cu(e){for(;e===Hl;)Hl=fo[--po],fo[po]=null,Wl=fo[--po],fo[po]=null;for(;e===$r;)$r=qt[--Vt],qt[Vt]=null,Tn=qt[--Vt],qt[Vt]=null,Pn=qt[--Vt],qt[Vt]=null}var Dt=null,Ut=null,ze=!1,cn=null;function Vf(e,t){var n=Kt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function nd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Dt=e,Ut=lr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Dt=e,Ut=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=$r!==null?{id:Pn,overflow:Tn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Kt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Dt=e,Ut=null,!0):!1;default:return!1}}function Oa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ma(e){if(ze){var t=Ut;if(t){var n=t;if(!nd(e,t)){if(Oa(e))throw Error(q(418));t=lr(n.nextSibling);var r=Dt;t&&nd(e,t)?Vf(r,n):(e.flags=e.flags&-4097|2,ze=!1,Dt=e)}}else{if(Oa(e))throw Error(q(418));e.flags=e.flags&-4097|2,ze=!1,Dt=e}}}function rd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Dt=e}function dl(e){if(e!==Dt)return!1;if(!ze)return rd(e),ze=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Pa(e.type,e.memoizedProps)),t&&(t=Ut)){if(Oa(e))throw Kf(),Error(q(418));for(;t;)Vf(e,t),t=lr(t.nextSibling)}if(rd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(q(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ut=lr(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ut=null}}else Ut=Dt?lr(e.stateNode.nextSibling):null;return!0}function Kf(){for(var e=Ut;e;)e=lr(e.nextSibling)}function Eo(){Ut=Dt=null,ze=!1}function _u(e){cn===null?cn=[e]:cn.push(e)}var Ug=Un.ReactCurrentBatchConfig;function Zo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(q(309));var r=n.stateNode}if(!r)throw Error(q(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var u=o.refs;s===null?delete u[i]:u[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(q(284));if(!n._owner)throw Error(q(290,e))}return e}function fl(e,t){throw e=Object.prototype.toString.call(t),Error(q(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function od(e){var t=e._init;return t(e._payload)}function Qf(e){function t(h,p){if(e){var y=h.deletions;y===null?(h.deletions=[p],h.flags|=16):y.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=cr(h,p),h.index=0,h.sibling=null,h}function i(h,p,y){return h.index=y,e?(y=h.alternate,y!==null?(y=y.index,y<p?(h.flags|=2,p):y):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function u(h,p,y,k){return p===null||p.tag!==6?(p=na(y,h.mode,k),p.return=h,p):(p=o(p,y),p.return=h,p)}function d(h,p,y,k){var T=y.type;return T===io?v(h,p,y.props.children,k,y.key):p!==null&&(p.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Yn&&od(T)===p.type)?(k=o(p,y.props),k.ref=Zo(h,p,y),k.return=h,k):(k=Tl(y.type,y.key,y.props,null,h.mode,k),k.ref=Zo(h,p,y),k.return=h,k)}function f(h,p,y,k){return p===null||p.tag!==4||p.stateNode.containerInfo!==y.containerInfo||p.stateNode.implementation!==y.implementation?(p=ra(y,h.mode,k),p.return=h,p):(p=o(p,y.children||[]),p.return=h,p)}function v(h,p,y,k,T){return p===null||p.tag!==7?(p=Rr(y,h.mode,k,T),p.return=h,p):(p=o(p,y),p.return=h,p)}function N(h,p,y){if(typeof p=="string"&&p!==""||typeof p=="number")return p=na(""+p,h.mode,y),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case tl:return y=Tl(p.type,p.key,p.props,null,h.mode,y),y.ref=Zo(h,null,p),y.return=h,y;case oo:return p=ra(p,h.mode,y),p.return=h,p;case Yn:var k=p._init;return N(h,k(p._payload),y)}if(ri(p)||Qo(p))return p=Rr(p,h.mode,y,null),p.return=h,p;fl(h,p)}return null}function _(h,p,y,k){var T=p!==null?p.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return T!==null?null:u(h,p,""+y,k);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case tl:return y.key===T?d(h,p,y,k):null;case oo:return y.key===T?f(h,p,y,k):null;case Yn:return T=y._init,_(h,p,T(y._payload),k)}if(ri(y)||Qo(y))return T!==null?null:v(h,p,y,k,null);fl(h,y)}return null}function S(h,p,y,k,T){if(typeof k=="string"&&k!==""||typeof k=="number")return h=h.get(y)||null,u(p,h,""+k,T);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case tl:return h=h.get(k.key===null?y:k.key)||null,d(p,h,k,T);case oo:return h=h.get(k.key===null?y:k.key)||null,f(p,h,k,T);case Yn:var A=k._init;return S(h,p,y,A(k._payload),T)}if(ri(k)||Qo(k))return h=h.get(y)||null,v(p,h,k,T,null);fl(p,k)}return null}function w(h,p,y,k){for(var T=null,A=null,m=p,x=p=0,O=null;m!==null&&x<y.length;x++){m.index>x?(O=m,m=null):O=m.sibling;var U=_(h,m,y[x],k);if(U===null){m===null&&(m=O);break}e&&m&&U.alternate===null&&t(h,m),p=i(U,p,x),A===null?T=U:A.sibling=U,A=U,m=O}if(x===y.length)return n(h,m),ze&&Lr(h,x),T;if(m===null){for(;x<y.length;x++)m=N(h,y[x],k),m!==null&&(p=i(m,p,x),A===null?T=m:A.sibling=m,A=m);return ze&&Lr(h,x),T}for(m=r(h,m);x<y.length;x++)O=S(m,h,x,y[x],k),O!==null&&(e&&O.alternate!==null&&m.delete(O.key===null?x:O.key),p=i(O,p,x),A===null?T=O:A.sibling=O,A=O);return e&&m.forEach(function(ne){return t(h,ne)}),ze&&Lr(h,x),T}function C(h,p,y,k){var T=Qo(y);if(typeof T!="function")throw Error(q(150));if(y=T.call(y),y==null)throw Error(q(151));for(var A=T=null,m=p,x=p=0,O=null,U=y.next();m!==null&&!U.done;x++,U=y.next()){m.index>x?(O=m,m=null):O=m.sibling;var ne=_(h,m,U.value,k);if(ne===null){m===null&&(m=O);break}e&&m&&ne.alternate===null&&t(h,m),p=i(ne,p,x),A===null?T=ne:A.sibling=ne,A=ne,m=O}if(U.done)return n(h,m),ze&&Lr(h,x),T;if(m===null){for(;!U.done;x++,U=y.next())U=N(h,U.value,k),U!==null&&(p=i(U,p,x),A===null?T=U:A.sibling=U,A=U);return ze&&Lr(h,x),T}for(m=r(h,m);!U.done;x++,U=y.next())U=S(m,h,x,U.value,k),U!==null&&(e&&U.alternate!==null&&m.delete(U.key===null?x:U.key),p=i(U,p,x),A===null?T=U:A.sibling=U,A=U);return e&&m.forEach(function(ce){return t(h,ce)}),ze&&Lr(h,x),T}function P(h,p,y,k){if(typeof y=="object"&&y!==null&&y.type===io&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case tl:e:{for(var T=y.key,A=p;A!==null;){if(A.key===T){if(T=y.type,T===io){if(A.tag===7){n(h,A.sibling),p=o(A,y.props.children),p.return=h,h=p;break e}}else if(A.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Yn&&od(T)===A.type){n(h,A.sibling),p=o(A,y.props),p.ref=Zo(h,A,y),p.return=h,h=p;break e}n(h,A);break}else t(h,A);A=A.sibling}y.type===io?(p=Rr(y.props.children,h.mode,k,y.key),p.return=h,h=p):(k=Tl(y.type,y.key,y.props,null,h.mode,k),k.ref=Zo(h,p,y),k.return=h,h=k)}return s(h);case oo:e:{for(A=y.key;p!==null;){if(p.key===A)if(p.tag===4&&p.stateNode.containerInfo===y.containerInfo&&p.stateNode.implementation===y.implementation){n(h,p.sibling),p=o(p,y.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=ra(y,h.mode,k),p.return=h,h=p}return s(h);case Yn:return A=y._init,P(h,p,A(y._payload),k)}if(ri(y))return w(h,p,y,k);if(Qo(y))return C(h,p,y,k);fl(h,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,y),p.return=h,h=p):(n(h,p),p=na(y,h.mode,k),p.return=h,h=p),s(h)):n(h,p)}return P}var No=Qf(!0),Gf=Qf(!1),ql=hr(null),Vl=null,ho=null,bu=null;function Eu(){bu=ho=Vl=null}function Nu(e){var t=ql.current;Me(ql),e._currentValue=t}function ja(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xo(e,t){Vl=e,bu=ho=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(It=!0),e.firstContext=null)}function Yt(e){var t=e._currentValue;if(bu!==e)if(e={context:e,memoizedValue:t,next:null},ho===null){if(Vl===null)throw Error(q(308));ho=e,Vl.dependencies={lanes:0,firstContext:e}}else ho=ho.next=e;return t}var jr=null;function Iu(e){jr===null?jr=[e]:jr.push(e)}function Yf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Iu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Mn(e,r)}function Mn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Xn=!1;function Pu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Xf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function An(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function sr(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Se&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Mn(e,n)}return o=r.interleaved,o===null?(t.next=t,Iu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Mn(e,n)}function _l(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hu(e,n)}}function id(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Kl(e,t,n,r){var o=e.updateQueue;Xn=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,u=o.shared.pending;if(u!==null){o.shared.pending=null;var d=u,f=d.next;d.next=null,s===null?i=f:s.next=f,s=d;var v=e.alternate;v!==null&&(v=v.updateQueue,u=v.lastBaseUpdate,u!==s&&(u===null?v.firstBaseUpdate=f:u.next=f,v.lastBaseUpdate=d))}if(i!==null){var N=o.baseState;s=0,v=f=d=null,u=i;do{var _=u.lane,S=u.eventTime;if((r&_)===_){v!==null&&(v=v.next={eventTime:S,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var w=e,C=u;switch(_=t,S=n,C.tag){case 1:if(w=C.payload,typeof w=="function"){N=w.call(S,N,_);break e}N=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=C.payload,_=typeof w=="function"?w.call(S,N,_):w,_==null)break e;N=We({},N,_);break e;case 2:Xn=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,_=o.effects,_===null?o.effects=[u]:_.push(u))}else S={eventTime:S,lane:_,tag:u.tag,payload:u.payload,callback:u.callback,next:null},v===null?(f=v=S,d=N):v=v.next=S,s|=_;if(u=u.next,u===null){if(u=o.shared.pending,u===null)break;_=u,u=_.next,_.next=null,o.lastBaseUpdate=_,o.shared.pending=null}}while(1);if(v===null&&(d=N),o.baseState=d,o.firstBaseUpdate=f,o.lastBaseUpdate=v,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Br|=s,e.lanes=s,e.memoizedState=N}}function ld(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(q(191,o));o.call(r)}}}var Di={},Cn=hr(Di),Ni=hr(Di),Ii=hr(Di);function Ur(e){if(e===Di)throw Error(q(174));return e}function Tu(e,t){switch(Le(Ii,t),Le(Ni,e),Le(Cn,Di),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ma(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ma(t,e)}Me(Cn),Le(Cn,t)}function Io(){Me(Cn),Me(Ni),Me(Ii)}function Jf(e){Ur(Ii.current);var t=Ur(Cn.current),n=ma(t,e.type);t!==n&&(Le(Ni,e),Le(Cn,n))}function Au(e){Ni.current===e&&(Me(Cn),Me(Ni))}var Be=hr(0);function Ql(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ys=[];function Lu(){for(var e=0;e<Ys.length;e++)Ys[e]._workInProgressVersionPrimary=null;Ys.length=0}var bl=Un.ReactCurrentDispatcher,Xs=Un.ReactCurrentBatchConfig,Fr=0,He=null,Ze=null,ot=null,Gl=!1,fi=!1,Pi=0,Dg=0;function ft(){throw Error(q(321))}function Ou(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!pn(e[n],t[n]))return!1;return!0}function Mu(e,t,n,r,o,i){if(Fr=i,He=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,bl.current=e===null||e.memoizedState===null?Fg:Bg,e=n(r,o),fi){i=0;do{if(fi=!1,Pi=0,25<=i)throw Error(q(301));i+=1,ot=Ze=null,t.updateQueue=null,bl.current=Hg,e=n(r,o)}while(fi)}if(bl.current=Yl,t=Ze!==null&&Ze.next!==null,Fr=0,ot=Ze=He=null,Gl=!1,t)throw Error(q(300));return e}function ju(){var e=Pi!==0;return Pi=0,e}function kn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ot===null?He.memoizedState=ot=e:ot=ot.next=e,ot}function Xt(){if(Ze===null){var e=He.alternate;e=e!==null?e.memoizedState:null}else e=Ze.next;var t=ot===null?He.memoizedState:ot.next;if(t!==null)ot=t,Ze=e;else{if(e===null)throw Error(q(310));Ze=e,e={memoizedState:Ze.memoizedState,baseState:Ze.baseState,baseQueue:Ze.baseQueue,queue:Ze.queue,next:null},ot===null?He.memoizedState=ot=e:ot=ot.next=e}return ot}function Ti(e,t){return typeof t=="function"?t(e):t}function Js(e){var t=Xt(),n=t.queue;if(n===null)throw Error(q(311));n.lastRenderedReducer=e;var r=Ze,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var u=s=null,d=null,f=i;do{var v=f.lane;if((Fr&v)===v)d!==null&&(d=d.next={lane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),r=f.hasEagerState?f.eagerState:e(r,f.action);else{var N={lane:v,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null};d===null?(u=d=N,s=r):d=d.next=N,He.lanes|=v,Br|=v}f=f.next}while(f!==null&&f!==i);d===null?s=r:d.next=u,pn(r,t.memoizedState)||(It=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=d,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,He.lanes|=i,Br|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Zs(e){var t=Xt(),n=t.queue;if(n===null)throw Error(q(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);pn(i,t.memoizedState)||(It=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Zf(){}function ep(e,t){var n=He,r=Xt(),o=t(),i=!pn(r.memoizedState,o);if(i&&(r.memoizedState=o,It=!0),r=r.queue,Uu(rp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ot!==null&&ot.memoizedState.tag&1){if(n.flags|=2048,Ai(9,np.bind(null,n,r,o,t),void 0,null),it===null)throw Error(q(349));Fr&30||tp(n,t,o)}return o}function tp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=He.updateQueue,t===null?(t={lastEffect:null,stores:null},He.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function np(e,t,n,r){t.value=n,t.getSnapshot=r,op(t)&&ip(e)}function rp(e,t,n){return n(function(){op(t)&&ip(e)})}function op(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!pn(e,n)}catch{return!0}}function ip(e){var t=Mn(e,1);t!==null&&fn(t,e,1,-1)}function sd(e){var t=kn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ti,lastRenderedState:e},t.queue=e,e=e.dispatch=$g.bind(null,He,e),[t.memoizedState,e]}function Ai(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=He.updateQueue,t===null?(t={lastEffect:null,stores:null},He.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function lp(){return Xt().memoizedState}function El(e,t,n,r){var o=kn();He.flags|=e,o.memoizedState=Ai(1|t,n,void 0,r===void 0?null:r)}function cs(e,t,n,r){var o=Xt();r=r===void 0?null:r;var i=void 0;if(Ze!==null){var s=Ze.memoizedState;if(i=s.destroy,r!==null&&Ou(r,s.deps)){o.memoizedState=Ai(t,n,i,r);return}}He.flags|=e,o.memoizedState=Ai(1|t,n,i,r)}function ad(e,t){return El(8390656,8,e,t)}function Uu(e,t){return cs(2048,8,e,t)}function sp(e,t){return cs(4,2,e,t)}function ap(e,t){return cs(4,4,e,t)}function up(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function cp(e,t,n){return n=n!=null?n.concat([e]):null,cs(4,4,up.bind(null,t,e),n)}function Du(){}function dp(e,t){var n=Xt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ou(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function fp(e,t){var n=Xt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ou(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function pp(e,t,n){return Fr&21?(pn(n,t)||(n=vf(),He.lanes|=n,Br|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,It=!0),e.memoizedState=n)}function Rg(e,t){var n=Ie;Ie=n!==0&&4>n?n:4,e(!0);var r=Xs.transition;Xs.transition={};try{e(!1),t()}finally{Ie=n,Xs.transition=r}}function hp(){return Xt().memoizedState}function zg(e,t,n){var r=ur(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},mp(e))gp(t,n);else if(n=Yf(e,t,n,r),n!==null){var o=wt();fn(n,e,r,o),yp(n,t,r)}}function $g(e,t,n){var r=ur(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(mp(e))gp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,u=i(s,n);if(o.hasEagerState=!0,o.eagerState=u,pn(u,s)){var d=t.interleaved;d===null?(o.next=o,Iu(t)):(o.next=d.next,d.next=o),t.interleaved=o;return}}catch{}finally{}n=Yf(e,t,o,r),n!==null&&(o=wt(),fn(n,e,r,o),yp(n,t,r))}}function mp(e){var t=e.alternate;return e===He||t!==null&&t===He}function gp(e,t){fi=Gl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function yp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,hu(e,n)}}var Yl={readContext:Yt,useCallback:ft,useContext:ft,useEffect:ft,useImperativeHandle:ft,useInsertionEffect:ft,useLayoutEffect:ft,useMemo:ft,useReducer:ft,useRef:ft,useState:ft,useDebugValue:ft,useDeferredValue:ft,useTransition:ft,useMutableSource:ft,useSyncExternalStore:ft,useId:ft,unstable_isNewReconciler:!1},Fg={readContext:Yt,useCallback:function(e,t){return kn().memoizedState=[e,t===void 0?null:t],e},useContext:Yt,useEffect:ad,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,El(4194308,4,up.bind(null,t,e),n)},useLayoutEffect:function(e,t){return El(4194308,4,e,t)},useInsertionEffect:function(e,t){return El(4,2,e,t)},useMemo:function(e,t){var n=kn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=kn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=zg.bind(null,He,e),[r.memoizedState,e]},useRef:function(e){var t=kn();return e={current:e},t.memoizedState=e},useState:sd,useDebugValue:Du,useDeferredValue:function(e){return kn().memoizedState=e},useTransition:function(){var e=sd(!1),t=e[0];return e=Rg.bind(null,e[1]),kn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=He,o=kn();if(ze){if(n===void 0)throw Error(q(407));n=n()}else{if(n=t(),it===null)throw Error(q(349));Fr&30||tp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,ad(rp.bind(null,r,i,e),[e]),r.flags|=2048,Ai(9,np.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=kn(),t=it.identifierPrefix;if(ze){var n=Tn,r=Pn;n=(r&~(1<<32-dn(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Pi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Dg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Bg={readContext:Yt,useCallback:dp,useContext:Yt,useEffect:Uu,useImperativeHandle:cp,useInsertionEffect:sp,useLayoutEffect:ap,useMemo:fp,useReducer:Js,useRef:lp,useState:function(){return Js(Ti)},useDebugValue:Du,useDeferredValue:function(e){var t=Xt();return pp(t,Ze.memoizedState,e)},useTransition:function(){var e=Js(Ti)[0],t=Xt().memoizedState;return[e,t]},useMutableSource:Zf,useSyncExternalStore:ep,useId:hp,unstable_isNewReconciler:!1},Hg={readContext:Yt,useCallback:dp,useContext:Yt,useEffect:Uu,useImperativeHandle:cp,useInsertionEffect:sp,useLayoutEffect:ap,useMemo:fp,useReducer:Zs,useRef:lp,useState:function(){return Zs(Ti)},useDebugValue:Du,useDeferredValue:function(e){var t=Xt();return Ze===null?t.memoizedState=e:pp(t,Ze.memoizedState,e)},useTransition:function(){var e=Zs(Ti)[0],t=Xt().memoizedState;return[e,t]},useMutableSource:Zf,useSyncExternalStore:ep,useId:hp,unstable_isNewReconciler:!1};function an(e,t){if(e&&e.defaultProps){t=We({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ua(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:We({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ds={isMounted:function(e){return(e=e._reactInternals)?qr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=wt(),o=ur(e),i=An(r,o);i.payload=t,n!=null&&(i.callback=n),t=sr(e,i,o),t!==null&&(fn(t,e,o,r),_l(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=wt(),o=ur(e),i=An(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=sr(e,i,o),t!==null&&(fn(t,e,o,r),_l(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=wt(),r=ur(e),o=An(n,r);o.tag=2,t!=null&&(o.callback=t),t=sr(e,o,r),t!==null&&(fn(t,e,r,n),_l(t,e,r))}};function ud(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Ci(n,r)||!Ci(o,i):!0}function vp(e,t,n){var r=!1,o=fr,i=t.contextType;return typeof i=="object"&&i!==null?i=Yt(i):(o=Tt(t)?zr:mt.current,r=t.contextTypes,i=(r=r!=null)?bo(e,o):fr),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ds,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function cd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ds.enqueueReplaceState(t,t.state,null)}function Da(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Pu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Yt(i):(i=Tt(t)?zr:mt.current,o.context=bo(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ua(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ds.enqueueReplaceState(o,o.state,null),Kl(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Po(e,t){try{var n="",r=t;do n+=ym(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ea(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ra(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Wg=typeof WeakMap=="function"?WeakMap:Map;function wp(e,t,n){n=An(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Jl||(Jl=!0,Qa=r),Ra(e,t)},n}function kp(e,t,n){n=An(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ra(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ra(e,t),typeof r!="function"&&(ar===null?ar=new Set([this]):ar.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function dd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Wg;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=o0.bind(null,e,t,n),t.then(e,e))}function fd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function pd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=An(-1,1),t.tag=2,sr(n,t,1))),n.lanes|=1),e)}var qg=Un.ReactCurrentOwner,It=!1;function vt(e,t,n,r){t.child=e===null?Gf(t,null,n,r):No(t,e.child,n,r)}function hd(e,t,n,r,o){n=n.render;var i=t.ref;return xo(t,o),r=Mu(e,t,n,r,i,o),n=ju(),e!==null&&!It?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,jn(e,t,o)):(ze&&n&&Su(t),t.flags|=1,vt(e,t,r,o),t.child)}function md(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!qu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,xp(e,t,i,r,o)):(e=Tl(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Ci,n(s,r)&&e.ref===t.ref)return jn(e,t,o)}return t.flags|=1,e=cr(i,r),e.ref=t.ref,e.return=t,t.child=e}function xp(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Ci(i,r)&&e.ref===t.ref)if(It=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(It=!0);else return t.lanes=e.lanes,jn(e,t,o)}return za(e,t,n,r,o)}function Sp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Le(go,jt),jt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Le(go,jt),jt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Le(go,jt),jt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Le(go,jt),jt|=r;return vt(e,t,o,n),t.child}function Cp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function za(e,t,n,r,o){var i=Tt(n)?zr:mt.current;return i=bo(t,i),xo(t,o),n=Mu(e,t,n,r,i,o),r=ju(),e!==null&&!It?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,jn(e,t,o)):(ze&&r&&Su(t),t.flags|=1,vt(e,t,n,o),t.child)}function gd(e,t,n,r,o){if(Tt(n)){var i=!0;Bl(t)}else i=!1;if(xo(t,o),t.stateNode===null)Nl(e,t),vp(t,n,r),Da(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,u=t.memoizedProps;s.props=u;var d=s.context,f=n.contextType;typeof f=="object"&&f!==null?f=Yt(f):(f=Tt(n)?zr:mt.current,f=bo(t,f));var v=n.getDerivedStateFromProps,N=typeof v=="function"||typeof s.getSnapshotBeforeUpdate=="function";N||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u!==r||d!==f)&&cd(t,s,r,f),Xn=!1;var _=t.memoizedState;s.state=_,Kl(t,r,s,o),d=t.memoizedState,u!==r||_!==d||Pt.current||Xn?(typeof v=="function"&&(Ua(t,n,v,r),d=t.memoizedState),(u=Xn||ud(t,n,u,r,_,d,f))?(N||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=d),s.props=r,s.state=d,s.context=f,r=u):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Xf(e,t),u=t.memoizedProps,f=t.type===t.elementType?u:an(t.type,u),s.props=f,N=t.pendingProps,_=s.context,d=n.contextType,typeof d=="object"&&d!==null?d=Yt(d):(d=Tt(n)?zr:mt.current,d=bo(t,d));var S=n.getDerivedStateFromProps;(v=typeof S=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(u!==N||_!==d)&&cd(t,s,r,d),Xn=!1,_=t.memoizedState,s.state=_,Kl(t,r,s,o);var w=t.memoizedState;u!==N||_!==w||Pt.current||Xn?(typeof S=="function"&&(Ua(t,n,S,r),w=t.memoizedState),(f=Xn||ud(t,n,f,r,_,w,d)||!1)?(v||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,w,d),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,w,d)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||u===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),s.props=r,s.state=w,s.context=d,r=f):(typeof s.componentDidUpdate!="function"||u===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),r=!1)}return $a(e,t,n,r,i,o)}function $a(e,t,n,r,o,i){Cp(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&td(t,n,!1),jn(e,t,i);r=t.stateNode,qg.current=t;var u=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=No(t,e.child,null,i),t.child=No(t,null,u,i)):vt(e,t,u,i),t.memoizedState=r.state,o&&td(t,n,!0),t.child}function _p(e){var t=e.stateNode;t.pendingContext?ed(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ed(e,t.context,!1),Tu(e,t.containerInfo)}function yd(e,t,n,r,o){return Eo(),_u(o),t.flags|=256,vt(e,t,n,r),t.child}var Fa={dehydrated:null,treeContext:null,retryLane:0};function Ba(e){return{baseLanes:e,cachePool:null,transitions:null}}function bp(e,t,n){var r=t.pendingProps,o=Be.current,i=!1,s=(t.flags&128)!==0,u;if((u=s)||(u=e!==null&&e.memoizedState===null?!1:(o&2)!==0),u?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Le(Be,o&1),e===null)return Ma(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=hs(s,r,0,null),e=Rr(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ba(n),t.memoizedState=Fa,e):Ru(t,s));if(o=e.memoizedState,o!==null&&(u=o.dehydrated,u!==null))return Vg(e,t,s,r,u,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,u=o.sibling;var d={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=d,t.deletions=null):(r=cr(o,d),r.subtreeFlags=o.subtreeFlags&14680064),u!==null?i=cr(u,i):(i=Rr(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Ba(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Fa,r}return i=e.child,e=i.sibling,r=cr(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ru(e,t){return t=hs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function pl(e,t,n,r){return r!==null&&_u(r),No(t,e.child,null,n),e=Ru(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Vg(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=ea(Error(q(422))),pl(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=hs({mode:"visible",children:r.children},o,0,null),i=Rr(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&No(t,e.child,null,s),t.child.memoizedState=Ba(s),t.memoizedState=Fa,i);if(!(t.mode&1))return pl(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var u=r.dgst;return r=u,i=Error(q(419)),r=ea(i,r,void 0),pl(e,t,s,r)}if(u=(s&e.childLanes)!==0,It||u){if(r=it,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Mn(e,o),fn(r,e,o,-1))}return Wu(),r=ea(Error(q(421))),pl(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=i0.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ut=lr(o.nextSibling),Dt=t,ze=!0,cn=null,e!==null&&(qt[Vt++]=Pn,qt[Vt++]=Tn,qt[Vt++]=$r,Pn=e.id,Tn=e.overflow,$r=t),t=Ru(t,r.children),t.flags|=4096,t)}function vd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ja(e.return,t,n)}function ta(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Ep(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(vt(e,t,r.children,n),r=Be.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&vd(e,n,t);else if(e.tag===19)vd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Le(Be,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ql(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ta(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ql(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ta(t,!0,n,null,i);break;case"together":ta(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Nl(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Br|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(q(153));if(t.child!==null){for(e=t.child,n=cr(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=cr(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Kg(e,t,n){switch(t.tag){case 3:_p(t),Eo();break;case 5:Jf(t);break;case 1:Tt(t.type)&&Bl(t);break;case 4:Tu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Le(ql,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Le(Be,Be.current&1),t.flags|=128,null):n&t.child.childLanes?bp(e,t,n):(Le(Be,Be.current&1),e=jn(e,t,n),e!==null?e.sibling:null);Le(Be,Be.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ep(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Le(Be,Be.current),r)break;return null;case 22:case 23:return t.lanes=0,Sp(e,t,n)}return jn(e,t,n)}var Np,Ha,Ip,Pp;Np=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ha=function(){};Ip=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Ur(Cn.current);var i=null;switch(n){case"input":o=da(e,o),r=da(e,r),i=[];break;case"select":o=We({},o,{value:void 0}),r=We({},r,{value:void 0}),i=[];break;case"textarea":o=ha(e,o),r=ha(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=$l)}ga(n,r);var s;n=null;for(f in o)if(!r.hasOwnProperty(f)&&o.hasOwnProperty(f)&&o[f]!=null)if(f==="style"){var u=o[f];for(s in u)u.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else f!=="dangerouslySetInnerHTML"&&f!=="children"&&f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&f!=="autoFocus"&&(gi.hasOwnProperty(f)?i||(i=[]):(i=i||[]).push(f,null));for(f in r){var d=r[f];if(u=o?.[f],r.hasOwnProperty(f)&&d!==u&&(d!=null||u!=null))if(f==="style")if(u){for(s in u)!u.hasOwnProperty(s)||d&&d.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in d)d.hasOwnProperty(s)&&u[s]!==d[s]&&(n||(n={}),n[s]=d[s])}else n||(i||(i=[]),i.push(f,n)),n=d;else f==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,u=u?u.__html:void 0,d!=null&&u!==d&&(i=i||[]).push(f,d)):f==="children"?typeof d!="string"&&typeof d!="number"||(i=i||[]).push(f,""+d):f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&(gi.hasOwnProperty(f)?(d!=null&&f==="onScroll"&&Oe("scroll",e),i||u===d||(i=[])):(i=i||[]).push(f,d))}n&&(i=i||[]).push("style",n);var f=i;(t.updateQueue=f)&&(t.flags|=4)}};Pp=function(e,t,n,r){n!==r&&(t.flags|=4)};function ei(e,t){if(!ze)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qg(e,t,n){var r=t.pendingProps;switch(Cu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pt(t),null;case 1:return Tt(t.type)&&Fl(),pt(t),null;case 3:return r=t.stateNode,Io(),Me(Pt),Me(mt),Lu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(dl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,cn!==null&&(Xa(cn),cn=null))),Ha(e,t),pt(t),null;case 5:Au(t);var o=Ur(Ii.current);if(n=t.type,e!==null&&t.stateNode!=null)Ip(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(q(166));return pt(t),null}if(e=Ur(Cn.current),dl(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[xn]=t,r[Ei]=i,e=(t.mode&1)!==0,n){case"dialog":Oe("cancel",r),Oe("close",r);break;case"iframe":case"object":case"embed":Oe("load",r);break;case"video":case"audio":for(o=0;o<ii.length;o++)Oe(ii[o],r);break;case"source":Oe("error",r);break;case"img":case"image":case"link":Oe("error",r),Oe("load",r);break;case"details":Oe("toggle",r);break;case"input":Nc(r,i),Oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Oe("invalid",r);break;case"textarea":Pc(r,i),Oe("invalid",r)}ga(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var u=i[s];s==="children"?typeof u=="string"?r.textContent!==u&&(i.suppressHydrationWarning!==!0&&cl(r.textContent,u,e),o=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(i.suppressHydrationWarning!==!0&&cl(r.textContent,u,e),o=["children",""+u]):gi.hasOwnProperty(s)&&u!=null&&s==="onScroll"&&Oe("scroll",r)}switch(n){case"input":nl(r),Ic(r,i,!0);break;case"textarea":nl(r),Tc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=$l)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=nf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[xn]=t,e[Ei]=r,Np(e,t,!1,!1),t.stateNode=e;e:{switch(s=ya(n,r),n){case"dialog":Oe("cancel",e),Oe("close",e),o=r;break;case"iframe":case"object":case"embed":Oe("load",e),o=r;break;case"video":case"audio":for(o=0;o<ii.length;o++)Oe(ii[o],e);o=r;break;case"source":Oe("error",e),o=r;break;case"img":case"image":case"link":Oe("error",e),Oe("load",e),o=r;break;case"details":Oe("toggle",e),o=r;break;case"input":Nc(e,r),o=da(e,r),Oe("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=We({},r,{value:void 0}),Oe("invalid",e);break;case"textarea":Pc(e,r),o=ha(e,r),Oe("invalid",e);break;default:o=r}ga(n,o),u=o;for(i in u)if(u.hasOwnProperty(i)){var d=u[i];i==="style"?lf(e,d):i==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,d!=null&&rf(e,d)):i==="children"?typeof d=="string"?(n!=="textarea"||d!=="")&&yi(e,d):typeof d=="number"&&yi(e,""+d):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(gi.hasOwnProperty(i)?d!=null&&i==="onScroll"&&Oe("scroll",e):d!=null&&au(e,i,d,s))}switch(n){case"input":nl(e),Ic(e,r,!1);break;case"textarea":nl(e),Tc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+dr(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?yo(e,!!r.multiple,i,!1):r.defaultValue!=null&&yo(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=$l)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pt(t),null;case 6:if(e&&t.stateNode!=null)Pp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(q(166));if(n=Ur(Ii.current),Ur(Cn.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[xn]=t,(i=r.nodeValue!==n)&&(e=Dt,e!==null))switch(e.tag){case 3:cl(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&cl(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[xn]=t,t.stateNode=r}return pt(t),null;case 13:if(Me(Be),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ze&&Ut!==null&&t.mode&1&&!(t.flags&128))Kf(),Eo(),t.flags|=98560,i=!1;else if(i=dl(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(q(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(q(317));i[xn]=t}else Eo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pt(t),i=!1}else cn!==null&&(Xa(cn),cn=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Be.current&1?et===0&&(et=3):Wu())),t.updateQueue!==null&&(t.flags|=4),pt(t),null);case 4:return Io(),Ha(e,t),e===null&&_i(t.stateNode.containerInfo),pt(t),null;case 10:return Nu(t.type._context),pt(t),null;case 17:return Tt(t.type)&&Fl(),pt(t),null;case 19:if(Me(Be),i=t.memoizedState,i===null)return pt(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)ei(i,!1);else{if(et!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Ql(e),s!==null){for(t.flags|=128,ei(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Le(Be,Be.current&1|2),t.child}e=e.sibling}i.tail!==null&&Qe()>To&&(t.flags|=128,r=!0,ei(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ql(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ei(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ze)return pt(t),null}else 2*Qe()-i.renderingStartTime>To&&n!==1073741824&&(t.flags|=128,r=!0,ei(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Qe(),t.sibling=null,n=Be.current,Le(Be,r?n&1|2:n&1),t):(pt(t),null);case 22:case 23:return Hu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?jt&1073741824&&(pt(t),t.subtreeFlags&6&&(t.flags|=8192)):pt(t),null;case 24:return null;case 25:return null}throw Error(q(156,t.tag))}function Gg(e,t){switch(Cu(t),t.tag){case 1:return Tt(t.type)&&Fl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Io(),Me(Pt),Me(mt),Lu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Au(t),null;case 13:if(Me(Be),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(q(340));Eo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Me(Be),null;case 4:return Io(),null;case 10:return Nu(t.type._context),null;case 22:case 23:return Hu(),null;case 24:return null;default:return null}}var hl=!1,ht=!1,Yg=typeof WeakSet=="function"?WeakSet:Set,te=null;function mo(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ve(e,t,r)}else n.current=null}function Wa(e,t,n){try{n()}catch(r){Ve(e,t,r)}}var wd=!1;function Xg(e,t){if(Na=Dl,e=Mf(),xu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,u=-1,d=-1,f=0,v=0,N=e,_=null;t:for(;;){for(var S;N!==n||o!==0&&N.nodeType!==3||(u=s+o),N!==i||r!==0&&N.nodeType!==3||(d=s+r),N.nodeType===3&&(s+=N.nodeValue.length),(S=N.firstChild)!==null;)_=N,N=S;for(;;){if(N===e)break t;if(_===n&&++f===o&&(u=s),_===i&&++v===r&&(d=s),(S=N.nextSibling)!==null)break;N=_,_=N.parentNode}N=S}n=u===-1||d===-1?null:{start:u,end:d}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ia={focusedElem:e,selectionRange:n},Dl=!1,te=t;te!==null;)if(t=te,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,te=e;else for(;te!==null;){t=te;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var C=w.memoizedProps,P=w.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?C:an(t.type,C),P);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(q(163))}}catch(k){Ve(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,te=e;break}te=t.return}return w=wd,wd=!1,w}function pi(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Wa(t,n,i)}o=o.next}while(o!==r)}}function fs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function qa(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Tp(e){var t=e.alternate;t!==null&&(e.alternate=null,Tp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[xn],delete t[Ei],delete t[Aa],delete t[Og],delete t[Mg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ap(e){return e.tag===5||e.tag===3||e.tag===4}function kd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ap(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Va(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=$l));else if(r!==4&&(e=e.child,e!==null))for(Va(e,t,n),e=e.sibling;e!==null;)Va(e,t,n),e=e.sibling}function Ka(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ka(e,t,n),e=e.sibling;e!==null;)Ka(e,t,n),e=e.sibling}var at=null,un=!1;function Gn(e,t,n){for(n=n.child;n!==null;)Lp(e,t,n),n=n.sibling}function Lp(e,t,n){if(Sn&&typeof Sn.onCommitFiberUnmount=="function")try{Sn.onCommitFiberUnmount(os,n)}catch{}switch(n.tag){case 5:ht||mo(n,t);case 6:var r=at,o=un;at=null,Gn(e,t,n),at=r,un=o,at!==null&&(un?(e=at,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):at.removeChild(n.stateNode));break;case 18:at!==null&&(un?(e=at,n=n.stateNode,e.nodeType===8?Qs(e.parentNode,n):e.nodeType===1&&Qs(e,n),xi(e)):Qs(at,n.stateNode));break;case 4:r=at,o=un,at=n.stateNode.containerInfo,un=!0,Gn(e,t,n),at=r,un=o;break;case 0:case 11:case 14:case 15:if(!ht&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Wa(n,t,s),o=o.next}while(o!==r)}Gn(e,t,n);break;case 1:if(!ht&&(mo(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(u){Ve(n,t,u)}Gn(e,t,n);break;case 21:Gn(e,t,n);break;case 22:n.mode&1?(ht=(r=ht)||n.memoizedState!==null,Gn(e,t,n),ht=r):Gn(e,t,n);break;default:Gn(e,t,n)}}function xd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Yg),t.forEach(function(r){var o=l0.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function sn(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,u=s;e:for(;u!==null;){switch(u.tag){case 5:at=u.stateNode,un=!1;break e;case 3:at=u.stateNode.containerInfo,un=!0;break e;case 4:at=u.stateNode.containerInfo,un=!0;break e}u=u.return}if(at===null)throw Error(q(160));Lp(i,s,o),at=null,un=!1;var d=o.alternate;d!==null&&(d.return=null),o.return=null}catch(f){Ve(o,t,f)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Op(t,e),t=t.sibling}function Op(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(sn(t,e),wn(e),r&4){try{pi(3,e,e.return),fs(3,e)}catch(C){Ve(e,e.return,C)}try{pi(5,e,e.return)}catch(C){Ve(e,e.return,C)}}break;case 1:sn(t,e),wn(e),r&512&&n!==null&&mo(n,n.return);break;case 5:if(sn(t,e),wn(e),r&512&&n!==null&&mo(n,n.return),e.flags&32){var o=e.stateNode;try{yi(o,"")}catch(C){Ve(e,e.return,C)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,u=e.type,d=e.updateQueue;if(e.updateQueue=null,d!==null)try{u==="input"&&i.type==="radio"&&i.name!=null&&ef(o,i),ya(u,s);var f=ya(u,i);for(s=0;s<d.length;s+=2){var v=d[s],N=d[s+1];v==="style"?lf(o,N):v==="dangerouslySetInnerHTML"?rf(o,N):v==="children"?yi(o,N):au(o,v,N,f)}switch(u){case"input":fa(o,i);break;case"textarea":tf(o,i);break;case"select":var _=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var S=i.value;S!=null?yo(o,!!i.multiple,S,!1):_!==!!i.multiple&&(i.defaultValue!=null?yo(o,!!i.multiple,i.defaultValue,!0):yo(o,!!i.multiple,i.multiple?[]:"",!1))}o[Ei]=i}catch(C){Ve(e,e.return,C)}}break;case 6:if(sn(t,e),wn(e),r&4){if(e.stateNode===null)throw Error(q(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(C){Ve(e,e.return,C)}}break;case 3:if(sn(t,e),wn(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{xi(t.containerInfo)}catch(C){Ve(e,e.return,C)}break;case 4:sn(t,e),wn(e);break;case 13:sn(t,e),wn(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Fu=Qe())),r&4&&xd(e);break;case 22:if(v=n!==null&&n.memoizedState!==null,e.mode&1?(ht=(f=ht)||v,sn(t,e),ht=f):sn(t,e),wn(e),r&8192){if(f=e.memoizedState!==null,(e.stateNode.isHidden=f)&&!v&&e.mode&1)for(te=e,v=e.child;v!==null;){for(N=te=v;te!==null;){switch(_=te,S=_.child,_.tag){case 0:case 11:case 14:case 15:pi(4,_,_.return);break;case 1:mo(_,_.return);var w=_.stateNode;if(typeof w.componentWillUnmount=="function"){r=_,n=_.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(C){Ve(r,n,C)}}break;case 5:mo(_,_.return);break;case 22:if(_.memoizedState!==null){Cd(N);continue}}S!==null?(S.return=_,te=S):Cd(N)}v=v.sibling}e:for(v=null,N=e;;){if(N.tag===5){if(v===null){v=N;try{o=N.stateNode,f?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(u=N.stateNode,d=N.memoizedProps.style,s=d!=null&&d.hasOwnProperty("display")?d.display:null,u.style.display=of("display",s))}catch(C){Ve(e,e.return,C)}}}else if(N.tag===6){if(v===null)try{N.stateNode.nodeValue=f?"":N.memoizedProps}catch(C){Ve(e,e.return,C)}}else if((N.tag!==22&&N.tag!==23||N.memoizedState===null||N===e)&&N.child!==null){N.child.return=N,N=N.child;continue}if(N===e)break e;for(;N.sibling===null;){if(N.return===null||N.return===e)break e;v===N&&(v=null),N=N.return}v===N&&(v=null),N.sibling.return=N.return,N=N.sibling}}break;case 19:sn(t,e),wn(e),r&4&&xd(e);break;case 21:break;default:sn(t,e),wn(e)}}function wn(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ap(n)){var r=n;break e}n=n.return}throw Error(q(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(yi(o,""),r.flags&=-33);var i=kd(e);Ka(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,u=kd(e);Va(e,u,s);break;default:throw Error(q(161))}}catch(d){Ve(e,e.return,d)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Jg(e,t,n){te=e,Mp(e)}function Mp(e,t,n){for(var r=(e.mode&1)!==0;te!==null;){var o=te,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||hl;if(!s){var u=o.alternate,d=u!==null&&u.memoizedState!==null||ht;u=hl;var f=ht;if(hl=s,(ht=d)&&!f)for(te=o;te!==null;)s=te,d=s.child,s.tag===22&&s.memoizedState!==null?_d(o):d!==null?(d.return=s,te=d):_d(o);for(;i!==null;)te=i,Mp(i),i=i.sibling;te=o,hl=u,ht=f}Sd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,te=i):Sd(e)}}function Sd(e){for(;te!==null;){var t=te;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ht||fs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ht)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:an(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ld(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ld(t,s,n)}break;case 5:var u=t.stateNode;if(n===null&&t.flags&4){n=u;var d=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":d.autoFocus&&n.focus();break;case"img":d.src&&(n.src=d.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var f=t.alternate;if(f!==null){var v=f.memoizedState;if(v!==null){var N=v.dehydrated;N!==null&&xi(N)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(q(163))}ht||t.flags&512&&qa(t)}catch(_){Ve(t,t.return,_)}}if(t===e){te=null;break}if(n=t.sibling,n!==null){n.return=t.return,te=n;break}te=t.return}}function Cd(e){for(;te!==null;){var t=te;if(t===e){te=null;break}var n=t.sibling;if(n!==null){n.return=t.return,te=n;break}te=t.return}}function _d(e){for(;te!==null;){var t=te;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{fs(4,t)}catch(d){Ve(t,n,d)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(d){Ve(t,o,d)}}var i=t.return;try{qa(t)}catch(d){Ve(t,i,d)}break;case 5:var s=t.return;try{qa(t)}catch(d){Ve(t,s,d)}}}catch(d){Ve(t,t.return,d)}if(t===e){te=null;break}var u=t.sibling;if(u!==null){u.return=t.return,te=u;break}te=t.return}}var Zg=Math.ceil,Xl=Un.ReactCurrentDispatcher,zu=Un.ReactCurrentOwner,Qt=Un.ReactCurrentBatchConfig,Se=0,it=null,Je=null,ut=0,jt=0,go=hr(0),et=0,Li=null,Br=0,ps=0,$u=0,hi=null,Nt=null,Fu=0,To=1/0,Nn=null,Jl=!1,Qa=null,ar=null,ml=!1,nr=null,Zl=0,mi=0,Ga=null,Il=-1,Pl=0;function wt(){return Se&6?Qe():Il!==-1?Il:Il=Qe()}function ur(e){return e.mode&1?Se&2&&ut!==0?ut&-ut:Ug.transition!==null?(Pl===0&&(Pl=vf()),Pl):(e=Ie,e!==0||(e=window.event,e=e===void 0?16:bf(e.type)),e):1}function fn(e,t,n,r){if(50<mi)throw mi=0,Ga=null,Error(q(185));Mi(e,n,r),(!(Se&2)||e!==it)&&(e===it&&(!(Se&2)&&(ps|=n),et===4&&Zn(e,ut)),At(e,r),n===1&&Se===0&&!(t.mode&1)&&(To=Qe()+500,us&&mr()))}function At(e,t){var n=e.callbackNode;Um(e,t);var r=Ul(e,e===it?ut:0);if(r===0)n!==null&&Oc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Oc(n),t===1)e.tag===0?jg(bd.bind(null,e)):Wf(bd.bind(null,e)),Ag(function(){!(Se&6)&&mr()}),n=null;else{switch(wf(r)){case 1:n=pu;break;case 4:n=gf;break;case 16:n=jl;break;case 536870912:n=yf;break;default:n=jl}n=Bp(n,jp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function jp(e,t){if(Il=-1,Pl=0,Se&6)throw Error(q(327));var n=e.callbackNode;if(So()&&e.callbackNode!==n)return null;var r=Ul(e,e===it?ut:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=es(e,r);else{t=r;var o=Se;Se|=2;var i=Dp();(it!==e||ut!==t)&&(Nn=null,To=Qe()+500,Dr(e,t));do try{n0();break}catch(u){Up(e,u)}while(1);Eu(),Xl.current=i,Se=o,Je!==null?t=0:(it=null,ut=0,t=et)}if(t!==0){if(t===2&&(o=Sa(e),o!==0&&(r=o,t=Ya(e,o))),t===1)throw n=Li,Dr(e,0),Zn(e,r),At(e,Qe()),n;if(t===6)Zn(e,r);else{if(o=e.current.alternate,!(r&30)&&!e0(o)&&(t=es(e,r),t===2&&(i=Sa(e),i!==0&&(r=i,t=Ya(e,i))),t===1))throw n=Li,Dr(e,0),Zn(e,r),At(e,Qe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(q(345));case 2:Or(e,Nt,Nn);break;case 3:if(Zn(e,r),(r&130023424)===r&&(t=Fu+500-Qe(),10<t)){if(Ul(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){wt(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ta(Or.bind(null,e,Nt,Nn),t);break}Or(e,Nt,Nn);break;case 4:if(Zn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-dn(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=Qe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Zg(r/1960))-r,10<r){e.timeoutHandle=Ta(Or.bind(null,e,Nt,Nn),r);break}Or(e,Nt,Nn);break;case 5:Or(e,Nt,Nn);break;default:throw Error(q(329))}}}return At(e,Qe()),e.callbackNode===n?jp.bind(null,e):null}function Ya(e,t){var n=hi;return e.current.memoizedState.isDehydrated&&(Dr(e,t).flags|=256),e=es(e,t),e!==2&&(t=Nt,Nt=n,t!==null&&Xa(t)),e}function Xa(e){Nt===null?Nt=e:Nt.push.apply(Nt,e)}function e0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!pn(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Zn(e,t){for(t&=~$u,t&=~ps,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-dn(t),r=1<<n;e[n]=-1,t&=~r}}function bd(e){if(Se&6)throw Error(q(327));So();var t=Ul(e,0);if(!(t&1))return At(e,Qe()),null;var n=es(e,t);if(e.tag!==0&&n===2){var r=Sa(e);r!==0&&(t=r,n=Ya(e,r))}if(n===1)throw n=Li,Dr(e,0),Zn(e,t),At(e,Qe()),n;if(n===6)throw Error(q(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Or(e,Nt,Nn),At(e,Qe()),null}function Bu(e,t){var n=Se;Se|=1;try{return e(t)}finally{Se=n,Se===0&&(To=Qe()+500,us&&mr())}}function Hr(e){nr!==null&&nr.tag===0&&!(Se&6)&&So();var t=Se;Se|=1;var n=Qt.transition,r=Ie;try{if(Qt.transition=null,Ie=1,e)return e()}finally{Ie=r,Qt.transition=n,Se=t,!(Se&6)&&mr()}}function Hu(){jt=go.current,Me(go)}function Dr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Tg(n)),Je!==null)for(n=Je.return;n!==null;){var r=n;switch(Cu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Fl();break;case 3:Io(),Me(Pt),Me(mt),Lu();break;case 5:Au(r);break;case 4:Io();break;case 13:Me(Be);break;case 19:Me(Be);break;case 10:Nu(r.type._context);break;case 22:case 23:Hu()}n=n.return}if(it=e,Je=e=cr(e.current,null),ut=jt=t,et=0,Li=null,$u=ps=Br=0,Nt=hi=null,jr!==null){for(t=0;t<jr.length;t++)if(n=jr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}jr=null}return e}function Up(e,t){do{var n=Je;try{if(Eu(),bl.current=Yl,Gl){for(var r=He.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Gl=!1}if(Fr=0,ot=Ze=He=null,fi=!1,Pi=0,zu.current=null,n===null||n.return===null){et=1,Li=t,Je=null;break}e:{var i=e,s=n.return,u=n,d=t;if(t=ut,u.flags|=32768,d!==null&&typeof d=="object"&&typeof d.then=="function"){var f=d,v=u,N=v.tag;if(!(v.mode&1)&&(N===0||N===11||N===15)){var _=v.alternate;_?(v.updateQueue=_.updateQueue,v.memoizedState=_.memoizedState,v.lanes=_.lanes):(v.updateQueue=null,v.memoizedState=null)}var S=fd(s);if(S!==null){S.flags&=-257,pd(S,s,u,i,t),S.mode&1&&dd(i,f,t),t=S,d=f;var w=t.updateQueue;if(w===null){var C=new Set;C.add(d),t.updateQueue=C}else w.add(d);break e}else{if(!(t&1)){dd(i,f,t),Wu();break e}d=Error(q(426))}}else if(ze&&u.mode&1){var P=fd(s);if(P!==null){!(P.flags&65536)&&(P.flags|=256),pd(P,s,u,i,t),_u(Po(d,u));break e}}i=d=Po(d,u),et!==4&&(et=2),hi===null?hi=[i]:hi.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=wp(i,d,t);id(i,h);break e;case 1:u=d;var p=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(ar===null||!ar.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var k=kp(i,u,t);id(i,k);break e}}i=i.return}while(i!==null)}zp(n)}catch(T){t=T,Je===n&&n!==null&&(Je=n=n.return);continue}break}while(1)}function Dp(){var e=Xl.current;return Xl.current=Yl,e===null?Yl:e}function Wu(){(et===0||et===3||et===2)&&(et=4),it===null||!(Br&268435455)&&!(ps&268435455)||Zn(it,ut)}function es(e,t){var n=Se;Se|=2;var r=Dp();(it!==e||ut!==t)&&(Nn=null,Dr(e,t));do try{t0();break}catch(o){Up(e,o)}while(1);if(Eu(),Se=n,Xl.current=r,Je!==null)throw Error(q(261));return it=null,ut=0,et}function t0(){for(;Je!==null;)Rp(Je)}function n0(){for(;Je!==null&&!Nm();)Rp(Je)}function Rp(e){var t=Fp(e.alternate,e,jt);e.memoizedProps=e.pendingProps,t===null?zp(e):Je=t,zu.current=null}function zp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Gg(n,t),n!==null){n.flags&=32767,Je=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{et=6,Je=null;return}}else if(n=Qg(n,t,jt),n!==null){Je=n;return}if(t=t.sibling,t!==null){Je=t;return}Je=t=e}while(t!==null);et===0&&(et=5)}function Or(e,t,n){var r=Ie,o=Qt.transition;try{Qt.transition=null,Ie=1,r0(e,t,n,r)}finally{Qt.transition=o,Ie=r}return null}function r0(e,t,n,r){do So();while(nr!==null);if(Se&6)throw Error(q(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(q(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Dm(e,i),e===it&&(Je=it=null,ut=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ml||(ml=!0,Bp(jl,function(){return So(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Qt.transition,Qt.transition=null;var s=Ie;Ie=1;var u=Se;Se|=4,zu.current=null,Xg(e,n),Op(n,e),Cg(Ia),Dl=!!Na,Ia=Na=null,e.current=n,Jg(n),Im(),Se=u,Ie=s,Qt.transition=i}else e.current=n;if(ml&&(ml=!1,nr=e,Zl=o),i=e.pendingLanes,i===0&&(ar=null),Am(n.stateNode),At(e,Qe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Jl)throw Jl=!1,e=Qa,Qa=null,e;return Zl&1&&e.tag!==0&&So(),i=e.pendingLanes,i&1?e===Ga?mi++:(mi=0,Ga=e):mi=0,mr(),null}function So(){if(nr!==null){var e=wf(Zl),t=Qt.transition,n=Ie;try{if(Qt.transition=null,Ie=16>e?16:e,nr===null)var r=!1;else{if(e=nr,nr=null,Zl=0,Se&6)throw Error(q(331));var o=Se;for(Se|=4,te=e.current;te!==null;){var i=te,s=i.child;if(te.flags&16){var u=i.deletions;if(u!==null){for(var d=0;d<u.length;d++){var f=u[d];for(te=f;te!==null;){var v=te;switch(v.tag){case 0:case 11:case 15:pi(8,v,i)}var N=v.child;if(N!==null)N.return=v,te=N;else for(;te!==null;){v=te;var _=v.sibling,S=v.return;if(Tp(v),v===f){te=null;break}if(_!==null){_.return=S,te=_;break}te=S}}}var w=i.alternate;if(w!==null){var C=w.child;if(C!==null){w.child=null;do{var P=C.sibling;C.sibling=null,C=P}while(C!==null)}}te=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,te=s;else e:for(;te!==null;){if(i=te,i.flags&2048)switch(i.tag){case 0:case 11:case 15:pi(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,te=h;break e}te=i.return}}var p=e.current;for(te=p;te!==null;){s=te;var y=s.child;if(s.subtreeFlags&2064&&y!==null)y.return=s,te=y;else e:for(s=p;te!==null;){if(u=te,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:fs(9,u)}}catch(T){Ve(u,u.return,T)}if(u===s){te=null;break e}var k=u.sibling;if(k!==null){k.return=u.return,te=k;break e}te=u.return}}if(Se=o,mr(),Sn&&typeof Sn.onPostCommitFiberRoot=="function")try{Sn.onPostCommitFiberRoot(os,e)}catch{}r=!0}return r}finally{Ie=n,Qt.transition=t}}return!1}function Ed(e,t,n){t=Po(n,t),t=wp(e,t,1),e=sr(e,t,1),t=wt(),e!==null&&(Mi(e,1,t),At(e,t))}function Ve(e,t,n){if(e.tag===3)Ed(e,e,n);else for(;t!==null;){if(t.tag===3){Ed(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ar===null||!ar.has(r))){e=Po(n,e),e=kp(t,e,1),t=sr(t,e,1),e=wt(),t!==null&&(Mi(t,1,e),At(t,e));break}}t=t.return}}function o0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=wt(),e.pingedLanes|=e.suspendedLanes&n,it===e&&(ut&n)===n&&(et===4||et===3&&(ut&130023424)===ut&&500>Qe()-Fu?Dr(e,0):$u|=n),At(e,t)}function $p(e,t){t===0&&(e.mode&1?(t=il,il<<=1,!(il&130023424)&&(il=4194304)):t=1);var n=wt();e=Mn(e,t),e!==null&&(Mi(e,t,n),At(e,n))}function i0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),$p(e,n)}function l0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(q(314))}r!==null&&r.delete(t),$p(e,n)}var Fp;Fp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pt.current)It=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return It=!1,Kg(e,t,n);It=!!(e.flags&131072)}else It=!1,ze&&t.flags&1048576&&qf(t,Wl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Nl(e,t),e=t.pendingProps;var o=bo(t,mt.current);xo(t,n),o=Mu(null,t,r,e,o,n);var i=ju();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Tt(r)?(i=!0,Bl(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Pu(t),o.updater=ds,t.stateNode=o,o._reactInternals=t,Da(t,r,e,n),t=$a(null,t,r,!0,i,n)):(t.tag=0,ze&&i&&Su(t),vt(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Nl(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=a0(r),e=an(r,e),o){case 0:t=za(null,t,r,e,n);break e;case 1:t=gd(null,t,r,e,n);break e;case 11:t=hd(null,t,r,e,n);break e;case 14:t=md(null,t,r,an(r.type,e),n);break e}throw Error(q(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:an(r,o),za(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:an(r,o),gd(e,t,r,o,n);case 3:e:{if(_p(t),e===null)throw Error(q(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Xf(e,t),Kl(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Po(Error(q(423)),t),t=yd(e,t,r,n,o);break e}else if(r!==o){o=Po(Error(q(424)),t),t=yd(e,t,r,n,o);break e}else for(Ut=lr(t.stateNode.containerInfo.firstChild),Dt=t,ze=!0,cn=null,n=Gf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Eo(),r===o){t=jn(e,t,n);break e}vt(e,t,r,n)}t=t.child}return t;case 5:return Jf(t),e===null&&Ma(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Pa(r,o)?s=null:i!==null&&Pa(r,i)&&(t.flags|=32),Cp(e,t),vt(e,t,s,n),t.child;case 6:return e===null&&Ma(t),null;case 13:return bp(e,t,n);case 4:return Tu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=No(t,null,r,n):vt(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:an(r,o),hd(e,t,r,o,n);case 7:return vt(e,t,t.pendingProps,n),t.child;case 8:return vt(e,t,t.pendingProps.children,n),t.child;case 12:return vt(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,Le(ql,r._currentValue),r._currentValue=s,i!==null)if(pn(i.value,s)){if(i.children===o.children&&!Pt.current){t=jn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var u=i.dependencies;if(u!==null){s=i.child;for(var d=u.firstContext;d!==null;){if(d.context===r){if(i.tag===1){d=An(-1,n&-n),d.tag=2;var f=i.updateQueue;if(f!==null){f=f.shared;var v=f.pending;v===null?d.next=d:(d.next=v.next,v.next=d),f.pending=d}}i.lanes|=n,d=i.alternate,d!==null&&(d.lanes|=n),ja(i.return,n,t),u.lanes|=n;break}d=d.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(q(341));s.lanes|=n,u=s.alternate,u!==null&&(u.lanes|=n),ja(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}vt(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,xo(t,n),o=Yt(o),r=r(o),t.flags|=1,vt(e,t,r,n),t.child;case 14:return r=t.type,o=an(r,t.pendingProps),o=an(r.type,o),md(e,t,r,o,n);case 15:return xp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:an(r,o),Nl(e,t),t.tag=1,Tt(r)?(e=!0,Bl(t)):e=!1,xo(t,n),vp(t,r,o),Da(t,r,o,n),$a(null,t,r,!0,e,n);case 19:return Ep(e,t,n);case 22:return Sp(e,t,n)}throw Error(q(156,t.tag))};function Bp(e,t){return mf(e,t)}function s0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Kt(e,t,n,r){return new s0(e,t,n,r)}function qu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function a0(e){if(typeof e=="function")return qu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===cu)return 11;if(e===du)return 14}return 2}function cr(e,t){var n=e.alternate;return n===null?(n=Kt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Tl(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")qu(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case io:return Rr(n.children,o,i,t);case uu:s=8,o|=8;break;case sa:return e=Kt(12,n,t,o|2),e.elementType=sa,e.lanes=i,e;case aa:return e=Kt(13,n,t,o),e.elementType=aa,e.lanes=i,e;case ua:return e=Kt(19,n,t,o),e.elementType=ua,e.lanes=i,e;case Xd:return hs(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Gd:s=10;break e;case Yd:s=9;break e;case cu:s=11;break e;case du:s=14;break e;case Yn:s=16,r=null;break e}throw Error(q(130,e==null?e:typeof e,""))}return t=Kt(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Rr(e,t,n,r){return e=Kt(7,e,r,t),e.lanes=n,e}function hs(e,t,n,r){return e=Kt(22,e,r,t),e.elementType=Xd,e.lanes=n,e.stateNode={isHidden:!1},e}function na(e,t,n){return e=Kt(6,e,null,t),e.lanes=n,e}function ra(e,t,n){return t=Kt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function u0(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ds(0),this.expirationTimes=Ds(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ds(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Vu(e,t,n,r,o,i,s,u,d){return e=new u0(e,t,n,u,d),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Kt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Pu(i),e}function c0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:oo,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Hp(e){if(!e)return fr;e=e._reactInternals;e:{if(qr(e)!==e||e.tag!==1)throw Error(q(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Tt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(q(171))}if(e.tag===1){var n=e.type;if(Tt(n))return Hf(e,n,t)}return t}function Wp(e,t,n,r,o,i,s,u,d){return e=Vu(n,r,!0,e,o,i,s,u,d),e.context=Hp(null),n=e.current,r=wt(),o=ur(n),i=An(r,o),i.callback=t??null,sr(n,i,o),e.current.lanes=o,Mi(e,o,r),At(e,r),e}function ms(e,t,n,r){var o=t.current,i=wt(),s=ur(o);return n=Hp(n),t.context===null?t.context=n:t.pendingContext=n,t=An(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=sr(o,t,s),e!==null&&(fn(e,o,s,i),_l(e,o,s)),s}function ts(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Nd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ku(e,t){Nd(e,t),(e=e.alternate)&&Nd(e,t)}function d0(){return null}var qp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Qu(e){this._internalRoot=e}gs.prototype.render=Qu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(q(409));ms(e,t,null,null)};gs.prototype.unmount=Qu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Hr(function(){ms(null,e,null,null)}),t[On]=null}};function gs(e){this._internalRoot=e}gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Sf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Jn.length&&t!==0&&t<Jn[n].priority;n++);Jn.splice(n,0,e),n===0&&_f(e)}};function Gu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ys(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Id(){}function f0(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var f=ts(s);i.call(f)}}var s=Wp(t,r,e,0,null,!1,!1,"",Id);return e._reactRootContainer=s,e[On]=s.current,_i(e.nodeType===8?e.parentNode:e),Hr(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var u=r;r=function(){var f=ts(d);u.call(f)}}var d=Vu(e,0,!1,null,null,!1,!1,"",Id);return e._reactRootContainer=d,e[On]=d.current,_i(e.nodeType===8?e.parentNode:e),Hr(function(){ms(t,d,n,r)}),d}function vs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var u=o;o=function(){var d=ts(s);u.call(d)}}ms(t,s,e,o)}else s=f0(n,t,e,o,r);return ts(s)}kf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=oi(t.pendingLanes);n!==0&&(hu(t,n|1),At(t,Qe()),!(Se&6)&&(To=Qe()+500,mr()))}break;case 13:Hr(function(){var r=Mn(e,1);if(r!==null){var o=wt();fn(r,e,1,o)}}),Ku(e,1)}};mu=function(e){if(e.tag===13){var t=Mn(e,134217728);if(t!==null){var n=wt();fn(t,e,134217728,n)}Ku(e,134217728)}};xf=function(e){if(e.tag===13){var t=ur(e),n=Mn(e,t);if(n!==null){var r=wt();fn(n,e,t,r)}Ku(e,t)}};Sf=function(){return Ie};Cf=function(e,t){var n=Ie;try{return Ie=e,t()}finally{Ie=n}};wa=function(e,t,n){switch(t){case"input":if(fa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=as(r);if(!o)throw Error(q(90));Zd(r),fa(r,o)}}}break;case"textarea":tf(e,n);break;case"select":t=n.value,t!=null&&yo(e,!!n.multiple,t,!1)}};uf=Bu;cf=Hr;var p0={usingClientEntryPoint:!1,Events:[Ui,uo,as,sf,af,Bu]},ti={findFiberByHostInstance:Mr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},h0={bundleType:ti.bundleType,version:ti.version,rendererPackageName:ti.rendererPackageName,rendererConfig:ti.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Un.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=pf(e),e===null?null:e.stateNode},findFiberByHostInstance:ti.findFiberByHostInstance||d0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gl.isDisabled&&gl.supportsFiber)try{os=gl.inject(h0),Sn=gl}catch{}}zt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=p0;zt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Gu(t))throw Error(q(200));return c0(e,t,null,n)};zt.createRoot=function(e,t){if(!Gu(e))throw Error(q(299));var n=!1,r="",o=qp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Vu(e,1,!1,null,null,n,!1,r,o),e[On]=t.current,_i(e.nodeType===8?e.parentNode:e),new Qu(t)};zt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(q(188)):(e=Object.keys(e).join(","),Error(q(268,e)));return e=pf(t),e=e===null?null:e.stateNode,e};zt.flushSync=function(e){return Hr(e)};zt.hydrate=function(e,t,n){if(!ys(t))throw Error(q(200));return vs(null,e,t,!0,n)};zt.hydrateRoot=function(e,t,n){if(!Gu(e))throw Error(q(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=qp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Wp(t,null,e,1,n??null,o,!1,i,s),e[On]=t.current,_i(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new gs(t)};zt.render=function(e,t,n){if(!ys(t))throw Error(q(200));return vs(null,e,t,!1,n)};zt.unmountComponentAtNode=function(e){if(!ys(e))throw Error(q(40));return e._reactRootContainer?(Hr(function(){vs(null,null,e,!1,function(){e._reactRootContainer=null,e[On]=null})}),!0):!1};zt.unstable_batchedUpdates=Bu;zt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ys(n))throw Error(q(200));if(e==null||e._reactInternals===void 0)throw Error(q(38));return vs(e,t,n,!1,r)};zt.version="18.3.1-next-f1338f8080-20240426";function Vp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Vp)}catch(e){console.error(e)}}Vp(),qd.exports=zt;var Kp=qd.exports;const m0=Ld(Kp);var Qp,Pd=Kp;Qp=Pd.createRoot,Pd.hydrateRoot;let Gp=logseq.isMainUIVisible;function g0(e,t){return logseq.on(e,t),()=>{logseq.off(e,t)}}const y0=e=>g0("ui:visible:changed",({visible:t})=>{Gp=t,e()}),v0=()=>Hd.useSyncExternalStore(y0,()=>Gp);async function Td(){try{const e=window.event,t=e?.clientX||0,n=e?.clientY||0;console.log(`鼠标位置: x=${t}, y=${n}`);const r=document.elementFromPoint(t,n);if(!r)return console.log("鼠标下方没有元素"),{element:null,uuid:null};let o=r,i=null;for(;o&&!o.getAttribute("blockid");)if(o=o.parentElement,!o||o===document.body)return console.log("未找到块元素"),{element:null,uuid:null};return i=o.getAttribute("blockid"),console.log(`找到块元素, UUID: ${i}`),{element:o,uuid:i}}catch(e){return console.error("获取鼠标下方块失败:",e),{element:null,uuid:null}}}const be={apiUrl:"https://api.openai.com/v1/chat/completions",apiKey:"",modelName:"gpt-3.5-turbo",apiConfigs:[{id:"default",name:"OpenAI",apiUrl:"https://api.openai.com/v1/chat/completions",apiKey:"",modelName:"gpt-3.5-turbo",provider:"openai",isDefault:!0}],currentApiConfigId:"default",requestTimeout:60,maxRetries:3,retryDelay:1,temperature:.7,maxTokens:2e3,enableHistory:!1,customPrompts:[{name:"总结",prompt:"请总结以下内容的要点："},{name:"扩展",prompt:"请基于以下内容进行扩展和补充："}],lastSelectedPrompt:"default",keybindings:{openChat:{binding:"ctrl+g",mac:"cmd+g"},quickReply:{binding:"ctrl+shift+g",mac:"cmd+shift+g"}},contextSettings:{enableSmartContext:!0,contextRange:3,includePageTitle:!0,enableThemeDetection:!0},uiSettings:{rememberWindowSize:!0,rememberWindowPosition:!0,defaultWidth:600,defaultHeight:500,enableDetachedWindow:!0,theme:"system",customThemeColor:"#0077ff",showToolbar:!0,autoResponseOnOpen:!1}};async function w0(){const e=logseq.settings||{},t=Object.assign({},be,e);if(Array.isArray(t.customPrompts)||(t.customPrompts=[...be.customPrompts]),t.temperature===void 0&&(t.temperature=be.temperature),t.maxTokens===void 0&&(t.maxTokens=be.maxTokens),t.lastSelectedPrompt===void 0&&(t.lastSelectedPrompt=be.lastSelectedPrompt),t.requestTimeout===void 0&&(t.requestTimeout=be.requestTimeout),t.maxRetries===void 0&&(t.maxRetries=be.maxRetries),t.retryDelay===void 0&&(t.retryDelay=be.retryDelay),!Array.isArray(t.apiConfigs)||t.apiConfigs.length===0)if(console.log("初始化API配置列表"),t.apiConfigs=[],t.apiUrl&&t.apiKey){const r={id:"default",name:"OpenAI",apiUrl:t.apiUrl,apiKey:t.apiKey,modelName:t.modelName||"gpt-3.5-turbo",provider:"openai",isDefault:!0};t.apiConfigs.push(r),t.currentApiConfigId="default"}else t.apiConfigs=[...be.apiConfigs],t.currentApiConfigId=be.currentApiConfigId;else t.apiConfigs=t.apiConfigs.map(r=>(r.id||(r.id=Yp()),r));if(!t.currentApiConfigId||!t.apiConfigs.some(r=>r.id===t.currentApiConfigId)){const r=t.apiConfigs.find(o=>o.isDefault);r?t.currentApiConfigId=r.id:t.apiConfigs.length>0&&(t.currentApiConfigId=t.apiConfigs[0].id)}const n=t.apiConfigs.find(r=>r.id===t.currentApiConfigId);return n&&(t.apiUrl=n.apiUrl,t.apiKey=n.apiKey,t.modelName=n.modelName),t.keybindings?(t.keybindings.openChat||(t.keybindings.openChat=be.keybindings.openChat),t.keybindings.quickReply||(t.keybindings.quickReply=be.keybindings.quickReply)):t.keybindings=be.keybindings,t.contextSettings?(t.contextSettings.enableSmartContext===void 0&&(t.contextSettings.enableSmartContext=be.contextSettings.enableSmartContext),t.contextSettings.contextRange===void 0&&(t.contextSettings.contextRange=be.contextSettings.contextRange),t.contextSettings.includePageTitle===void 0&&(t.contextSettings.includePageTitle=be.contextSettings.includePageTitle),t.contextSettings.enableThemeDetection===void 0&&(t.contextSettings.enableThemeDetection=be.contextSettings.enableThemeDetection)):t.contextSettings=be.contextSettings,t.uiSettings?(t.uiSettings.rememberWindowSize===void 0&&(t.uiSettings.rememberWindowSize=be.uiSettings.rememberWindowSize),t.uiSettings.rememberWindowPosition===void 0&&(t.uiSettings.rememberWindowPosition=be.uiSettings.rememberWindowPosition),t.uiSettings.defaultWidth===void 0&&(t.uiSettings.defaultWidth=be.uiSettings.defaultWidth),t.uiSettings.defaultHeight===void 0&&(t.uiSettings.defaultHeight=be.uiSettings.defaultHeight),t.uiSettings.enableDetachedWindow===void 0&&(t.uiSettings.enableDetachedWindow=be.uiSettings.enableDetachedWindow),t.uiSettings.theme===void 0&&(t.uiSettings.theme=be.uiSettings.theme),t.uiSettings.customThemeColor===void 0&&(t.uiSettings.customThemeColor=be.uiSettings.customThemeColor),t.uiSettings.showToolbar===void 0&&(t.uiSettings.showToolbar=be.uiSettings.showToolbar),t.uiSettings.autoResponseOnOpen===void 0&&(t.uiSettings.autoResponseOnOpen=be.uiSettings.autoResponseOnOpen)):t.uiSettings=be.uiSettings,await logseq.updateSettings(t),t}function Yp(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}async function er(e){const n={...logseq.settings||{},...e};await logseq.updateSettings(n),console.log("Settings updated:",n)}async function Gt(){const e=logseq.settings||{};return Object.assign({},be,e)}class Co extends Error{constructor(t){super(t),this.name="ApiConnectionError"}}async function k0(e,t,n){const r=[];await Gt(),n?r.push({role:"system",content:n}):r.push({role:"system",content:"你是一个有用的助手，可以帮助用户处理和分析文本内容。提供简洁、准确的回答。"});let o=e;if(t.content&&!e.includes(t.content)){let i="";switch(t.type){case"selection":i=`以下是选中的文本：

`;break;case"block":i=`以下是当前块的内容：

`;break;case"blocks":i=`以下是选中的多个块：

`;break;case"smart":i=`以下是智能上下文：

`;break}o=`${i}${t.content}

${e}`}return r.push({role:"user",content:o}),r}function oa(e){try{const t=e.split(`
`).filter(r=>r.trim()!==""&&r.trim()!=="data: [DONE]");let n="";for(const r of t)if(r.startsWith("data: ")){const o=r.replace(/^data: /,"");if(!o||o==="[DONE]")continue;try{const i=JSON.parse(o);i.choices&&i.choices[0]?i.choices[0].delta&&i.choices[0].delta.content?n+=i.choices[0].delta.content:i.choices[0].message&&i.choices[0].message.content&&(n+=i.choices[0].message.content):i.candidates&&i.candidates[0]?i.candidates[0].content&&i.candidates[0].content.parts&&i.candidates[0].content.parts.forEach(s=>{s.text&&(n+=s.text)}):i.content?n+=i.content:i.text&&(n+=i.text)}catch{console.warn("解析 JSON 失败:",o)}}return n}catch(t){return console.error("解析数据块失败:",t),""}}async function li(){const e=await Gt();if(console.log("获取当前API配置"),!Array.isArray(e.apiConfigs)||e.apiConfigs.length===0)throw console.error("API 配置列表为空，请在设置中添加API配置"),new Co("API 配置列表为空，请在设置中添加API配置");const t=e.currentApiConfigId;let n=e.apiConfigs.find(r=>r.id===t);if(!n&&e.apiConfigs.length>0&&(n=e.apiConfigs[0],console.log("找不到当前选中的配置，使用第一个配置:",n.name)),!n)throw console.error("API 未配置，请在设置中配置API"),new Co("API 未配置，请在设置中配置API");if(!n.apiUrl||!n.apiKey)throw console.error("当前API配置不完整，请检查API URL和API Key"),new Co("当前API配置不完整，请检查API URL和API Key");return n}async function x0(e){try{if(!e.apiUrl||!e.apiKey)return{success:!1,message:"API URL 或 API Key 不能为空"};const n=(await Gt()).requestTimeout||60,r=[{role:"system",content:"你是一个有用的助手。"},{role:"user",content:'你好，这是一个连接测试。请回复"连接成功"。'}];let o;switch(e.provider){case"gemini":o={contents:r.map(f=>{let v;return f.role==="system"?v="user":f.role==="assistant"?v="model":v=f.role,{role:v,parts:[{text:f.content}]}}),generationConfig:{temperature:.7,maxOutputTokens:50}};break;case"openai":default:o={messages:r,stream:!1,model:e.modelName||"gpt-3.5-turbo",temperature:.7,max_tokens:50};break}const i=new AbortController,s=setTimeout(()=>i.abort(),n*1e3);try{const u=await fetch(e.apiUrl,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e.apiKey}`},body:JSON.stringify(o),signal:i.signal});if(clearTimeout(s),!u.ok){let f=`API 请求失败: ${u.status} ${u.statusText}`;try{const v=await u.json();f=`${f}. ${v.error?.message||JSON.stringify(v)}`}catch{}return{success:!1,message:f}}const d=await u.json();switch(e.provider){case"gemini":if(d.candidates&&d.candidates.length>0)return{success:!0,message:"连接成功！Gemini API 正常工作。"};break;case"openai":default:if(d.choices&&d.choices.length>0)return{success:!0,message:"连接成功！OpenAI API 正常工作。"};break}return{success:!1,message:"收到响应，但格式不正确。请检查API提供商设置是否正确。"}}catch(u){if(clearTimeout(s),u instanceof Error&&u.name==="AbortError")return{success:!1,message:`API 请求超时（${n}秒），请在设置中增加超时时间或检查网络连接。`};throw u}}catch(t){return{success:!1,message:`连接测试失败: ${t instanceof Error?t.message:String(t)}`}}}async function Ja(e,t,n,r){const o=await Gt(),i=await li(),s=o.requestTimeout||60,u=o.maxRetries||3,d=o.retryDelay||1,f=await k0(e,t,r);let v;switch(i.provider){case"gemini":v={contents:f.map(w=>{let C;return w.role==="system"?C="user":w.role==="assistant"?C="model":C=w.role,{role:C,parts:[{text:w.content}]}}),generationConfig:{}},o.temperature!==void 0&&(v.generationConfig.temperature=o.temperature),o.maxTokens!==void 0&&o.maxTokens>0&&(v.generationConfig.maxOutputTokens=o.maxTokens);break;case"openai":default:const S={messages:f,stream:!0,model:i.modelName};o.temperature!==void 0&&(S.temperature=o.temperature),o.maxTokens!==void 0&&o.maxTokens>0&&(S.max_tokens=o.maxTokens),v=S;break}const N=async _=>{try{const S=new AbortController,w=setTimeout(()=>S.abort(),s*1e3);console.log(`发送API请求（尝试 ${_}/${u}），超时时间: ${s}秒`);const C=await fetch(i.apiUrl,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i.apiKey}`},body:JSON.stringify(v),signal:S.signal});return clearTimeout(w),C}catch(S){if(S instanceof Error&&S.name==="AbortError"&&_<u)return console.log(`请求超时，${d}秒后重试...`),await new Promise(w=>setTimeout(w,d*1e3)),N(_+1);throw S}};try{const _=await N(1);if(!_.ok){let S=`API 请求失败: ${_.status} ${_.statusText}`;try{const w=await _.json();S=`${S}. ${w.error?.message||JSON.stringify(w)}`}catch{}throw new Co(S)}if(_.body){const S=_.body.getReader(),w=new TextDecoder("utf-8");let C="",P="",h=Date.now();const p=50;for(;;){const{done:y,value:k}=await S.read();if(y){if(P.length>0){const O=oa(P);O&&(C+=O,n.onChunk(O))}n.onComplete(C);break}const T=w.decode(k,{stream:!0});P+=T;const A=P.split(`

`),m=A.pop()||"";if(A.length>0){const O=oa(A.join(`

`));O&&(C+=O,n.onChunk(O))}P=m;const x=Date.now();if(x-h>=p&&P.includes("data: ")){const O=oa(P);O&&(C+=O,n.onChunk(O),P=""),h=x}}}else throw new Co("API 响应中没有正文")}catch(_){if(console.error("API请求错误:",_),_ instanceof Co)n.onError(_.message);else if(_ instanceof Error&&_.name==="AbortError"){const S=`API请求超时（${s}秒）。已尝试 ${u} 次重试。

建议操作：
1. 在设置中增加超时时间
2. 检查网络连接
3. 确认API服务器状态`;n.onError(S)}else{const S=_ instanceof Error?_.message:String(_);n.onError(`请求失败: ${S}

如果问题持续存在，请尝试在设置中增加超时时间或重试次数。`)}}}const S0=({apiConfigs:e,currentApiConfigId:t,onApiConfigsChange:n,onCurrentApiConfigChange:r})=>{const[o,i]=Z.useState(null),[s,u]=Z.useState(!1),[d,f]=Z.useState({loading:!1});Z.useEffect(()=>{if(s&&!o){const k=e.find(T=>T.id===t);k&&i({...k})}},[s,o,e,t]);const v=()=>{i({id:Yp(),name:"",apiUrl:"",apiKey:"",modelName:"gpt-3.5-turbo",provider:"openai",isDefault:e.length===0}),u(!0)},N=k=>{const T=e.find(A=>A.id===k);T&&(i({...T}),u(!0))},_=k=>{if(e.length<=1){alert("至少需要保留一个API配置");return}if(window.confirm("确定要删除此API配置吗？")){const T=e.filter(A=>A.id!==k);k===t&&T.length>0&&r(T[0].id),T.length>0&&!T.some(A=>A.isDefault)&&(T[0].isDefault=!0),n(T)}},S=k=>{const T=e.map(A=>({...A,isDefault:A.id===k}));n(T)},w=k=>{r(k)},C=()=>{if(!o)return;if(!o.name||!o.apiUrl||!o.apiKey){alert("配置名称、API URL和API Key不能为空");return}if(e.some(m=>m.name===o.name&&m.id!==o.id)){alert(`配置名称 "${o.name}" 已存在，请使用不同的名称`);return}let T;const A=e.findIndex(m=>m.id===o.id);A>=0?T=e.map(m=>m.id===o.id?{...o}:m):T=[...e,{...o}],o.isDefault?T=T.map(m=>({...m,isDefault:m.id===o.id})):T.some(m=>m.isDefault)||T.length>0&&(T[0].isDefault=!0),n(T),(A<0||!t)&&r(o.id),u(!1),i(null)},P=()=>{u(!1),i(null)},h=async k=>{f({configId:k.id,loading:!0});try{const T=await x0(k);f({configId:k.id,loading:!1,success:T.success,message:T.message})}catch(T){f({configId:k.id,loading:!1,success:!1,message:`测试连接时出错: ${T instanceof Error?T.message:String(T)}`})}},p=()=>e.length===0?E("div",{className:"text-gray-500 dark:text-gray-400 italic",children:"没有API配置，请添加一个"}):E("div",{className:"space-y-2",children:e.map(k=>z("div",{className:`p-3 border rounded ${k.id===t?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600"}`,children:[z("div",{className:"flex justify-between items-center mb-2",children:[z("div",{className:"font-medium",children:[k.name,k.isDefault&&E("span",{className:"ml-2 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-0.5 rounded",children:"默认"})]}),z("div",{className:"flex space-x-2",children:[E("button",{onClick:()=>N(k.id),className:"text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300",children:"编辑"}),E("button",{onClick:()=>_(k.id),className:"text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",disabled:e.length<=1,children:"删除"}),!k.isDefault&&E("button",{onClick:()=>S(k.id),className:"text-green-500 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300",children:"设为默认"}),k.id!==t&&E("button",{onClick:()=>w(k.id),className:"text-purple-500 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300",children:"使用此配置"})]})]}),z("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:[z("div",{children:["提供商: ",k.provider==="openai"?"OpenAI":k.provider==="gemini"?"Google Gemini":k.provider==="anthropic"?"Anthropic Claude":k.provider==="azure"?"Azure OpenAI":k.provider]}),z("div",{children:["API URL: ",k.apiUrl]}),z("div",{children:["模型: ",k.modelName]})]}),z("div",{className:"mt-2",children:[E("button",{onClick:()=>h(k),className:"px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600",disabled:d.loading&&d.configId===k.id,children:d.loading&&d.configId===k.id?"测试中...":"测试连接"}),d.configId===k.id&&d.message&&E("span",{className:`ml-2 text-xs ${d.success?"text-green-500":"text-red-500"}`,children:d.message})]})]},k.id))});return z("div",{className:"api-config-manager",children:[(()=>o?z("div",{className:"bg-white dark:bg-gray-800 p-4 border border-gray-300 dark:border-gray-600 rounded-lg",children:[E("h3",{className:"text-lg font-medium mb-4",children:o.id?"编辑API配置":"添加新的API配置"}),z("div",{className:"space-y-4",children:[z("div",{children:[z("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:["配置名称 ",E("span",{className:"text-red-500",children:"*"})]}),E("input",{type:"text",value:o.name,onChange:k=>i({...o,name:k.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"例如: OpenAI API",required:!0})]}),z("div",{children:[z("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:["API提供商 ",E("span",{className:"text-red-500",children:"*"})]}),z("select",{value:o.provider,onChange:k=>i({...o,provider:k.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",required:!0,children:[E("option",{value:"openai",children:"OpenAI"}),E("option",{value:"gemini",children:"Google Gemini"}),E("option",{value:"anthropic",children:"Anthropic (Claude)"}),E("option",{value:"azure",children:"Azure OpenAI"})]})]}),z("div",{children:[z("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:["API URL ",E("span",{className:"text-red-500",children:"*"})]}),E("input",{type:"text",value:o.apiUrl,onChange:k=>i({...o,apiUrl:k.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"例如: https://api.openai.com/v1/chat/completions",required:!0}),z("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:[o.provider==="openai"&&"OpenAI API地址: https://api.openai.com/v1/chat/completions",o.provider==="gemini"&&"Google Gemini API地址: https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",o.provider==="anthropic"&&"Anthropic API地址: https://api.anthropic.com/v1/complete"]})]}),z("div",{children:[z("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:["API Key ",E("span",{className:"text-red-500",children:"*"})]}),E("input",{type:"password",value:o.apiKey,onChange:k=>i({...o,apiKey:k.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"输入API Key",required:!0})]}),z("div",{children:[E("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"模型名称"}),E("input",{type:"text",value:o.modelName,onChange:k=>i({...o,modelName:k.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:o.provider==="openai"?"gpt-3.5-turbo":o.provider==="gemini"?"gemini-pro":o.provider==="anthropic"?"claude-2":"model-name"}),z("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:[o.provider==="openai"&&"例如: gpt-3.5-turbo, gpt-4, gpt-4-turbo",o.provider==="gemini"&&"例如: gemini-pro, gemini-pro-vision",o.provider==="anthropic"&&"例如: claude-2, claude-instant-1"]})]}),z("div",{className:"flex items-center",children:[E("input",{type:"checkbox",id:"isDefault",checked:o.isDefault,onChange:k=>i({...o,isDefault:k.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),E("label",{htmlFor:"isDefault",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"设为默认配置"})]}),z("div",{className:"flex justify-end space-x-3 mt-6",children:[E("button",{onClick:P,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"取消"}),E("button",{onClick:C,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"保存"}),E("button",{onClick:()=>h(o),className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",disabled:!o.apiUrl||!o.apiKey,children:"测试连接"})]}),d.configId===o.id&&E("div",{className:`mt-4 p-3 rounded ${d.success?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200"}`,children:d.loading?z("div",{className:"flex items-center",children:[z("svg",{className:"animate-spin h-5 w-5 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[E("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),E("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"测试连接中..."]}):E("div",{children:d.message})})]})]}):null)(),z("div",{className:"flex justify-between items-center mb-3",children:[E("h3",{className:"text-lg font-semibold",children:"API配置"}),!s&&E("button",{onClick:v,className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600",children:"添加配置"})]}),p()]})},C0=({onClose:e})=>{const[t,n]=Z.useState(be),[r,o]=Z.useState(!0),[i,s]=Z.useState(!1),[u,d]=Z.useState(""),[f,v]=Z.useState(null),[N,_]=Z.useState({name:"",prompt:""}),[S,w]=Z.useState(null),[C,P]=Z.useState({api:!0,history:!1,keybindings:!1,prompts:!1});Z.useEffect(()=>{(async()=>{try{const X=await Gt();n(X)}catch(X){console.error("加载设置失败:",X)}finally{o(!1)}})()},[]);const h=()=>{e()},p=W=>{const{name:X,checked:ee}=W.target;n(H=>({...H,[X]:ee}))},y=(W,X,ee)=>{const H=W.target.value;n(I=>({...I,keybindings:{...I.keybindings,[X]:{...I.keybindings[X],[ee]:H}}}))},k=(W,X,ee)=>{if(W.preventDefault(),["Control","Alt","Shift","Meta"].includes(W.key))return;let H=[];W.ctrlKey&&H.push("ctrl"),W.altKey&&H.push("alt"),W.shiftKey&&H.push("shift"),W.metaKey&&(ee==="mac"?H.push("cmd"):H.push("meta")),["Control","Alt","Shift","Meta"].includes(W.key)||H.push(W.key.toLowerCase());const I=H.join("+"),j=T(I,ee);n(B=>({...B,keybindings:{...B.keybindings,[X]:{...B.keybindings[X],[ee]:I}}})),j?v({message:`快捷键冲突: ${I} 已被 ${j} 使用`,action:X,platform:ee}):f&&f.action===X&&f.platform===ee&&v(null)},T=(W,X)=>{const ee={"ctrl+c":"复制","ctrl+v":"粘贴","ctrl+x":"剪切","ctrl+z":"撤销","ctrl+y":"重做","ctrl+a":"全选","ctrl+s":"保存","ctrl+f":"查找","ctrl+n":"新建","ctrl+o":"打开","ctrl+p":"打印","ctrl+w":"关闭","alt+f4":"关闭应用","alt+tab":"切换应用","cmd+c":"复制","cmd+v":"粘贴","cmd+x":"剪切","cmd+z":"撤销","cmd+a":"全选","cmd+s":"保存","cmd+f":"查找","cmd+n":"新建","cmd+o":"打开","cmd+p":"打印","cmd+w":"关闭","cmd+q":"退出应用"};if(ee[W])return`系统快捷键 (${ee[W]})`;const H={"ctrl+enter":"创建新块","shift+enter":"创建新行","ctrl+shift+enter":"创建任务","ctrl+o":"打开文件","ctrl+p":"命令面板","ctrl+shift+f":"全局搜索","ctrl+shift+g":"图谱视图","cmd+enter":"创建新块(Mac)","cmd+shift+enter":"创建任务(Mac)","cmd+o":"打开文件(Mac)","cmd+p":"命令面板(Mac)","cmd+shift+f":"全局搜索(Mac)"};if(H[W])return`Logseq 快捷键 (${H[W]})`;const{openChat:I,quickReply:j}=t.keybindings;if(X==="binding"){if(W===j.binding&&W!==I.binding)return"快速AI回复";if(W===I.binding&&W!==j.binding)return"打开AI聊天"}else if(X==="mac"){if(W===j.mac&&W!==I.mac)return"快速AI回复";if(W===I.mac&&W!==j.mac)return"打开AI聊天"}return null},A=W=>{const{name:X,value:ee}=W.target;_(H=>({...H,[X]:ee}))},m=()=>{!N.name||!N.prompt||(n(W=>({...W,customPrompts:[...W.customPrompts,{...N}]})),_({name:"",prompt:""}))},x=W=>{n(X=>({...X,customPrompts:X.customPrompts.filter((ee,H)=>H!==W)}))},O=W=>{w(W)},U=(W,X)=>{const{name:ee,value:H}=W.target;n(I=>({...I,customPrompts:I.customPrompts.map((j,B)=>B===X?{...j,[ee]:H}:j)}))},ne=()=>{w(null)},ce=()=>{w(null)},fe=async()=>{s(!0),d("");try{await er(t),d("设置已保存"),setTimeout(()=>d(""),3e3)}catch(W){console.error("保存设置失败:",W),d("保存失败，请重试")}finally{s(!1)}};if(r)return E("div",{className:"p-4 text-center",children:"加载中..."});const pe=W=>{P(X=>({...X,[W]:!X[W]}))};return z("div",{className:"p-4 max-w-2xl mx-auto text-gray-800 dark:text-gray-200 overflow-y-auto",style:{maxHeight:"calc(90vh - 40px)"},children:[z("div",{className:"flex justify-between items-center mb-6",children:[E("h1",{className:"text-2xl font-bold text-gray-800 dark:text-gray-100",children:"AI 聊天设置"}),E("button",{onClick:h,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",title:"关闭设置",children:E("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),z("div",{className:"mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[z("div",{className:"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer",onClick:()=>pe("api"),children:[E("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-100",children:"API 配置"}),E("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 transition-transform ${C.api?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),C.api&&z("div",{className:"p-3",children:[E(S0,{apiConfigs:t.apiConfigs||[],currentApiConfigId:t.currentApiConfigId||"",onApiConfigsChange:W=>{n(X=>{const ee={...X,apiConfigs:W},H=W.find(j=>j.isDefault);H&&ee.currentApiConfigId!==H.id&&(ee.currentApiConfigId=H.id);const I=W.find(j=>j.id===ee.currentApiConfigId);return I&&(ee.apiUrl=I.apiUrl,ee.apiKey=I.apiKey,ee.modelName=I.modelName),er(ee).catch(j=>console.error("保存API配置时出错:",j)),ee})},onCurrentApiConfigChange:W=>{n(X=>{const ee=X.apiConfigs?.find(I=>I.id===W);if(!ee)return X;const H={...X,currentApiConfigId:W,apiUrl:ee.apiUrl,apiKey:ee.apiKey,modelName:ee.modelName};return er(H).catch(I=>console.error("保存当前API配置时出错:",I)),H})}}),z("div",{className:"mb-4",children:[E("label",{className:"block mb-2 font-medium",children:"温度"}),E("input",{type:"number",name:"temperature",value:t.temperature,onChange:W=>{const X=parseFloat(W.target.value);!isNaN(X)&&X>=0&&X<=2&&n(ee=>({...ee,temperature:X}))},min:"0",max:"2",step:"0.1",className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"}),E("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"较低的值使输出更确定，较高的值使输出更多样化。范围: 0-2"})]}),z("div",{className:"mb-4",children:[E("label",{className:"block mb-2 font-medium",children:"最大输出标记数"}),E("input",{type:"number",name:"maxTokens",value:t.maxTokens,onChange:W=>{const X=parseInt(W.target.value);!isNaN(X)&&X>0&&n(ee=>({...ee,maxTokens:X}))},min:"1",className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"}),E("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"模型将生成的最大标记数量。"})]})]})]}),z("div",{className:"mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[z("div",{className:"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer",onClick:()=>pe("history"),children:[E("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-100",children:"历史记录设置"}),E("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 transition-transform ${C.history?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),C.history&&z("div",{className:"p-3",children:[z("div",{className:"flex items-center",children:[E("input",{type:"checkbox",id:"enableHistory",name:"enableHistory",checked:t.enableHistory,onChange:p,className:"mr-2"}),E("label",{htmlFor:"enableHistory",className:"font-medium",children:"启用会话历史记录"})]}),E("p",{className:"text-sm text-gray-500 mt-1",children:"启用后，将在同一聊天会话中保存历史对话记录。"})]})]}),z("div",{className:"mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[z("div",{className:"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer",onClick:()=>pe("keybindings"),children:[E("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-100",children:"快捷键设置"}),E("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 transition-transform ${C.keybindings?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),C.keybindings&&z("div",{className:"p-3",children:[z("div",{className:"mb-4",children:[E("label",{className:"block mb-2 font-medium",children:"打开AI聊天"}),z("div",{className:"grid grid-cols-2 gap-4",children:[z("div",{children:[E("label",{className:"block mb-1 text-sm",children:"Windows/Linux"}),E("input",{type:"text",value:t.keybindings.openChat.binding,onChange:W=>y(W,"openChat","binding"),onKeyDown:W=>k(W,"openChat","binding"),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",placeholder:"ctrl+g"})]}),z("div",{children:[E("label",{className:"block mb-1 text-sm",children:"Mac"}),E("input",{type:"text",value:t.keybindings.openChat.mac,onChange:W=>y(W,"openChat","mac"),onKeyDown:W=>k(W,"openChat","mac"),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",placeholder:"cmd+g"})]})]}),E("p",{className:"text-sm text-gray-500 mt-1",children:"格式示例：ctrl+g, ctrl+shift+g, alt+g"}),f&&f.action==="openChat"&&E("p",{className:"text-sm text-red-500 mt-1",children:f.message})]}),z("div",{className:"mb-4",children:[E("label",{className:"block mb-2 font-medium",children:"快速AI回复"}),z("div",{className:"grid grid-cols-2 gap-4",children:[z("div",{children:[E("label",{className:"block mb-1 text-sm",children:"Windows/Linux"}),E("input",{type:"text",value:t.keybindings.quickReply.binding,onChange:W=>y(W,"quickReply","binding"),onKeyDown:W=>k(W,"quickReply","binding"),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",placeholder:"ctrl+shift+g"})]}),z("div",{children:[E("label",{className:"block mb-1 text-sm",children:"Mac"}),E("input",{type:"text",value:t.keybindings.quickReply.mac,onChange:W=>y(W,"quickReply","mac"),onKeyDown:W=>k(W,"quickReply","mac"),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",placeholder:"cmd+shift+g"})]})]}),f&&f.action==="quickReply"&&E("p",{className:"text-sm text-red-500 mt-1",children:f.message})]})]})]}),z("div",{className:"mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[z("div",{className:"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer",onClick:()=>pe("prompts"),children:[E("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-100",children:"自定义提示词"}),E("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 transition-transform ${C.prompts?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),C.prompts&&z("div",{className:"p-3",children:[z("div",{className:"bg-gray-50 dark:bg-gray-800 p-4 rounded mb-4",children:[E("h3",{className:"font-medium mb-2",children:"添加新提示词"}),z("div",{className:"mb-3",children:[E("label",{className:"block mb-1 text-sm",children:"提示词名称"}),E("input",{type:"text",name:"name",value:N.name,onChange:A,className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",placeholder:"提示名称，如：总结、翻译等"})]}),z("div",{className:"mb-3",children:[E("label",{className:"block mb-1 text-sm",children:"提示词内容"}),E("textarea",{name:"prompt",value:N.prompt,onChange:A,className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",rows:3,placeholder:"提示词内容，如：请总结以下内容的要点："})]}),E("button",{onClick:m,disabled:!N.name||!N.prompt,className:"bg-blue-500 text-white px-3 py-1 rounded disabled:bg-gray-300 dark:disabled:bg-gray-700",children:"添加提示词"})]}),z("div",{children:[E("h3",{className:"font-medium mb-2",children:"现有提示词"}),t.customPrompts.length===0?E("p",{className:"text-gray-500",children:"暂无自定义提示词"}):E("div",{className:"space-y-3",children:t.customPrompts.map((W,X)=>E("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:S===X?z("div",{className:"p-3 bg-white dark:bg-gray-800",children:[z("div",{className:"mb-2",children:[E("label",{className:"block mb-1 text-sm",children:"提示词名称"}),E("input",{type:"text",name:"name",value:W.name,onChange:ee=>U(ee,X),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"})]}),z("div",{className:"mb-2",children:[E("label",{className:"block mb-1 text-sm",children:"提示词内容"}),E("textarea",{name:"prompt",value:W.prompt,onChange:ee=>U(ee,X),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",rows:3})]}),z("div",{className:"flex space-x-2",children:[E("button",{onClick:ne,className:"px-2 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600",children:"完成"}),E("button",{onClick:ce,className:"px-2 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 text-sm rounded hover:bg-gray-400 dark:hover:bg-gray-500",children:"取消"})]})]}):z("div",{className:"flex justify-between p-3 bg-white dark:bg-gray-800",children:[z("div",{children:[E("div",{className:"font-medium text-gray-800 dark:text-gray-200",children:W.name}),E("div",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2",children:W.prompt})]}),z("div",{className:"flex items-start space-x-2",children:[E("button",{onClick:()=>O(X),className:"text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300",title:"编辑提示词",children:E("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),E("button",{onClick:()=>x(X),className:"text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",title:"删除提示词",children:E("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},X))})]})]})]}),z("div",{className:"mt-6 flex items-center",children:[E("button",{onClick:fe,disabled:i,className:"bg-green-500 text-white px-4 py-2 rounded font-medium disabled:bg-gray-300 dark:disabled:bg-gray-700",children:i?"保存中...":"保存设置"}),u&&E("span",{className:"ml-3 text-green-600",children:u})]})]})};function ns(e){if(!e||typeof e!="string")return[];e=e.replace(/^>+\s/gm,"");const t=e.split(/\r?\n/),n=[];let r="",o=!1,i="",s=0;const u=t.filter(f=>f.trim().length>0).map(f=>{const v=f.match(/^(\s+)/);return v?v[1].length:0}).filter(f=>f>0);let d=2;if(u.length>0){const f=u.filter(v=>v>0);f.length>0&&(d=Math.min(...f))}for(let f=0;f<t.length;f++){const v=t[f],N=v.match(/^(\s+)/),_=N?N[1].length:0,S=Math.floor(_/d);if(v.trim().startsWith("```")){if(o){o=!1;const w=s>0?"  ".repeat(s):"";n.push(`${w}\`\`\``)}else{if(r.trim()){const h=s>0?"  ".repeat(s):"";n.push(`${h}${r.trim()}`),r=""}o=!0,i="    ";const w=v.trim().match(/^```(.*)$/),C=w?w[1].trim():"",P=S>0?"  ".repeat(S):"";C?r=`\`\`\` ${C}`:r="```",n.push(`${P}${r}`),r="",s=S}continue}if(o){const w=s>0?"  ".repeat(s):"";n.push(`${w}${i}${v.trimStart()}`);continue}if(v.trim().length>0&&(s=S),v.trim().match(/^(\d+\.|-|\*|\+)\s/)){if(r.trim()){const P=s>0?"  ".repeat(s):"";n.push(`${P}${r.trim()}`),r=""}const w=S>0?"  ".repeat(S):"",C=v.trim().replace(/^(\d+\.|-|\*|\+)\s+/,"");n.push(`${w}${C}`);continue}if(v.trim().match(/^#{1,6}\s/)){if(r.trim()){const C=s>0?"  ".repeat(s):"";n.push(`${C}${r.trim()}`),r=""}const w=S>0?"  ".repeat(S):"";n.push(`${w}${v.trim()}`);continue}if(!v.trim()){if(r.trim()){const w=s>0?"  ".repeat(s):"";n.push(`${w}${r.trim()}`),r=""}continue}r?r+=" "+v.trim():r=v.trim()}if(r.trim()){const f=s>0?"  ".repeat(s):"";n.push(`${f}${r.trim()}`)}return n}const _0="0.24.0",b0={id:"logseq-plugin-ai-chat",icon:"./logo.svg",title:"AI 聊天助手",description:"在Logseq中使用自定义API进行AI聊天，支持智能上下文获取、页面主题识别、可调整大小窗口和分离式界面"},E0=({context:e,onClose:t,onReplace:n,onInsert:r})=>{const[o,i]=Z.useState("");Z.useState("");const[s,u]=Z.useState(!1),[d,f]=Z.useState(""),[v,N]=Z.useState(null),[_,S]=Z.useState("default"),[w,C]=Z.useState(""),[P,h]=Z.useState(!0),[p,y]=Z.useState(0),[k,T]=Z.useState([]),[A,m]=Z.useState(-1),[x,O]=Z.useState(!1),[U,ne]=Z.useState(""),[ce,fe]=Z.useState(!1),[pe,W]=Z.useState(""),X=Z.useRef(null),[ee,H]=Z.useState(!1),[I,j]=Z.useState([]),[B,se]=Z.useState({x:0,y:0,show:!1,selectedText:""}),[je,G]=Z.useState({x:0,y:0}),[ue,ye]=Z.useState({width:600,height:500}),[$e,Ge]=Z.useState(!1),[lt,Jt]=Z.useState({x:0,y:0}),[_e,Pe]=Z.useState(!1);Z.useState(!1);const[tt,st]=Z.useState(!0),[St,Zt]=Z.useState("system"),[gt,nt]=Z.useState(null),[Ft,Mo]=Z.useState([]),[Ct,_t]=Z.useState(""),gr=Z.useRef(null),yr=Z.useRef(null),jo=Z.useRef(null),en=Z.useRef(null),vr=Z.useRef(null),Vr=Z.useRef(null);Z.useEffect(()=>{(async()=>{try{const D=await Gt();console.log("Loaded settings:",D),N(D),D.uiSettings&&(ye({width:D.uiSettings.defaultWidth,height:D.uiSettings.defaultHeight}),st(D.uiSettings.showToolbar),Zt(D.uiSettings.theme)),D.lastSelectedPrompt?(console.log("Last selected prompt from settings:",D.lastSelectedPrompt),S(D.lastSelectedPrompt)):(console.log("No lastSelectedPrompt found in settings, using default"),S("default")),console.log("加载API配置"),Array.isArray(D.apiConfigs)&&(Mo(D.apiConfigs),_t(D.currentApiConfigId||""));try{const he=await li();console.log("获取到的当前配置:",he),nt(he)}catch(he){console.error("加载API配置失败:",he)}}catch(D){console.error("加载设置出错:",D),f("无法加载插件设置，请检查配置")}})()},[]);const V=Z.useCallback($=>{vr.current&&vr.current.contains($.target)&&(Ge(!0),Jt({x:$.clientX,y:$.clientY}))},[]),J=Z.useCallback($=>{if($e&&gr.current){const D=$.clientX-lt.x,he=$.clientY-lt.y;G(ge=>({x:ge.x+D,y:ge.y+he})),Jt({x:$.clientX,y:$.clientY})}},[$e,lt]),ie=Z.useCallback(()=>{Ge(!1)},[]);Z.useEffect(()=>(document.addEventListener("mousemove",J),document.addEventListener("mouseup",ie),()=>{document.removeEventListener("mousemove",J),document.removeEventListener("mouseup",ie)}),[J,ie]);const me=()=>{Pe(!_e),_e||G({x:50,y:50})},[Ue,Lt]=Z.useState(!0),Bt=v?.uiSettings?.autoResponseOnOpen||!1;Z.useEffect(()=>{if(Ue){Bt&&e?.content&&(ne(e.content),Ot()),Lt(!1);return}_!=="default"&&_!=="custom"&&e?.content&&(U||ne(e.content),Ot()),_==="custom"?(h(!0),setTimeout(()=>{yr.current?.focus()},50)):h(!1)},[_]),Z.useEffect(()=>{P&&setTimeout(()=>{yr.current?.focus()},50)},[]),Z.useEffect(()=>{o&&ce&&(fe(!1),W(""))},[o]),Z.useEffect(()=>{const $=D=>{en.current&&!en.current.contains(D.target)&&O(!1)};return document.addEventListener("mousedown",$),()=>{document.removeEventListener("mousedown",$)}},[]),Z.useEffect(()=>{const $=D=>{D.target instanceof HTMLInputElement||D.target instanceof HTMLTextAreaElement||s||(D.key==="r"&&o&&!ee&&(D.preventDefault(),bt()),D.key==="Escape"&&ee&&(D.preventDefault(),mn()))};return document.addEventListener("keydown",$),()=>{document.removeEventListener("keydown",$)}},[o,s,ee]);const hn=async $=>{const D=$.target.value;if(S(D),v)try{console.log("Saving lastSelectedPrompt:",D),await er({lastSelectedPrompt:D}),console.log("lastSelectedPrompt saved successfully")}catch(he){console.error("Failed to save lastSelectedPrompt:",he)}D!=="default"&&D!=="custom"&&(U||e?.content)&&!s&&setTimeout(()=>{Ot()},100)},wr=$=>{const D=$.target.value;if(C(D),v&&D.trim()!==""){const he=v.customPrompts.filter(ge=>ge.name.toLowerCase().includes(D.toLowerCase()));T(he),O(he.length>0),m(-1)}else T([]),O(!1)},kr=async($,D)=>{const he=`使用提示词: ${$}`;if(C(he),S("custom"),O(!1),v)try{console.log("Saving lastSelectedPrompt: 'custom'"),await er({lastSelectedPrompt:"custom"}),console.log("lastSelectedPrompt saved successfully")}catch(ge){console.error("Failed to save lastSelectedPrompt:",ge)}setTimeout(()=>{Ot(D)},100)},tn=$=>{if($.key==="Escape"){O(!1);return}if(!x){$.key==="Enter"&&w.trim()&&!s&&($.preventDefault(),Ot());return}if($.key==="ArrowDown")$.preventDefault(),m(D=>D<k.length-1?D+1:D);else if($.key==="ArrowUp")$.preventDefault(),m(D=>D>0?D-1:0);else if($.key==="Enter"&&A>=0){$.preventDefault();const D=k[A];kr(D.name,D.prompt)}},Uo=()=>{w.trim()&&!s&&Ot()},Ot=async $=>{if(!s)try{f(""),u(!0);let D="";if($)D=$;else if(_==="custom"&&w)D=w;else if(_==="default")D="你是一个有用的助手。";else{const we=v?.customPrompts.find(Ce=>Ce.name===_);we?D=we.prompt:D="你是一个有用的助手。"}if(D||(D="你是一个有用的助手。"),ne(D),j(we=>[...we,{type:"question",content:D,timestamp:Date.now()}]),y(0),i(""),!(gt||await li()))throw new Error("无法获取API配置");await Ja(D,e,{onChunk:we=>{console.log("接收到分片:",we),i(Ce=>{let De;return Ce?De=Ce+we:De=we,De})},onComplete:we=>{console.log("响应完成"),u(!1),j(Ce=>[...Ce,{type:"answer",content:we,timestamp:Date.now()}]),_&&v&&er({...v,lastSelectedPrompt:_})},onError:we=>{console.error("接收响应出错:",we),f(`请求失败: ${we}`),u(!1)}},D)}catch(D){console.error("发送消息出错:",D),f(`发送消息失败: ${D instanceof Error?D.message:String(D)}`),u(!1)}},Kr=async()=>{if(!U||s)return;f("正在重新生成回答...");let $;if(_==="custom")$=w;else if(_!=="default"&&v){const D=v.customPrompts.find(he=>he.name===_);D&&($=D.prompt)}$?$=`${$}

注意：请重新思考并给出另一个角度的回答。`:$="请重新思考并给出另一个角度的回答。",Ot($)},Dn=$=>{!o||s||(fe(!0),setTimeout(()=>{X.current?.focus()},50))},Do=$=>{W($.target.value)},xr=async()=>{if(!pe.trim()||s)return;const $=o;u(!0);const D=`${pe}

上下文：
${$}`,he=`${$}

---

**追问：**${pe}

**回答：**_正在生成回答..._`;i(he),j(Ce=>[...Ce,{type:"followup",content:pe,timestamp:Date.now()}]),ne(D),fe(!1),W(""),f("正在回答追问...");let ge;if(_==="custom")ge=w;else if(_!=="default"&&v){const Ce=v.customPrompts.find(De=>De.name===_);Ce&&(ge=Ce.prompt)}const we=pe.includes('"')||pe.includes('"');ge?we?ge=`${ge}

注意：这是一个基于阅读内容的追问，请重点关注引号中的内容进行回答。`:ge=`${ge}

注意：这是一个追问，请基于之前的对话继续回答。`:we?ge="这是一个基于阅读内容的追问，请重点关注引号中的内容进行回答。":ge="这是一个追问，请基于之前的对话继续回答。";try{await Ja(D,e,{onChunk:De=>{i(Ke=>{if(Ke.includes("_正在生成回答..._"))return Ke.replace("_正在生成回答..._",De);if(Ke.includes("**回答：**")){const Te=Ke.lastIndexOf("**回答：**")+7,zn=Ke.substring(0,Te),qe=Ke.substring(Te);return zn+qe+De}else return Ke+De})},onComplete:De=>{u(!1);let Ke=De;const ke=o;if(ke.includes("**回答：**")){const zn=ke.lastIndexOf("**回答：**")+7;Ke=ke.substring(zn).replace("_正在生成回答..._","")}j(Te=>[...Te,{type:"followup-answer",content:Ke.trim(),timestamp:Date.now()}])},onError:De=>{console.error("接收追问响应出错:",De),f(`追问失败: ${De}`),u(!1)}},ge)}catch(Ce){console.error("发送追问出错:",Ce),f(`追问失败: ${Ce instanceof Error?Ce.message:String(Ce)}`),u(!1)}},Qr=()=>{fe(!1),W("")},Sr=$=>{$.key==="Enter"&&pe.trim()&&!s?($.preventDefault(),xr()):$.key==="Escape"&&($.preventDefault(),Qr())},Mt=$=>{s||(W($),fe(!0),setTimeout(()=>{X.current&&(X.current.focus(),xr())},50))},bt=()=>{o&&H(!0)},mn=()=>{H(!1),se({...B,show:!1})},Rn=$=>{if($.preventDefault(),console.log("右键菜单事件触发",{isReadingMode:ee,hasResponse:!!o}),o){const D=window.getSelection(),he=D?D.toString():"";console.log("选中的文本:",he);const ge=Math.min($.clientX,window.innerWidth-200),we=Math.min($.clientY,window.innerHeight-300);console.log("设置右键菜单位置",{x:ge,y:we}),se({x:ge,y:we,show:!0,selectedText:he}),he?logseq.UI.showMsg("已选中部分内容，可以进行操作","info"):logseq.UI.showMsg("右键菜单已显示","info")}},Ht=()=>{se({...B,show:!1})};Z.useEffect(()=>{const $=D=>{B.show&&(D.target.closest(".context-menu")||se({...B,show:!1}))};return document.addEventListener("mousedown",$),()=>{document.removeEventListener("mousedown",$)}},[B]);const gn=()=>E("div",{ref:jo,className:"p-3 max-h-[350px] overflow-y-auto rounded-lg bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 mb-10 ai-response typewriter-text relative cursor-pointer",style:{paddingBottom:"40px"},onClick:bt,title:"点击打开阅读模式",children:o?z("div",{className:"relative",children:[o,s&&E("span",{className:"blinking-cursor"}),z("div",{className:"reading-hint",children:[z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",style:{marginRight:"4px"},children:[E("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),E("circle",{cx:"12",cy:"12",r:"3"})]}),"点击阅读"]})]}):E("span",{className:"text-gray-500 dark:text-gray-400 italic",children:s?"思考中...":"响应将显示在这里"})}),Gr=()=>z("select",{value:_,onChange:hn,className:"apple-input apple-select w-full p-2 mb-2 text-gray-700 dark:text-gray-200",children:[E("option",{value:"default",children:"默认提示词"}),E("option",{value:"custom",children:"自定义提示词"}),v?.customPrompts.map(($,D)=>E("option",{value:$.name,children:$.name},D))]}),Yr=()=>!x||k.length===0?null:E("div",{ref:en,className:"absolute top-full left-0 right-0 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden mt-1",children:k.map(($,D)=>z("div",{className:`p-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${D===A?"bg-blue-50 dark:bg-blue-900":""}`,onClick:()=>kr($.name,$.prompt),children:[E("div",{className:"font-medium text-gray-800 dark:text-gray-200",children:$.name}),E("div",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:$.prompt})]},D))}),Ro=async $=>{try{console.log("开始替换原文，响应文本长度:",$.length);const D=ns($);if(console.log("格式化后的块数量:",D.length),console.log("格式化后的块内容示例:",D.slice(0,2)),console.log("当前上下文块UUID数量:",e.blockUUIDs.length),console.log("当前上下文块UUID示例:",e.blockUUIDs[0]),D.length===0||e.blockUUIDs.length===0){const he="无法替换原文：无有效块数据或无原始块 UUID";console.error(he),f(he);return}console.log("调用onReplace回调函数"),n($),console.log("替换原文操作完成")}catch(D){console.error("替换原文出错:",D),f("替换原文失败，请重试")}},Xr=async $=>{try{console.log("开始插入子块，响应文本长度:",$.length);const D=ns($);if(console.log("格式化后的块数量:",D.length),console.log("格式化后的块内容示例:",D.slice(0,2)),console.log("当前上下文块UUID数量:",e.blockUUIDs.length),console.log("当前上下文块UUID示例:",e.blockUUIDs[0]),D.length===0||e.blockUUIDs.length===0){const he="无法插入子块：无有效块数据或无原始块 UUID";console.error(he),f(he);return}console.log("调用onInsert回调函数"),r($),console.log("插入子块操作完成")}catch(D){console.error("插入子块出错:",D),f("插入子块失败，请重试")}},yt=()=>tt?z("div",{ref:Vr,className:"toolbar",children:[E("div",{className:"toolbar-left",children:z("div",{className:"toolbar-item flex items-center",children:[E("span",{className:"text-sm text-gray-600 dark:text-gray-300 mr-1",children:"模型:"}),E("select",{value:Ct,onChange:$=>{const D=$.target.value;_t(D),(async()=>{try{const ge=await Gt(),we={...ge,currentApiConfigId:D},Ce=ge.apiConfigs?.find(Ke=>Ke.id===D);Ce&&(we.apiUrl=Ce.apiUrl,we.apiKey=Ce.apiKey,we.modelName=Ce.modelName),await er(we);const De=await li();nt(De),e?.content&&!s&&(i(""),f(`正在使用新模型 ${De.modelName} 重新回答...`),setTimeout(()=>{Ot()},300))}catch(ge){console.error("更新API配置失败:",ge),f("更新API配置失败，请重试")}})()},className:"text-xs p-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 max-w-[150px]",children:Ft.length>0?Ft.map($=>z("option",{value:$.id,children:[$.name," (",$.modelName,")"]},$.id)):E("option",{value:"",children:"未找到API配置"})})]})}),z("div",{className:"toolbar-right",children:[z("button",{className:"toolbar-button",onClick:()=>{if(o){const $=o.replace(/```(.*?)\n([\s\S]*?)```/g,(D,he,ge)=>`\`\`\`${he}
${ge.trim()}
\`\`\``);i($)}},children:[z("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("polyline",{points:"16 18 22 12 16 6"}),E("polyline",{points:"8 6 2 12 8 18"})]}),"格式化"]}),z("button",{className:"toolbar-button",onClick:()=>{i(""),f("")},children:[z("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("path",{d:"M3 6h18"}),E("path",{d:"M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"})]}),"清除"]}),z("button",{className:"toolbar-button",onClick:()=>{logseq.showSettingsUI()},title:"打开插件设置",children:[z("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("path",{d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),E("path",{d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"设置"]})]})]}):null;return z("div",{ref:gr,className:`apple-modal z-50 ${_e?"detached-window":"fixed top-1/4 left-1/2 transform -translate-x-1/2"} ${St!=="system"?`theme-${St}`:""}`,style:{width:`${ue.width}px`,height:`${ue.height}px`,..._e?{left:`${je.x}px`,top:`${je.y}px`}:{}},onMouseDown:V,children:[z("div",{ref:vr,className:"titlebar apple-titlebar cursor-move flex justify-between items-center p-2 header-gradient rounded-t-lg",onMouseDown:V,children:[E("div",{className:"flex items-center space-x-2",children:z("span",{className:"text-base font-medium text-gray-800 dark:text-gray-100",children:["AI 聊天助手 ",z("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["v",_0]})]})}),z("div",{className:"flex items-center space-x-2",children:[E("button",{className:"p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300",onClick:me,title:_e?"重新嵌入窗口":"分离为独立窗口",children:z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),_e?E("path",{d:"M9 3v18"}):z(cm,{children:[E("line",{x1:"15",y1:"3",x2:"15",y2:"9"}),E("line",{x1:"9",y1:"15",x2:"15",y2:"9"})]})]})}),E("button",{className:"p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300",onClick:t,title:"关闭",children:z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),E("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})]}),z("div",{className:"p-4 overflow-y-auto",style:{height:"calc(100% - 100px)"},children:[z("div",{className:"mb-4",children:[z("div",{className:"flex justify-between items-center mb-1",children:[E("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"上下文:"}),e?.type==="smart"&&E("span",{className:"text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-0.5 rounded-full",children:"智能上下文"})]}),(e?.pageTitle||e?.theme)&&z("div",{className:"flex flex-wrap gap-2 mb-2",children:[e.pageTitle&&z("span",{className:"text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-md flex items-center",children:[E("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"页面: ",e.pageTitle]}),e.theme&&z("span",{className:"text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-md flex items-center",children:[E("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:E("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"主题: ",e.theme]})]}),E("div",{className:"p-2 bg-gray-100 dark:bg-gray-700 rounded text-gray-800 dark:text-gray-200 text-sm max-h-[100px] overflow-y-auto",children:e?.content||"无上下文"}),e?.blockUUIDs&&e.blockUUIDs.length>0&&z("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:["包含 ",e.blockUUIDs.length," 个块"]})]}),z("div",{className:"mb-4",children:[Gr(),P&&z("div",{className:"relative",children:[z("div",{className:"flex items-center",children:[E("input",{ref:yr,type:"text",value:w,onChange:wr,onKeyDown:tn,placeholder:"输入提示或搜索已保存的提示词...",className:"apple-input w-full p-2 text-gray-700 dark:text-gray-200"}),E("button",{onClick:Uo,disabled:s||!w.trim(),className:`ml-2 apple-button px-4 py-2 ${s||!w.trim()?"bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed":"bg-blue-500 hover:bg-blue-600 text-white"} rounded transition-colors`,children:"发送"})]}),Yr()]})]}),gn(),d&&E("div",{className:"mb-4 p-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded",children:d})]}),E("div",{className:"floating-actions",children:yt()}),E("div",{className:"resize-handle"}),ee&&E("div",{className:"reading-mode-overlay",onClick:$=>{$.target===$.currentTarget&&mn()},children:z("div",{className:"reading-mode-container",children:[z("div",{className:"reading-mode-header",children:[E("div",{className:"reading-mode-title",children:"阅读内容"}),E("div",{className:"reading-mode-close",onClick:mn,role:"button",tabIndex:0,"aria-label":"关闭",children:z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),E("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),z("div",{className:"reading-mode-content",title:"右键点击显示操作菜单",children:[E("div",{className:"reading-mode-content-tip",children:"选中部分内容后右键点击可操作选中内容，或右键点击操作全部内容"}),E("div",{className:"reading-mode-content-inner",onContextMenu:Rn,style:{minHeight:"100%",maxHeight:"70vh",overflow:"auto"},children:I.length>0?z("div",{className:"chat-history-container",children:[I.map(($,D)=>{switch($.type){case"question":return z("div",{className:"history-item question-item",children:[E("div",{className:"history-item-header",children:"问题:"}),E("div",{className:"history-item-content",children:$.content}),D<I.length-1&&I[D+1].type==="answer"&&E("hr",{className:"history-divider"})]},`${$.type}-${D}`);case"answer":return z("div",{className:"history-item answer-item",children:[E("div",{className:"history-item-header",children:"回答:"}),E("div",{className:"history-item-content",children:$.content}),D<I.length-1&&I[D+1].type==="followup"&&E("hr",{className:"history-divider"})]},`${$.type}-${D}`);case"followup":return z("div",{className:"history-item followup-item",children:[z("div",{className:"history-item-header flex justify-between items-center",children:[E("span",{children:"追问:"}),E("button",{className:"refresh-button p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors",onClick:()=>Mt($.content),title:"重新发送这个追问",disabled:s,children:E("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:E("path",{d:"M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"})})})]}),E("div",{className:"history-item-content",children:$.content}),D<I.length-1&&I[D+1].type==="followup-answer"&&E("hr",{className:"history-divider"})]},`${$.type}-${D}`);case"followup-answer":return z("div",{className:"history-item followup-answer-item",children:[E("div",{className:"history-item-header",children:"回答:"}),E("div",{className:"history-item-content",children:$.content}),D<I.length-1&&(I[D+1].type==="followup"||I[D+1].type==="question")&&E("hr",{className:"history-divider"})]},`${$.type}-${D}`);default:return null}}),s&&E("div",{className:"history-item loading-item",children:E("div",{className:"history-item-content",children:"思考中..."})})]}):E("div",{className:"no-history-message",children:o||(s?"正在加载内容...":"无对话历史")})})]}),m0.createPortal(B.show&&z("div",{className:"context-menu",style:{top:B.y,left:B.x},children:[z("div",{className:`context-menu-item ${!o||s||ce?"disabled":""}`,onClick:()=>{!o||s||ce||(Ht(),Dn(B.selectedText))},children:[E("div",{className:"context-menu-item-icon",children:E("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:E("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})})}),"追问"]}),z("div",{className:`context-menu-item ${!o||s||!U?"disabled":""}`,onClick:()=>{!o||s||!U||(Ht(),Kr())},children:[E("div",{className:"context-menu-item-icon",children:E("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:E("path",{d:"M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"})})}),"重新回答"]}),z("div",{className:`context-menu-item ${!o||s?"disabled":""}`,onClick:()=>{if(!o||s)return;Ht();const $=B.selectedText||o;Ro($),mn()},children:[E("div",{className:"context-menu-item-icon",children:z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),E("line",{x1:"9",y1:"9",x2:"15",y2:"15"}),E("line",{x1:"15",y1:"9",x2:"9",y2:"15"})]})}),B.selectedText?"替换选中内容":"替换原文"]}),z("div",{className:`context-menu-item ${!o||s||!U?"disabled":""}`,onClick:()=>{!o||s||!U||(Ht(),Kr())},children:[E("div",{className:"context-menu-item-icon",children:z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("polyline",{points:"17 1 21 5 17 9"}),E("path",{d:"M3 11V9a4 4 0 0 1 4-4h14"}),E("polyline",{points:"7 23 3 19 7 15"}),E("path",{d:"M21 13v2a4 4 0 0 1-4 4H3"})]})}),(B.selectedText,"重答")]}),z("div",{className:`context-menu-item ${!o||s?"disabled":""}`,onClick:()=>{if(!o||s)return;Ht();const $=B.selectedText||o;Xr($),mn()},children:[E("div",{className:"context-menu-item-icon",children:z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),E("line",{x1:"5",y1:"12",x2:"19",y2:"12"})]})}),B.selectedText?"插入选中内容":"插入子块"]}),E("div",{className:"context-menu-divider"}),z("div",{className:"context-menu-item",onClick:()=>{if(Ht(),B.selectedText||o){const $=B.selectedText||o;navigator.clipboard.writeText($),logseq.UI.showMsg(B.selectedText?"已复制选中内容":"已复制全部内容","success")}},children:[E("div",{className:"context-menu-item-icon",children:z("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[E("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),E("rect",{x:"8",y:"2",width:"8",height:"4",rx:"1",ry:"1"})]})}),B.selectedText?"复制选中内容":"复制全部内容"]})]}),document.body),ce&&E("div",{className:"reading-mode-followup",children:z("div",{className:"flex flex-col bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 shadow-lg",children:[z("div",{className:"flex-grow",children:[E("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:"追问"}),E("input",{ref:X,type:"text",value:pe,onChange:Do,onKeyDown:Sr,placeholder:"输入你的追问...",className:"apple-input w-full p-2 text-gray-700 dark:text-gray-200",autoFocus:!0})]}),z("div",{className:"mt-2 mb-2",children:[E("label",{className:"block text-xs text-gray-500 dark:text-gray-400 mb-1",children:"模型"}),E("select",{value:Ct,onChange:$=>{const D=$.target.value;_t(D),(async()=>{try{const ge=await Gt(),we={...ge,currentApiConfigId:D},Ce=ge.apiConfigs?.find(Ke=>Ke.id===D);Ce&&(we.apiUrl=Ce.apiUrl,we.apiKey=Ce.apiKey,we.modelName=Ce.modelName),await er(we);const De=await li();nt(De)}catch(ge){console.error("更新API配置失败:",ge),f("更新API配置失败，请重试")}})()},className:"apple-input w-full p-2 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700",children:Ft.length>0?Ft.map($=>z("option",{value:$.id,children:[$.name," (",$.modelName,")"]},$.id)):E("option",{value:"",children:"未找到API配置"})})]}),z("div",{className:"flex justify-end space-x-2",children:[E("button",{onClick:xr,disabled:s||!pe.trim(),className:`apple-button px-4 py-2 ${s||!pe.trim()?"bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed":"bg-blue-500 hover:bg-blue-600 text-white"} rounded transition-colors`,children:"发送"}),E("button",{onClick:Qr,className:"apple-button px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded transition-colors",children:"取消"})]})]})})]})})]})};function N0({chatContext:e}={}){const t=Z.useRef(null),n=v0(),[r,o]=Z.useState("settings"),[i,s]=Z.useState(e),[u,d]=Z.useState(null);Z.useEffect(()=>{e?(s(e),o("chat"),setTimeout(f,50)):o("settings")},[e]);const f=async()=>{if(!i||!i.blockUUIDs.length){console.log("无法获取块位置：上下文或块UUID不存在");return}try{const C=i.blockUUIDs[0],P=document.querySelector(`[blockid="${C}"]`);if(P){const h=P.getBoundingClientRect();d({top:h.bottom,left:h.left,width:h.width})}else console.log(`无法找到块元素，UUID: ${C}`),d(null)}catch(C){console.error("获取块位置失败:",C),d(null)}},v=async C=>{if(!i||!i.blockUUIDs.length){console.error("替换原文失败: 上下文或块UUID不存在");return}try{console.log("开始替换原文操作, 内容长度:",C.length),console.log("块UUID列表:",i.blockUUIDs);const P=ns(C);if(console.log("格式化后的块数量:",P.length),console.log("格式化后的块内容示例:",P.slice(0,2)),P.length===0){console.error("格式化后的块为空");return}const h=i.blockUUIDs[0];console.log("获取第一个块信息, UUID:",h);const p=await logseq.Editor.getBlock(h);if(!p){console.error("无法获取块信息, UUID:",h);return}console.log("获取到块信息:",p);const y=p.parent?{uuid:p.parent.id||p.parent.uuid}:null,k=p.page?{name:p.page.originalName||p.page.name}:null;console.log("父块信息:",y),console.log("页面信息:",k);const T={parentUUID:y?.uuid,pageName:k?.name,prevSiblingUUID:p.left?.id||null};console.log("块详细信息:",{firstBlock:p,parentInfo:y,pageInfo:k,leftBlock:p.left}),console.log("块位置信息:",T);try{const A=P.join(`
`);if(console.log("合并后的内容:",A.substring(0,100)+"..."),console.log("更新第一个块的内容, UUID:",h),await logseq.Editor.updateBlock(h,A),console.log("更新块内容成功"),i.blockUUIDs.length>1){console.log("删除其余块...");for(let m=1;m<i.blockUUIDs.length;m++){const x=i.blockUUIDs[m];console.log("删除块, UUID:",x),await logseq.Editor.removeBlock(x)}console.log("其余块删除完成")}}catch(A){console.error("更新块内容失败:",A),console.log("尝试删除后重新插入的方法..."),console.log("开始删除原始块...");for(const m of i.blockUUIDs)console.log("删除块, UUID:",m),await logseq.Editor.removeBlock(m);if(console.log("原始块删除完成"),console.log("开始插入新块..."),T.parentUUID){console.log("在父块下插入, 父块UUID:",T.parentUUID);let m=null;for(let x=0;x<P.length;x++){const O=P[x];console.log(`插入第${x+1}个块, 内容:`,O.substring(0,30)+"...");try{m=await logseq.Editor.insertBlock(T.parentUUID,O,{sibling:!0,before:!1}),console.log("插入块成功:",m?.uuid)}catch(U){console.error(`插入第${x+1}个块失败:`,U)}}}else if(T.pageName){console.log("在页面中插入, 页面名称:",T.pageName);for(let m=0;m<P.length;m++){const x=P[m];console.log(`插入第${m+1}个块到页面, 内容:`,x.substring(0,30)+"...");try{const O=await logseq.Editor.insertBlock(T.pageName,x);console.log("插入页面块成功:",O?.uuid)}catch(O){console.error(`插入第${m+1}个块到页面失败:`,O)}}}else{console.error("无法确定插入位置");return}}console.log("替换原文操作完成"),logseq.hideMainUI()}catch(P){console.error("替换内容失败:",P)}},N=async C=>{if(!i||!i.blockUUIDs.length){console.error("插入子块失败: 上下文或块UUID不存在");return}try{console.log("开始插入子块操作, 内容长度:",C.length),console.log("块UUID列表:",i.blockUUIDs);const P=ns(C);if(console.log("格式化后的块数量:",P.length),console.log("格式化后的块内容示例:",P.slice(0,2)),P.length===0){console.error("格式化后的块为空");return}const h=i.blockUUIDs[0];console.log("父块UUID:",h);const p=_(P);console.log("构建的块结构树:",p),console.log("开始递归插入块树..."),await S(h,p),console.log("插入子块操作完成"),logseq.hideMainUI()}catch(P){console.error("插入内容失败:",P)}},_=C=>{if(!C.length)return[];const P=[],h=[];for(const p of C){const y=p.match(/^(\s+)/),k=y?Math.floor(y[1].length/2):0,m={content:p.trimStart().replace(/^(\d+\.|-|\*|\+)\s+/,"")};if(h.length===0)P.push(m),h.push({node:m,indent:k});else{for(;h.length>0&&h[h.length-1].indent>=k;)h.pop();if(h.length===0)P.push(m);else{const x=h[h.length-1].node;x.children||(x.children=[]),x.children.push(m)}h.push({node:m,indent:k})}}return P},S=async(C,P)=>{console.log(`开始插入块树，父块UUID: ${C}, 块数量: ${P.length}`);const h=await logseq.Editor.getBlock(C),p=h&&h.content&&h.content.trim()!=="",y=h&&h.parent,k=y&&p;console.log(`父块状态: ${p?"非空内容":"空内容"}, 阅读模式: ${p}, 是否为子块: ${y?"是":"否"}, 是否有选中内容: ${k?"是":"否"}`);let T=[];if(h&&h.children)try{const A=await logseq.Editor.getBlock(C,{includeChildren:!0});A&&A.children&&(T=A.children),console.log(`子块数量: ${T.length}`)}catch(A){console.error("获取子块失败:",A),T=[]}for(let A=0;A<P.length;A++){const m=P[A];try{console.log(`插入第${A+1}个块, 内容: ${m.content.substring(0,30)}...`);let x;k?(x={sibling:!0,before:!1},console.log("子块选中内容插入模式: 将内容插入到子块后面")):y?(x={sibling:!1},console.log("子块插入模式: 将内容插入到子块内部")):p?(x={sibling:!0,before:!1},console.log("阅读模式插入: 作为兄弟块插入，不在前面插入")):(x={sibling:!1},console.log("普通模式插入: 作为子块插入")),console.log("使用插入选项:",x);const O=await logseq.Editor.insertBlock(C,m.content,x);O&&O.uuid?(console.log(`块插入成功, 新块UUID: ${O.uuid}`),m.children&&m.children.length>0&&(console.log(`开始插入${m.children.length}个子块...`),await S(O.uuid,m.children))):console.error("块插入失败，未返回有效的块对象")}catch(x){console.error(`插入块失败: ${m.content.substring(0,30)}...`,x)}}console.log(`块树插入完成，父块UUID: ${C}`)},w=()=>{logseq.hideMainUI()};return n?z("main",{className:"fixed inset-0 z-50 flex items-center justify-center",style:{backdropFilter:"none",WebkitBackdropFilter:"none",backgroundColor:"transparent"},onClick:C=>{t.current?.contains(C.target)||window.logseq.hideMainUI()},children:[r==="settings"&&E("div",{ref:t,className:"apple-modal bg-white dark:bg-gray-900 rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto pointer-events-auto mx-auto",style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)"},onClick:C=>C.stopPropagation(),children:E(C0,{onClose:w})}),r==="chat"&&i&&E("div",{ref:t,className:"pointer-events-auto",style:u?{position:"absolute",top:`${u.top+10}px`,left:`${u.left}px`,zIndex:9999,maxWidth:"700px",width:`${Math.max(580,Math.min(u.width*1.5,700))}px`}:{maxWidth:"700px",width:"100%"},onClick:C=>C.stopPropagation(),children:E(E0,{context:i,onClose:w,onReplace:v,onInsert:N})})]}):null}var Za=(e=>(e.SELECTION="selection",e.CURRENT_BLOCK="block",e.SELECTED_BLOCKS="blocks",e.SMART_CONTEXT="smart",e.NONE="none",e))(Za||{});async function I0(){try{const e=await logseq.Editor.getEditingCursorPosition();if(e&&e.pos){const t=await logseq.Editor.getCurrentBlock();if(t){const n=t.content,r=e.pos;if(r.start!==r.end)return n.substring(r.start,r.end)}}return null}catch(e){return console.error("获取选中文本失败:",e),null}}async function P0(){try{return await logseq.Editor.getSelectedBlocks()||[]}catch(e){return console.error("获取选中块失败:",e),[]}}async function Ad(){try{return await logseq.Editor.getCurrentBlock()}catch(e){return console.error("获取当前块失败:",e),null}}async function T0(){try{return(await logseq.Editor.getCurrentPage())?.originalName?.toString()||""}catch(e){return console.error("获取页面标题失败:",e),""}}function A0(e,t){const n=[{theme:"学习",keywords:["学习","笔记","课程","教育","知识","考试"]},{theme:"工作",keywords:["工作","项目","会议","报告","审核","计划"]},{theme:"生活",keywords:["生活","日记","旅行","饮食","健康","休闲"]},{theme:"阅读",keywords:["阅读","书籍","文章","文学","诗歌","小说"]},{theme:"编程",keywords:["编程","code","代码","function","class","API","bug","开发"]}],r=`${e} ${t}`.toLowerCase(),o=n.map(i=>{let s=0;return i.keywords.forEach(u=>{const d=(e.toLowerCase().match(new RegExp(u.toLowerCase(),"g"))||[]).length,f=(r.match(new RegExp(u.toLowerCase(),"g"))||[]).length;s+=d*3+f}),{theme:i.theme,score:s}});return o.sort((i,s)=>s.score-i.score),o[0].score>0?o[0].theme:""}async function L0(e,t){try{const n=await logseq.Editor.getBlock(e);if(!n)return[];const r=n.parent?await logseq.Editor.getBlock(n.parent.id):null,o=await logseq.Editor.getPage(n.page.id);if(!o)return[n];const i=await logseq.Editor.getPageBlocksTree(o.name);if(!i||i.length===0)return[n];const s=(N,_=[])=>{for(const S of N)_.push(S),S.children&&S.children.length>0&&s(S.children,_);return _},u=s(i),d=u.findIndex(N=>N.uuid===e);if(d===-1)return[n];const f=Math.max(0,d-t),v=Math.min(u.length-1,d+t);return u.slice(f,v+1)}catch(n){return console.error("获取块上下文失败:",n),[]}}async function yl(e,t,n,r){if(!await logseq.Editor.getBlock(e))return{type:"none",content:"",blockUUIDs:[]};const i=await L0(e,t),s=i.map(f=>f.content).join(`

`),u=n?await T0():"",d={type:"smart",content:s,blockUUIDs:i.map(f=>f.uuid)};if(u&&(d.pageTitle=u,d.content=`页面: ${u}

${d.content}`),r){const f=A0(u,s);f&&(d.theme=f,d.content=`主题: ${f}

${d.content}`)}return d}async function vl(e){const t=await Gt(),{enableSmartContext:n,contextRange:r,includePageTitle:o,enableThemeDetection:i}=t.contextSettings;if(e){const f=await logseq.Editor.getBlock(e);if(f)return n?await yl(e,r,o,i):{type:"block",content:f.content,blockUUIDs:[e]}}const s=await I0();if(s){const f=await Ad();return f&&n?await yl(f.uuid,r,o,i):{type:"selection",content:s,blockUUIDs:f?[f.uuid]:[]}}const u=await P0();if(u&&u.length>0)return n&&u.length===1?await yl(u[0].uuid,r,o,i):{type:"blocks",content:u.map(v=>v.content).join(`

`),blockUUIDs:u.map(v=>v.uuid)};const d=await Ad();return d?n?await yl(d.uuid,r,o,i):{type:"block",content:d.content,blockUUIDs:[d.uuid]}:{type:"none",content:"",blockUUIDs:[]}}const O0=(e,...t)=>String.raw(e,...t),M0=b0.id,j0=Qp(document.getElementById("app"));let eu;async function U0(){console.info(`#${M0}: MAIN`),await w0(),await D0(),logseq.useSettingsSchema([{key:"apiSettings",title:"API设置",type:"heading",default:"",description:"API相关配置"},{key:"apiUrl",type:"string",default:"https://api.openai.com/v1/chat/completions",title:"API URL",description:"AI服务的API地址（兼容旧版本）"},{key:"apiKey",type:"string",default:"",title:"API Key",description:"访问AI服务所需的API密钥（兼容旧版本）"},{key:"modelName",type:"string",default:"gpt-3.5-turbo",title:"模型名称",description:"使用的AI模型名称（兼容旧版本）"},{key:"temperature",type:"number",default:.7,title:"温度",description:"控制生成文本的随机性，范围从0（确定性高）到2（多样性高）"},{key:"maxTokens",type:"number",default:2e3,title:"最大输出标记数",description:"模型将生成的最大标记数量"},{key:"requestTimeout",type:"number",default:60,title:"API请求超时时间",description:"API请求的超时时间（秒），增加该值可减少超时错误"},{key:"maxRetries",type:"number",default:3,title:"最大重试次数",description:"API请求失败时的最大重试次数"},{key:"retryDelay",type:"number",default:1,title:"重试延迟",description:"重试之间的延迟时间（秒）"},{key:"enableHistory",type:"boolean",default:!1,title:"启用历史记录",description:"是否保存聊天历史记录"},{key:"uiSettings.autoResponseOnOpen",type:"boolean",default:!1,title:"激活窗口时自动响应",description:"当使用快捷键激活聊天窗口时，是否自动发送请求"},{key:"contextSettingsHeading",title:"上下文设置",type:"heading",default:"",description:"上下文获取相关配置"},{key:"contextSettings.enableSmartContext",type:"boolean",default:!0,title:"启用智能上下文",description:"自动获取相关的前后文内容"},{key:"contextSettings.contextRange",type:"number",default:3,title:"上下文范围",description:"获取当前块前后的块数量"},{key:"contextSettings.includePageTitle",type:"boolean",default:!0,title:"包含页面标题",description:"在上下文中包含当前页面的标题"},{key:"contextSettings.enableThemeDetection",type:"boolean",default:!0,title:"启用主题检测",description:"自动识别当前页面的主题"},{key:"uiSettingsHeading",title:"UI设置",type:"heading",default:"",description:"用户界面相关配置"},{key:"uiSettings.rememberWindowSize",type:"boolean",default:!0,title:"记住窗口大小",description:"在会话之间记住对话窗口的大小"},{key:"uiSettings.rememberWindowPosition",type:"boolean",default:!0,title:"记住窗口位置",description:"在会话之间记住对话窗口的位置"},{key:"uiSettings.defaultWidth",type:"number",default:600,title:"默认窗口宽度",description:"对话窗口的默认宽度（像素）"},{key:"uiSettings.defaultHeight",type:"number",default:500,title:"默认窗口高度",description:"对话窗口的默认高度（像素）"},{key:"uiSettings.enableDetachedWindow",type:"boolean",default:!0,title:"启用分离式窗口",description:"允许将对话窗口分离为独立窗口"},{key:"uiSettings.theme",type:"enum",default:"system",title:"主题",description:"选择聊天界面的主题",enumChoices:["system","light","dark","custom"],enumPicker:"select"},{key:"uiSettings.customThemeColor",type:"string",default:"#0077ff",title:"自定义主题颜色",description:"当选择自定义主题时的主色调（十六进制颜色代码）"},{key:"uiSettings.showToolbar",type:"boolean",default:!0,title:"显示工具栏",description:"在对话窗口中显示快捷工具栏"},{key:"knowledgeBaseSettingsHeading",title:"知识库设置",type:"heading",default:"",description:"知识库集成相关配置"},{key:"knowledgeBaseSettings.enableKnowledgeBase",type:"boolean",default:!0,title:"启用知识库集成",description:"允许AI访问您的Logseq知识库，使回答更加个性化和相关"},{key:"knowledgeBaseSettings.searchLimit",type:"number",default:5,title:"搜索结果数量限制",description:"每次查询返回的知识库搜索结果数量"},{key:"knowledgeBaseSettings.includePages",type:"boolean",default:!0,title:"包含页面",description:"在搜索结果中包含页面"},{key:"knowledgeBaseSettings.excludeArchived",type:"boolean",default:!0,title:"排除归档页面",description:"从搜索结果中排除归档页面"},{key:"knowledgeBaseSettings.includeRecentContent",type:"boolean",default:!0,title:"包含最近内容",description:"在搜索结果中包含最近编辑的内容"},{key:"knowledgeBaseSettings.recentContentLimit",type:"number",default:3,title:"最近内容数量限制",description:"返回的最近编辑内容数量"}]),ia();function e(){return{show(){eu=void 0,ia(),logseq.showMainUI()},async openAIChat(){const s=await Td();console.log("鼠标下方的块:",s);let u;if(s.uuid){const d=await logseq.Editor.getBlock(s.uuid);if(d){const f=await Gt(),{enableSmartContext:v,contextRange:N,includePageTitle:_,enableThemeDetection:S}=f.contextSettings;v?u=await vl(s.uuid):u={type:Za.CURRENT_BLOCK,content:d.content,blockUUIDs:[s.uuid]},console.log("使用鼠标下方块的上下文:",u)}}u||(u=await vl(),console.log("使用默认方式获取的上下文:",u)),eu=u,ia(),logseq.showMainUI()},async quickAIReply(){const s=await Td();console.log("鼠标下方的块 (快速AI回复):",s);let u;if(s.uuid){const d=await logseq.Editor.getBlock(s.uuid);if(d){const f=await Gt(),{enableSmartContext:v,contextRange:N,includePageTitle:_,enableThemeDetection:S}=f.contextSettings;v?u=await vl(s.uuid):u={type:Za.CURRENT_BLOCK,content:d.content,blockUUIDs:[s.uuid]},console.log("使用鼠标下方块的上下文 (快速AI回复):",u)}}if(u||(u=await vl(),console.log("使用默认方式获取的上下文 (快速AI回复):",u)),!u||!u.content){logseq.UI.showMsg("没有找到有效的上下文内容","warning");return}logseq.UI.showMsg("AI正在思考中...","info");try{let d="";const f={onChunk:v=>{d+=v},onComplete:async v=>{if(u&&u.blockUUIDs&&u.blockUUIDs.length>0){const N=u.blockUUIDs[0];N?(await logseq.Editor.insertBlock(N,v,{sibling:!1}),logseq.UI.showMsg("AI回复已插入","success")):logseq.UI.showMsg("无法插入回复：找不到父块","warning")}else logseq.UI.showMsg("无法插入回复：上下文不完整","warning")},onError:v=>{logseq.UI.showMsg(`AI回复失败: ${v}`,"error")}};await Ja(u.content,u,f,"请用中文简洁地回复以下内容：")}catch(d){console.error("快速回复出错:",d),logseq.UI.showMsg("AI回复失败，请重试","error")}}}}const t=e();logseq.provideModel(t),logseq.setMainUIInlineStyle({zIndex:9}),logseq.Editor.registerSlashCommand("AI聊天",async()=>t.openAIChat()),logseq.Editor.registerSlashCommand("快速AI回复",async()=>t.quickAIReply());const n=await Gt();logseq.App.registerCommandPalette({key:"open-ai-chat",label:"打开AI聊天",keybinding:{binding:n.keybindings.openChat.binding,mac:n.keybindings.openChat.mac}},async()=>t.openAIChat()),logseq.App.registerCommandPalette({key:"quick-ai-reply",label:"快速AI回复",keybinding:{binding:n.keybindings.quickReply.binding,mac:n.keybindings.quickReply.mac}},async()=>t.quickAIReply());let r=JSON.stringify(n.keybindings),o=!0;logseq.onSettingsChanged(async s=>{if(o){o=!1;return}if(s.keybindings){const u=JSON.stringify(s.keybindings);u!==r&&(console.log("快捷键设置已更新，请重启 Logseq 以应用新的快捷键设置"),logseq.UI.showMsg("快捷键设置已更新，请重启 Logseq 以应用新的快捷键设置","info",{timeout:5e3}),r=u)}});const i="ai-chat-plugin-open";logseq.provideStyle(O0`
    .${i} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${i}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `),logseq.App.registerUIItem("toolbar",{key:i,template:`
    <a data-on-click="show">
        <div class="${i}">🤖</div>
    </a>
`})}async function D0(){await logseq.App.getStateFromStore("ui/theme")==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),logseq.App.onThemeModeChanged(({mode:n})=>{n==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")})}function ia(){j0.render(E(Hd.StrictMode,{children:E(N0,{chatContext:eu})}))}logseq.ready(U0).catch(console.error);
