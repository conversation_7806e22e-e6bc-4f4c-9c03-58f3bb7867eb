import { LSPluginUserEvents } from "@logseq/libs/dist/LSPlugin.user";
import React from "react";

let _visible = logseq.isMainUIVisible;

function subscribeLogseqEvent<T extends LSPluginUserEvents>(
  eventName: T,
  handler: (...args: any) => void
) {
  // 使用类型断言解决类型错误
  (logseq as any).on(eventName, handler);
  return () => {
    (logseq as any).off(eventName, handler);
  };
}

const subscribeToUIVisible = (onChange: () => void) =>
  subscribeLogseqEvent("ui:visible:changed", ({ visible }) => {
    _visible = visible;
    onChange();
  });

export const useAppVisible = () => {
  return React.useSyncExternalStore(subscribeToUIVisible, () => _visible);
};

/**
 * 获取当前编辑器焦点块
 * @returns 返回当前编辑器焦点的块UUID
 */
async function getCurrentEditingBlock(): Promise<string | null> {
  try {
    // 尝试获取当前编辑的块
    const currentBlock = await logseq.Editor.getCurrentBlock();
    if (currentBlock) {
      console.log(`获取到当前编辑块: ${currentBlock.uuid}`);
      return currentBlock.uuid;
    }

    // 如果没有当前编辑块，尝试获取选中的块
    const selectedBlocks = await logseq.Editor.getSelectedBlocks();
    if (selectedBlocks && selectedBlocks.length > 0) {
      console.log(`获取到选中块: ${selectedBlocks[0].uuid}`);
      return selectedBlocks[0].uuid;
    }

    console.log('未找到当前编辑或选中的块');
    return null;
  } catch (error) {
    console.error('获取当前编辑块失败:', error);
    return null;
  }
}

/**
 * 存储最后的鼠标位置
 */
let lastMousePosition = { x: 0, y: 0 };

/**
 * 监听鼠标移动事件，记录最后的鼠标位置
 */
if (typeof window !== 'undefined') {
  document.addEventListener('mousemove', (e) => {
    lastMousePosition.x = e.clientX;
    lastMousePosition.y = e.clientY;
  });
}

/**
 * 块检测器
 * 提供多种策略来准确检测当前活动的块
 */
class BlockDetector {
  private lastKnownBlock: { uuid: string; timestamp: number } | null = null;
  private blockCache = new Map<string, { element: HTMLElement; timestamp: number }>();

  /**
   * 清除过期的缓存
   */
  private clearExpiredCache() {
    const now = Date.now();
    const expireTime = 30000; // 30秒过期

    for (const [uuid, cache] of this.blockCache.entries()) {
      if (now - cache.timestamp > expireTime) {
        this.blockCache.delete(uuid);
      }
    }
  }

  /**
   * 获取当前活动的块（多策略，修复同级块问题）
   */
  async getActiveBlock(): Promise<{ element: HTMLElement | null, uuid: string | null }> {
    this.clearExpiredCache();

    // 策略1: 尝试获取当前编辑的块（优先级最高，最准确）
    const editingBlock = await this.getCurrentEditingBlock();
    if (editingBlock.uuid) {
      console.log('策略1成功：获取到当前编辑块');
      // 清除旧的缓存，确保获取最新状态
      this.clearLastKnownBlock();
      return editingBlock;
    }

    // 策略2: 尝试获取选中的块（比鼠标位置更准确）
    const selectedBlock = await this.getSelectedBlock();
    if (selectedBlock.uuid) {
      console.log('策略2成功：获取到选中的块');
      this.clearLastKnownBlock();
      return selectedBlock;
    }

    // 策略3: 尝试获取鼠标下方的块
    const mouseBlock = await this.getBlockUnderMouse();
    if (mouseBlock.uuid) {
      console.log('策略3成功：获取到鼠标下方的块');
      this.clearLastKnownBlock();
      return mouseBlock;
    }

    // 策略4: 尝试获取焦点块
    const focusedBlock = await this.getFocusedBlock();
    if (focusedBlock.uuid) {
      console.log('策略4成功：获取到焦点块');
      this.clearLastKnownBlock();
      return focusedBlock;
    }

    // 策略5: 使用最后已知的块（缩短缓存时间，避免过期数据）
    if (this.lastKnownBlock && Date.now() - this.lastKnownBlock.timestamp < 10000) { // 从60秒改为10秒
      const lastBlock = await this.getBlockByUUID(this.lastKnownBlock.uuid);
      if (lastBlock.uuid) {
        console.log('策略5成功：使用最后已知的块（10秒内）');
        return lastBlock;
      }
    }

    console.log('所有策略都失败，返回空结果');
    return { element: null, uuid: null };
  }

  /**
   * 清除最后已知的块缓存
   */
  private clearLastKnownBlock() {
    this.lastKnownBlock = null;
  }

  /**
   * 策略1: 获取当前编辑的块
   */
  private async getCurrentEditingBlock(): Promise<{ element: HTMLElement | null, uuid: string | null }> {
    try {
      const currentBlock = await logseq.Editor.getCurrentBlock();
      if (currentBlock) {
        const element = document.querySelector(`[blockid="${currentBlock.uuid}"]`) as HTMLElement;
        if (element) {
          this.updateLastKnownBlock(currentBlock.uuid);
          return { element, uuid: currentBlock.uuid };
        }
      }
    } catch (error) {
      console.debug('获取当前编辑块失败:', error);
    }
    return { element: null, uuid: null };
  }

  /**
   * 策略2: 获取鼠标下方的块
   */
  private async getBlockUnderMouse(): Promise<{ element: HTMLElement | null, uuid: string | null }> {
    try {
      const mousePos = this.getMousePosition();
      if (!mousePos) {
        return { element: null, uuid: null };
      }

      const elementUnderMouse = document.elementFromPoint(mousePos.x, mousePos.y) as HTMLElement;
      if (!elementUnderMouse) {
        return { element: null, uuid: null };
      }

      const blockInfo = this.findBlockElement(elementUnderMouse);
      if (blockInfo.uuid) {
        this.updateLastKnownBlock(blockInfo.uuid);
        return blockInfo;
      }
    } catch (error) {
      console.debug('获取鼠标下方块失败:', error);
    }
    return { element: null, uuid: null };
  }

  /**
   * 策略3: 获取选中的块
   */
  private async getSelectedBlock(): Promise<{ element: HTMLElement | null, uuid: string | null }> {
    try {
      const selectedBlocks = await logseq.Editor.getSelectedBlocks();
      if (selectedBlocks && selectedBlocks.length > 0) {
        const firstSelected = selectedBlocks[0];
        const element = document.querySelector(`[blockid="${firstSelected.uuid}"]`) as HTMLElement;
        if (element) {
          this.updateLastKnownBlock(firstSelected.uuid);
          return { element, uuid: firstSelected.uuid };
        }
      }
    } catch (error) {
      console.debug('获取选中块失败:', error);
    }
    return { element: null, uuid: null };
  }

  /**
   * 策略4: 获取焦点块
   */
  private async getFocusedBlock(): Promise<{ element: HTMLElement | null, uuid: string | null }> {
    try {
      // 查找当前有焦点的块元素
      const activeElement = document.activeElement as HTMLElement;
      if (activeElement) {
        const blockInfo = this.findBlockElement(activeElement);
        if (blockInfo.uuid) {
          this.updateLastKnownBlock(blockInfo.uuid);
          return blockInfo;
        }
      }
    } catch (error) {
      console.debug('获取焦点块失败:', error);
    }
    return { element: null, uuid: null };
  }

  /**
   * 根据UUID获取块
   */
  private async getBlockByUUID(uuid: string): Promise<{ element: HTMLElement | null, uuid: string | null }> {
    try {
      const block = await logseq.Editor.getBlock(uuid);
      if (block) {
        const element = document.querySelector(`[blockid="${uuid}"]`) as HTMLElement;
        if (element) {
          return { element, uuid };
        }
      }
    } catch (error) {
      console.debug('根据UUID获取块失败:', error);
    }
    return { element: null, uuid: null };
  }

  /**
   * 获取鼠标位置
   */
  private getMousePosition(): { x: number; y: number } | null {
    // 尝试从当前事件获取鼠标位置
    const currentEvent = window.event;
    if (currentEvent && 'clientX' in currentEvent && 'clientY' in currentEvent) {
      const mouseEvent = currentEvent as MouseEvent;
      return { x: mouseEvent.clientX, y: mouseEvent.clientY };
    }

    // 使用最后记录的鼠标位置
    if (lastMousePosition.x !== 0 || lastMousePosition.y !== 0) {
      return { x: lastMousePosition.x, y: lastMousePosition.y };
    }

    return null;
  }

  /**
   * 查找块元素（改进的DOM查找逻辑）
   */
  private findBlockElement(element: HTMLElement): { element: HTMLElement | null, uuid: string | null } {
    let currentElement = element;
    let attempts = 0;
    const maxAttempts = 10; // 防止无限循环

    while (currentElement && attempts < maxAttempts) {
      // 检查当前元素是否有blockid
      const blockId = currentElement.getAttribute('blockid');
      if (blockId) {
        return { element: currentElement, uuid: blockId };
      }

      // 检查data-blockid属性
      const dataBlockId = currentElement.getAttribute('data-blockid');
      if (dataBlockId) {
        return { element: currentElement, uuid: dataBlockId };
      }

      // 检查是否是块容器
      if (currentElement.classList.contains('ls-block') ||
          currentElement.classList.contains('block-content') ||
          currentElement.classList.contains('block-main')) {
        // 在子元素中查找blockid
        const blockElement = currentElement.querySelector('[blockid]') as HTMLElement;
        if (blockElement) {
          const blockId = blockElement.getAttribute('blockid');
          if (blockId) {
            return { element: blockElement, uuid: blockId };
          }
        }
      }

      // 向上查找父元素
      currentElement = currentElement.parentElement as HTMLElement;
      attempts++;

      // 如果到达body或html，停止查找
      if (!currentElement || currentElement === document.body || currentElement === document.documentElement) {
        break;
      }
    }

    return { element: null, uuid: null };
  }

  /**
   * 更新最后已知的块
   */
  private updateLastKnownBlock(uuid: string) {
    this.lastKnownBlock = {
      uuid,
      timestamp: Date.now()
    };
  }
}

// 创建全局实例
const blockDetector = new BlockDetector();

/**
 * 获取鼠标位置下方的块元素
 * @returns 返回鼠标下方的块元素和UUID
 */
export async function getBlockUnderMouse(): Promise<{ element: HTMLElement | null, uuid: string | null }> {
  try {
    console.log('开始块检测...');
    const result = await blockDetector.getActiveBlock();

    if (result.uuid) {
      console.log(`块检测成功，UUID: ${result.uuid}`);

      // 获取块的详细信息用于调试
      try {
        const blockInfo = await logseq.Editor.getBlock(result.uuid);
        if (blockInfo) {
          console.log(`块信息 - 内容: ${blockInfo.content.substring(0, 50)}...`);
          if (blockInfo.parent) {
            console.log(`块信息 - 父块: ${blockInfo.parent.id}`);
          }
          console.log(`块信息 - 层级: ${blockInfo.level || 0}`);
        }
      } catch (debugError) {
        console.debug('获取块详细信息失败:', debugError);
      }
    } else {
      console.log('块检测失败，未找到有效块');
    }

    return result;
  } catch (error) {
    console.error('块检测出错:', error);

    // 降级到原始方法
    try {
      const currentBlockUUID = await getCurrentEditingBlock();
      if (currentBlockUUID) {
        const blockElement = document.querySelector(`[blockid="${currentBlockUUID}"]`) as HTMLElement;
        if (blockElement) {
          console.log('降级方案成功');
          return { element: blockElement, uuid: currentBlockUUID };
        }
      }
    } catch (fallbackError) {
      console.error('降级方案也失败:', fallbackError);
    }

    return { element: null, uuid: null };
  }
}
