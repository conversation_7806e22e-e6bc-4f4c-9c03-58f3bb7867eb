@tailwind base;
@tailwind components;
@tailwind utilities;

/* 苹果设计风格组件 */
.apple-modal {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all 0.3s ease;
  overflow: hidden;
  resize: both;
  min-width: 300px;
  min-height: 300px;
}

.dark .apple-modal {
  background-color: rgba(30, 30, 30, 0.7);
  border: 1px solid rgba(60, 60, 60, 0.2);
}

/* 可调整大小的窗口手柄 */
.resize-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: nwse-resize;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10'%3E%3Cpath fill='%23888' d='M0 10L10 0V10H0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

.resize-handle:hover {
  opacity: 0.8;
}

/* 分离式窗口 */
.detached-window {
  position: fixed;
  top: 50px;
  left: 50px;
  z-index: 9999;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
}

/* 自定义主题支持 */
.theme-custom {
  --theme-primary-color: #0077ff;
  --theme-secondary-color: #f0f7ff;
  --theme-text-color: #333333;
  --theme-border-color: rgba(0, 119, 255, 0.2);
}

.dark.theme-custom {
  --theme-primary-color: #4096ff;
  --theme-secondary-color: #1a2733;
  --theme-text-color: #e0e0e0;
  --theme-border-color: rgba(64, 150, 255, 0.2);
}

.theme-light {
  --theme-primary-color: #0077ff;
  --theme-secondary-color: #f0f7ff;
  --theme-text-color: #333333;
  --theme-border-color: rgba(0, 0, 0, 0.1);
}

.theme-dark {
  --theme-primary-color: #4096ff;
  --theme-secondary-color: #1a2733;
  --theme-text-color: #e0e0e0;
  --theme-border-color: rgba(255, 255, 255, 0.1);
  background-color: rgba(30, 30, 30, 0.7);
}

.apple-input {
  border-radius: 10px;
  transition: all 0.2s ease;
  background-color: rgba(240, 240, 240, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .apple-input {
  background-color: rgba(50, 50, 50, 0.8);
  border: 1px solid rgba(80, 80, 80, 0.2);
  color: #e0e0e0;
}

.apple-input:focus {
  box-shadow: 0 0 0 4px rgba(0, 125, 250, 0.2);
  border-color: #0077ff;
}

.dark .apple-input:focus {
  box-shadow: 0 0 0 4px rgba(64, 156, 255, 0.2);
  border-color: #4096ff;
}

.apple-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23888' d='M4 8l4-4 1 1-5 5-2.5-2.5 1-1z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
  transition: all 0.2s ease;
}

.dark .apple-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23aaa' d='M4 8l4-4 1 1-5 5-2.5-2.5 1-1z'/%3E%3C/svg%3E");
}

.apple-spinner {
  border-width: 2px;
  animation: spin 1s linear infinite;
}

/* 打字机效果与流式响应 */
.typewriter-text {
  word-break: break-word;
}

.blinking-cursor {
  display: inline-block;
  width: 2px;
  height: 18px;
  background-color: currentColor;
  margin-left: 1px;
  animation: blink 1s step-end infinite;
  vertical-align: text-bottom;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.ai-response {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 苹果风格按钮 */
.apple-button {
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.apple-button:hover {
  transform: translateY(-1px);
}

.apple-button:active {
  transform: translateY(1px);
}

/* 工具栏样式 - 苹果风格 */
.toolbar {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  padding: 8px;
  border-top: 1px solid var(--theme-border-color, rgba(0, 0, 0, 0.1));
  background-color: rgba(245, 245, 245, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dark .toolbar {
  background-color: rgba(40, 40, 40, 0.8);
  border-top: 1px solid var(--theme-border-color, rgba(255, 255, 255, 0.1));
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-item {
  display: flex;
  align-items: center;
}

.toolbar-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--theme-text-color, #333);
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dark .toolbar-button {
  background-color: rgba(255, 255, 255, 0.08);
  color: var(--theme-text-color, #e0e0e0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.toolbar-button:hover {
  background-color: rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .toolbar-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.toolbar-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.toolbar-button svg {
  width: 16px;
  height: 16px;
}

/* 窗口标题栏 */
.window-titlebar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--theme-border-color, rgba(0, 0, 0, 0.1));
  cursor: move;
}

.dark .window-titlebar {
  border-bottom: 1px solid var(--theme-border-color, rgba(255, 255, 255, 0.1));
}

.window-controls {
  display: flex;
  gap: 8px;
}

.window-control-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.window-control-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .window-control-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 底部操作按钮区域 */
.floating-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background-color: rgba(245, 245, 245, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  opacity: 1;
  z-index: 10;
}

.dark .floating-actions {
  background-color: rgba(40, 40, 40, 0.7);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* 悬浮按钮样式 */
.floating-button {
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.85rem;
  padding: 6px 12px;
  transition: all 0.2s ease;
  opacity: 0.85;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dark .floating-button {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.floating-button:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.floating-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 自动刷新相关样式 */
.auto-refresh-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.refresh-button-active {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.dark .refresh-button-active {
  background-color: rgba(59, 130, 246, 0.2);
  color: rgb(147, 197, 253);
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 脉冲动画 */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

.animate-pulse {
  animation: pulse-dot 2s infinite;
}

/* 按钮指示器 - 已移除 */

/* 阅读模式样式 */
.reading-mode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999; /* 增加z-index确保在最上层 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

/* 阅读模式下的浮动追问按钮 */
.reading-mode-followup-button {
  position: fixed;
  bottom: 80px;
  right: 30px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #0077ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 100;
  transition: all 0.2s ease;
  border: none;
}

.dark .reading-mode-followup-button {
  background-color: #4096ff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
}

.reading-mode-followup-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  background-color: #0066dd;
}

.reading-mode-followup-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.reading-mode-followup-button svg {
  width: 20px;
  height: 20px;
}

.reading-mode-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 650px;
  max-width: 85vw;
  height: 500px;
  max-height: 75vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative; /* 确保子元素定位正确 */
}

.dark .reading-mode-container {
  background-color: #1e1e1e;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.reading-mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #f8f8f8;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .reading-mode-header {
  border-bottom: 1px solid #333;
  background-color: #252525;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.reading-mode-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: auto;
}

.dark .reading-mode-title {
  color: #eee;
}

.reading-mode-close {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  background-color: #e8e8e8;
  margin-left: 8px;
}

.dark .reading-mode-close {
  color: #ddd;
  background-color: #444;
}

.reading-mode-close:hover {
  background-color: #d0d0d0;
  color: #333;
}

.dark .reading-mode-close:hover {
  background-color: #555;
  color: #fff;
}

.reading-mode-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  line-height: 1.6;
  font-size: 15px;
  color: #333;
  white-space: pre-wrap;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  /* 确保内容区域有足够的空间，不被头部和底部遮挡 */
  padding-top: 16px;
  padding-bottom: 16px;
  /* 设置滚动行为 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

.dark .reading-mode-content {
  color: #eee;
}

.reading-mode-content-tip {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  color: #888;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-style: italic;
  opacity: 0.8;
  pointer-events: none;
  animation: fadeOut 5s forwards;
}

.dark .reading-mode-content-tip {
  background-color: rgba(255, 255, 255, 0.05);
  color: #aaa;
}

/* 移除了不再需要的右键菜单按钮样式 */

@keyframes fadeOut {
  0% { opacity: 0.8; }
  70% { opacity: 0.8; }
  100% { opacity: 0; }
}

.reading-mode-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  border-top: 1px solid #eee;
  background-color: #f8f8f8;
  position: sticky;
  bottom: 0;
  z-index: 10;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

.dark .reading-mode-footer {
  border-top: 1px solid #333;
  background-color: #252525;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.3);
}

.reading-mode-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.reading-mode-buttons {
  display: flex;
  gap: 8px;
}

.reading-mode-followup {
  padding: 12px 16px;
  border-top: 1px solid #eee;
  background-color: #f8f8f8;
  position: sticky;
  bottom: 60px;
  z-index: 10;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

.dark .reading-mode-followup {
  border-top: 1px solid #333;
  background-color: #252525;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.3);
}

.reading-mode-tip {
  font-size: 13px;
  color: #888;
  font-style: italic;
}

.reading-mode-button {
  padding: 8px 14px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  background-color: #e0e0e0;
  color: #333;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .reading-mode-button {
  background-color: #444;
  color: #eee;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.reading-mode-button:hover:not(:disabled) {
  background-color: #d0d0d0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.dark .reading-mode-button:hover:not(:disabled) {
  background-color: #555;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.reading-mode-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.reading-mode-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reading-mode-button.primary {
  background-color: #0077ff;
  color: white;
}

.reading-mode-button.primary:hover:not(:disabled) {
  background-color: #0066dd;
}

/* 阅读提示样式 - 已移除 */
.reading-hint {
  display: none;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 6px 0;
  min-width: 160px;
  z-index: 9999999; /* 确保在最上层 */
  animation: fadeIn 0.15s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
  /* 确保菜单可见 */
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  pointer-events: auto !important;
}

.dark .context-menu {
  background-color: #2a2a2a;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.dark .context-menu-item {
  color: #eee;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.dark .context-menu-item:hover {
  background-color: #3a3a3a;
}

.context-menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.context-menu-item.disabled:hover {
  background-color: transparent;
}

.dark .context-menu-item.disabled:hover {
  background-color: transparent;
}

.context-menu-item-icon {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.dark .context-menu-item-icon {
  color: #aaa;
}

.context-menu-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 6px 0;
}

.dark .context-menu-divider {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 追问对话框样式 */
.followup-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 9999;
  /* 移除模糊效果，使阅读模式内容更清晰 */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

.followup-dialog {
  width: 500px;
  max-width: 90vw;
  max-height: 50vh;
  overflow-y: auto;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.2);
  animation: dialogSlideUp 0.2s ease-out;
  margin-bottom: 0;
}

@keyframes dialogSlideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dark .followup-dialog {
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.4);
}

/* 添加聊天历史样式 */
.chat-history-container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  padding: 8px 12px;
  border-radius: 8px;
  position: relative;
}

.history-item-header {
  font-weight: 600;
  margin-bottom: 6px;
  color: #555;
}

.dark .history-item-header {
  color: #ccc;
}

.history-item-content {
  line-height: 1.5;
}

.question-item {
  background-color: #f5f5f5;
  border-left: 3px solid #3b82f6;
}

.dark .question-item {
  background-color: #333;
  border-left: 3px solid #3b82f6;
}

.answer-item {
  background-color: #f0f7ff;
  border-left: 3px solid #10b981;
}

.dark .answer-item {
  background-color: #1a2e3a;
  border-left: 3px solid #10b981;
}

.followup-item {
  background-color: #f5f5f5;
  border-left: 3px solid #8b5cf6;
}

.dark .followup-item {
  background-color: #333;
  border-left: 3px solid #8b5cf6;
}

.followup-answer-item {
  background-color: #f0f7ff;
  border-left: 3px solid #f59e0b;
}

.dark .followup-answer-item {
  background-color: #1a2e3a;
  border-left: 3px solid #f59e0b;
}

.history-divider {
  height: 1px;
  background-color: #e5e7eb;
  border: none;
  margin: 12px 0 8px;
}

.dark .history-divider {
  background-color: #4b5563;
}

.no-history-message {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-style: italic;
}

.loading-item {
  background-color: #f5f5f5;
  border-left: 3px solid #9ca3af;
  font-style: italic;
}

.dark .loading-item {
  background-color: #333;
  border-left: 3px solid #9ca3af;
}

