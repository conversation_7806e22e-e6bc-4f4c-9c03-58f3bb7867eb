import React, { useRef, useState, useEffect } from "react";
import { useAppVisible } from "./utils";
import { SettingsUI } from "./components/SettingsUI";
import { ChatModal } from "./components/ChatModal";
import { ContextData, ContextSourceType, getContext } from "./contextManager";
import { formatResponseToBlocks, prepareBlocksForInsertion, BlockEntity } from "./formatter";

// 应用页面类型
enum AppPageType {
  SETTINGS = 'settings',
  CHAT = 'chat'
}

// 应用程序属性
interface AppProps {
  chatContext?: ContextData;
  isNewSession?: boolean;
}

function App({ chatContext, isNewSession }: AppProps = {}) {
  const innerRef = useRef<HTMLDivElement>(null);
  const visible = useAppVisible();
  const [currentPage, setCurrentPage] = useState<AppPageType>(AppPageType.SETTINGS);
  const [context, setContext] = useState<ContextData | undefined>(chatContext);
  const [blockPosition, setBlockPosition] = useState<{top: number, left: number, width: number} | null>(null);
  const [forceReset, setForceReset] = useState<boolean>(false);

  // 当chatContext变化时更新state
  useEffect(() => {
    if (chatContext) {
      setContext(chatContext);
      setCurrentPage(AppPageType.CHAT);
      // 获取块的位置
      setTimeout(getBlockPosition, 50);
    } else {
      // 当chatContext为undefined时，始终显示设置页面
      setCurrentPage(AppPageType.SETTINGS);
    }
  }, [chatContext]);

  // 当isNewSession变化时，强制重置聊天状态
  useEffect(() => {
    if (isNewSession) {
      console.log("App组件检测到新会话标志，强制重置聊天状态");
      setForceReset(true);
      // 重置标志在下一个渲染周期
      setTimeout(() => {
        setForceReset(false);
      }, 100);
    }
  }, [isNewSession]);

  // 获取块的位置
  const getBlockPosition = async () => {
    if (!context || !context.blockUUIDs.length) {
      console.log('无法获取块位置：上下文或块UUID不存在');
      return;
    }

    try {
      const blockUUID = context.blockUUIDs[0];
      // 使用DOM方法获取块的位置
      const blockElement = document.querySelector(`[blockid="${blockUUID}"]`);
      if (blockElement) {
        const rect = blockElement.getBoundingClientRect();
        setBlockPosition({
          top: rect.bottom,
          left: rect.left,
          width: rect.width
        });
      } else {
        console.log(`无法找到块元素，UUID: ${blockUUID}`);
        // 设置默认位置
        setBlockPosition(null);
      }
    } catch (error) {
      console.error('获取块位置失败:', error);
      // 设置默认位置
      setBlockPosition(null);
    }
  };

  // 处理替换原文
  const handleReplace = async (content: string) => {
    if (!context || !context.blockUUIDs.length) {
      console.error('替换原文失败: 上下文或块UUID不存在');
      return;
    }

    try {
      console.log('开始替换原文操作, 内容长度:', content.length);
      console.log('块UUID列表:', context.blockUUIDs);

      // 格式化 AI 响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(content);
      console.log('格式化后的块数量:', formattedBlocks.length);
      console.log('格式化后的块内容示例:', formattedBlocks.slice(0, 2));

      if (formattedBlocks.length === 0) {
        console.error('格式化后的块为空');
        return;
      }

      // 先获取第一个块的信息，然后再删除
      const firstUUID = context.blockUUIDs[0];
      console.log('获取第一个块信息, UUID:', firstUUID);
      const firstBlock = await logseq.Editor.getBlock(firstUUID);

      if (!firstBlock) {
        console.error('无法获取块信息, UUID:', firstUUID);
        return;
      }

      console.log('获取到块信息:', firstBlock);

      // 保存父块信息和页面信息
      const parentInfo = firstBlock.parent ? {
        uuid: firstBlock.parent.id || firstBlock.parent.uuid
      } : null;

      const pageInfo = firstBlock.page ? {
        name: firstBlock.page.originalName || firstBlock.page.name
      } : null;

      console.log('父块信息:', parentInfo);
      console.log('页面信息:', pageInfo);

      // 记录块的位置信息，用于插入新块
      const blockPosition = {
        parentUUID: parentInfo?.uuid,
        pageName: pageInfo?.name,
        // 如果有前一个兄弟块，记录其UUID
        prevSiblingUUID: firstBlock.left?.id || null
      };

      // 输出块的详细信息以便调试
      console.log('块详细信息:', {
        firstBlock,
        parentInfo,
        pageInfo,
        leftBlock: firstBlock.left
      });

      console.log('块位置信息:', blockPosition);

      // 使用更简单的方法替换块内容
      try {
        // 将格式化后的块合并为一个字符串
        const combinedContent = formattedBlocks.join('\n');
        console.log('合并后的内容:', combinedContent.substring(0, 100) + '...');

        // 直接更新第一个块的内容
        console.log('更新第一个块的内容, UUID:', firstUUID);
        await logseq.Editor.updateBlock(firstUUID, combinedContent);
        console.log('更新块内容成功');

        // 如果有多个块，删除其余的块
        if (context.blockUUIDs.length > 1) {
          console.log('删除其余块...');
          for (let i = 1; i < context.blockUUIDs.length; i++) {
            const uuid = context.blockUUIDs[i];
            console.log('删除块, UUID:', uuid);
            await logseq.Editor.removeBlock(uuid);
          }
          console.log('其余块删除完成');
        }
      } catch (updateError) {
        console.error('更新块内容失败:', updateError);

        // 如果直接更新失败，尝试删除后重新插入的方法
        console.log('尝试删除后重新插入的方法...');

        // 删除原始块
        console.log('开始删除原始块...');
        for (const uuid of context.blockUUIDs) {
          console.log('删除块, UUID:', uuid);
          await logseq.Editor.removeBlock(uuid);
        }
        console.log('原始块删除完成');

        // 确定插入位置并插入新块
        console.log('开始插入新块...');
        if (blockPosition.parentUUID) {
          // 如果有父块，在父块下插入
          console.log('在父块下插入, 父块UUID:', blockPosition.parentUUID);
          let lastInsertedBlock = null;

          for (let i = 0; i < formattedBlocks.length; i++) {
            const blockContent = formattedBlocks[i];
            console.log(`插入第${i+1}个块, 内容:`, blockContent.substring(0, 30) + '...');

            try {
              // 在父块下插入新块
              lastInsertedBlock = await logseq.Editor.insertBlock(
                blockPosition.parentUUID,
                blockContent,
                { sibling: true, before: false }
              );

              console.log('插入块成功:', lastInsertedBlock?.uuid);
            } catch (insertError) {
              console.error(`插入第${i+1}个块失败:`, insertError);
            }
          }
        } else if (blockPosition.pageName) {
          // 如果是顶级块，在页面中插入
          console.log('在页面中插入, 页面名称:', blockPosition.pageName);
          for (let i = 0; i < formattedBlocks.length; i++) {
            const blockContent = formattedBlocks[i];
            console.log(`插入第${i+1}个块到页面, 内容:`, blockContent.substring(0, 30) + '...');

            try {
              const insertedBlock = await logseq.Editor.insertBlock(blockPosition.pageName, blockContent);
              console.log('插入页面块成功:', insertedBlock?.uuid);
            } catch (insertError) {
              console.error(`插入第${i+1}个块到页面失败:`, insertError);
            }
          }
        } else {
          console.error('无法确定插入位置');
          return;
        }
      }

      console.log('替换原文操作完成');
      // 关闭UI
      logseq.hideMainUI();
    } catch (error) {
      console.error('替换内容失败:', error);
    }
  };

  // 处理插入子块
  const handleInsert = async (content: string) => {
    if (!context || !context.blockUUIDs.length) {
      console.error('插入子块失败: 上下文或块UUID不存在');
      return;
    }

    try {
      console.log('开始插入子块操作, 内容长度:', content.length);
      console.log('块UUID列表:', context.blockUUIDs);
      // 输出完整的上下文信息以便调试
      console.log('完整上下文对象:', JSON.stringify(context, null, 2));

      // 格式化 AI 响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(content);
      console.log('格式化后的块数量:', formattedBlocks.length);
      console.log('格式化后的块内容示例:', formattedBlocks.slice(0, 2));

      if (formattedBlocks.length === 0) {
        console.error('格式化后的块为空');
        return;
      }

      // 获取第一个块的UUID
      const parentUUID = context.blockUUIDs[0];
      console.log('父块UUID:', parentUUID);

      // 构建块结构树，处理缩进关系
      const blockTree = buildBlockTree(formattedBlocks);
      console.log('构建的块结构树:', blockTree);

      // 判断是否有明确设置了插入模式
      if (context.isInsertAsSibling !== undefined) {
        console.log(`使用显式指定的插入模式: ${context.isInsertAsSibling ? '同级插入' : '子块插入'}`);
        console.log(`isInsertAsSibling类型: ${typeof context.isInsertAsSibling}`);
      }

      // 递归插入块树
      console.log('开始递归插入块树...');
      await insertBlockTree(parentUUID, blockTree);
      console.log('插入操作完成');

      // 关闭UI
      logseq.hideMainUI();
    } catch (error) {
      console.error('插入内容失败:', error);
    }
  };

  // 根据缩进构建块结构树
  const buildBlockTree = (blocks: string[]): BlockEntity[] => {
    if (!blocks.length) return [];

    const result: BlockEntity[] = [];
    const stack: {node: BlockEntity, indent: number}[] = [];

    for (const block of blocks) {
      // 获取缩进级别
      const indentMatch = block.match(/^(\s+)/);
      const indent = indentMatch ? Math.floor(indentMatch[1].length / 2) : 0;

      // 创建新块对象（去除前导空格）
      const content = block.trimStart();
      // 如果内容以 "-"、"*"、"+" 或数字+点开始，移除这个标记
      const cleanContent = content.replace(/^(\d+\.|-|\*|\+)\s+/, '');

      const newNode: BlockEntity = {
        content: cleanContent,
      };

      if (stack.length === 0) {
        // 根级别块
        result.push(newNode);
        stack.push({ node: newNode, indent });
      } else {
        // 找到合适的父节点
        while (stack.length > 0 && stack[stack.length - 1].indent >= indent) {
          stack.pop();
        }

        if (stack.length === 0) {
          // 如果没有找到合适的父节点，添加到根级别
          result.push(newNode);
        } else {
          // 将当前块添加为上一级的子块
          const parent = stack[stack.length - 1].node;
          if (!parent.children) {
            parent.children = [];
          }
          parent.children.push(newNode);
        }

        stack.push({ node: newNode, indent });
      }
    }

    return result;
  };

  // 递归插入块树
  const insertBlockTree = async (parentUUID: string, blocks: BlockEntity[]) => {
    console.log(`开始插入块树，父块UUID: ${parentUUID}, 块数量: ${blocks.length}`);

    // 增加额外的日志 - 输出上下文信息以便调试
    console.log(`上下文类型: ${context?.type || '未知'}`);
    console.log(`上下文块数量: ${context?.blockUUIDs?.length || 0}`);

    // 获取父块信息，用于确定是否为阅读模式下的插入
    const parentBlock = await logseq.Editor.getBlock(parentUUID);

    // 检查是否为阅读模式（块有内容）
    const isReadingMode = parentBlock && parentBlock.content && parentBlock.content.trim() !== "";

    // 检查是否为子块（鼠标悬停在子块上）
    // 只有在parent是对象且有id属性时才视为子块
    const isChildBlock = parentBlock && parentBlock.parent &&
                         (typeof parentBlock.parent === 'object') &&
                         (parentBlock.parent.id || parentBlock.parent.uuid);

    // 获取当前是否有选中内容的标志 - 从context属性中获取
    // 默认为false，除非明确设置了选中内容
    const hasTextSelection = context && context.type === 'selection';

    // 检查子块是否有内容（只有明确有文本选中时才视为有选中内容）
    const hasSelectedContent = hasTextSelection;

    console.log(`父块状态: ${isReadingMode ? "非空内容" : "空内容"}, 阅读模式: ${isReadingMode}, 是否为子块: ${isChildBlock ? "是" : "否"}, 是否有选中内容: ${hasSelectedContent ? "是" : "否"}`);

    // 获取子块的子块列表（如果有）
    let childBlocks: any[] = [];
    if (parentBlock && parentBlock.children) {
      try {
        const blockWithChildren = await logseq.Editor.getBlock(parentUUID, { includeChildren: true });
        if (blockWithChildren && blockWithChildren.children) {
          childBlocks = blockWithChildren.children;
        }
        console.log(`子块数量: ${childBlocks.length}`);
      } catch (e) {
        console.error(`获取子块失败:`, e);
        childBlocks = [];
      }
    }

    for (let i = 0; i < blocks.length; i++) {
      const block = blocks[i];
      try {
        console.log(`插入第${i+1}个块, 内容: ${block.content.substring(0, 30)}...`);

        // 根据不同情况决定插入选项
        let insertOptions;

        // 强制检查isInsertAsSibling的值，确保布尔值正确传递
        const forceSibling = context?.isInsertAsSibling === true;
        console.log(`isInsertAsSibling的原始值: ${context?.isInsertAsSibling}, 类型: ${typeof context?.isInsertAsSibling}, 强制转换后: ${forceSibling}`);

        if (forceSibling) {
          // 如果明确指定为同级插入，优先级最高，强制作为兄弟块插入
          insertOptions = { sibling: true, before: false };
          console.log(`【强制同级插入模式】: 作为兄弟块插入(同级)`);
        } else if (hasSelectedContent) {
          // 如果有文本选中，作为兄弟块插入（在块后面）
          insertOptions = { sibling: true, before: false };
          console.log(`文本选中模式: 将内容插入到块后面(同级)`);
        } else if (!isChildBlock) {
          // 如果不是子块（是顶层块），作为兄弟块插入
          insertOptions = { sibling: true, before: false };
          console.log(`顶层块模式: 作为兄弟块插入(同级)`);
        } else {
          // 子块模式，作为子块插入
          insertOptions = { sibling: false };
          console.log(`子块插入模式: 将内容插入到子块内部(作为子级)`);
        }

        console.log(`使用插入选项:`, insertOptions);

        // 为子块添加位置控制参数
        if (isChildBlock && !hasSelectedContent && !insertOptions.sibling) {
          // 只有当插入模式为子块插入时才修改位置参数
          // 修复子块插入位置问题，设置为第一个子块位置（不是默认的最后一个位置）
          insertOptions = {
            ...insertOptions,
            sibling: false,
            before: true // 确保插入为第一个子块位置
          };
          console.log(`子块插入优化: 放置到子级的开头`, insertOptions);
        }

        const insertedBlock = await logseq.Editor.insertBlock(
          parentUUID,
          block.content,
          insertOptions
        );

        if (insertedBlock && insertedBlock.uuid) {
          console.log(`块插入成功, 新块UUID: ${insertedBlock.uuid}`);

          // 如果有子块且插入成功了，递归插入子块
          if (block.children && block.children.length > 0) {
            console.log(`开始插入${block.children.length}个子块...`);
            await insertBlockTree(insertedBlock.uuid, block.children);
          }
        } else {
          console.error(`块插入失败，未返回有效的块对象`);
        }
      } catch (error) {
        console.error(`插入块失败: ${block.content.substring(0, 30)}...`, error);
        // 继续处理其他块，不中断整个过程
      }
    }

    console.log(`块树插入完成，父块UUID: ${parentUUID}`);
  };

  // 处理模态框关闭
  const handleClose = () => {
    logseq.hideMainUI();
  };

  // 处理刷新上下文
  const handleRefreshContext = async () => {
    try {
      console.log('开始刷新上下文...');

      // 重新获取当前上下文
      const newContext = await getContext();
      console.log('获取到新的上下文:', newContext);

      // 更新上下文状态
      setContext(newContext);

      console.log('上下文刷新完成');
    } catch (error) {
      console.error('刷新上下文失败:', error);
      throw error; // 重新抛出错误，让ChatModal处理
    }
  };

  if (!visible) {
    return null;
  }

  // 创建一个检查点击是否在元素附近的函数
  const isClickNearElement = (element: HTMLElement | null, event: React.MouseEvent, bufferPx: number = 20) => {
    if (!element) return false;

    const rect = element.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    // 扩展区域，添加缓冲区
    return (
      x >= rect.left - bufferPx &&
      x <= rect.right + bufferPx &&
      y >= rect.top - bufferPx &&
      y <= rect.bottom + bufferPx
    );
  };

  return (
    <main
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{
        backdropFilter: 'none',
        WebkitBackdropFilter: 'none',
        backgroundColor: 'transparent'
      }}
      onClick={(e) => {
        // 检查点击是否在元素附近，如果是则不关闭界面
        if (!innerRef.current?.contains(e.target as any) && !isClickNearElement(innerRef.current, e)) {
          window.logseq.hideMainUI();
        }
      }}
    >
      {currentPage === AppPageType.SETTINGS && (
        <div
          ref={innerRef}
          className="apple-modal bg-white dark:bg-gray-900 rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto pointer-events-auto mx-auto"
          style={{
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <SettingsUI onClose={handleClose} />
        </div>
      )}

      {currentPage === AppPageType.CHAT && context && (
        <div
          ref={innerRef}
          className="pointer-events-auto"
          style={blockPosition ? {
            position: 'absolute',
            top: `${blockPosition.top + 10}px`,
            left: `${blockPosition.left}px`,
            zIndex: 9999,
            maxWidth: '700px',
            width: `${Math.max(580, Math.min(blockPosition.width * 1.5, 700))}px`
          } : {
            maxWidth: '700px',
            width: '100%'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <ChatModal
            context={context}
            onClose={handleClose}
            onReplace={handleReplace}
            onInsert={handleInsert}
            onRefreshContext={handleRefreshContext}
            forceReset={forceReset}
          />
        </div>
      )}
    </main>
  );
}

export default App;
