/**
 * 快捷键配置接口
 */
export interface KeybindingConfig {
  binding: string; // 默认快捷键（Windows/Linux）
  mac: string;     // Mac快捷键
}

/**
 * 上下文设置接口
 */
export interface ContextSettings {
  contextRange: number;         // 上下文范围（块数）
  includePageTitle: boolean;    // 是否包含页面标题
  enableThemeDetection: boolean; // 是否启用主题检测
}

/**
 * UI设置接口
 */
export interface UISettings {
  rememberWindowSize: boolean;   // 是否记住窗口大小
  rememberWindowPosition: boolean; // 是否记住窗口位置
  defaultWidth: number;         // 默认窗口宽度
  defaultHeight: number;        // 默认窗口高度
  enableDetachedWindow: boolean; // 是否启用分离式窗口
  theme: string;                // 主题（light, dark, system）
  customThemeColor: string;      // 自定义主题颜色
  showToolbar: boolean;         // 是否显示工具栏
  autoResponseOnOpen: boolean;   // 是否在激活聊天窗口时自动响应
  autoRefreshContext: boolean;   // 是否启用自动刷新上下文
}

/**
 * API配置接口
 */
export interface ApiConfig {
  id: string;          // 唯一标识符
  name: string;         // 配置名称
  apiUrl: string;       // API URL
  apiKey: string;       // API Key
  modelName: string;    // 模型名称
  followUpModelName?: string; // 追问对话框默认模型名称
  provider: string;     // API提供商（openai, gemini, azure, anthropic等）
  isDefault?: boolean;  // 是否为默认配置
}

/**
 * 插件设置接口定义
 */
export interface PluginSettings {
  // 兼容旧版本的设置
  apiUrl: string;       // API URL
  apiKey: string;       // API Key
  modelName: string;    // 模型名称

  // 新版API配置
  apiConfigs: ApiConfig[];      // API配置列表
  currentApiConfigId: string;   // 当前选中的API配置ID
  followupModelId: string;      // 追问对话框中使用的模型 ID

  // API请求设置
  requestTimeout: number;      // API请求超时时间（秒）
  maxRetries: number;          // 最大重试次数
  retryDelay: number;          // 重试延迟（秒）

  // 其他设置
  temperature: number;
  maxTokens: number;
  enableHistory: boolean;
  customPrompts: Array<{name: string, prompt: string}>;
  lastSelectedPrompt: string; // 上次选择的提示词
  keybindings: {
    openChat: KeybindingConfig;
    quickReply: KeybindingConfig;
  };
  contextSettings: ContextSettings; // 上下文相关设置
  uiSettings: UISettings;        // UI相关设置

}

/**
 * 默认设置值
 */
export const DEFAULT_SETTINGS: PluginSettings = {
  // 兼容旧版本的设置
  apiUrl: "https://api.openai.com/v1/chat/completions",
  apiKey: "",
  modelName: "gpt-3.5-turbo",

  // 新版API配置
  apiConfigs: [
    {
      id: "default",
      name: "OpenAI",
      apiUrl: "https://api.openai.com/v1/chat/completions",
      apiKey: "",
      modelName: "gpt-3.5-turbo",
      followUpModelName: "gpt-3.5-turbo", // 默认使用与主模型相同的模型
      provider: "openai",
      isDefault: true
    }
  ],
  currentApiConfigId: "default",
  followupModelId: "default", // 默认使用与主模型相同的模型 ID

  // API请求设置
  requestTimeout: 60,      // 默认超时时间为60秒
  maxRetries: 3,           // 默认最大重试次数为3次
  retryDelay: 1,           // 默认重试延迟为1秒

  // 其他设置
  temperature: 0.7,
  maxTokens: 2000,
  enableHistory: false,
  customPrompts: [
    {
      name: "总结",
      prompt: "请总结以下内容的要点："
    },
    {
      name: "扩展",
      prompt: "请基于以下内容进行扩展和补充："
    }
  ],
  lastSelectedPrompt: "default",
  keybindings: {
    openChat: {
      binding: "ctrl+g",
      mac: "cmd+g"
    },
    quickReply: {
      binding: "ctrl+shift+g",
      mac: "cmd+shift+g"
    }
  },
  contextSettings: {
    contextRange: 0,  // 默认只获取当前块，不包含前后文
    includePageTitle: true,
    enableThemeDetection: true
  },
  uiSettings: {
    rememberWindowSize: true,
    rememberWindowPosition: true,
    defaultWidth: 600,
    defaultHeight: 500,
    enableDetachedWindow: true,
    theme: 'system',
    customThemeColor: '#0077ff',
    showToolbar: true,
    autoResponseOnOpen: false,
    autoRefreshContext: false  // 默认关闭自动刷新
  },

};

/**
 * 初始化插件设置
 * @returns 合并后的设置对象
 */
export async function initializeSettings(): Promise<PluginSettings> {
  // 获取当前设置，使用兼容的API
  const currentSettings = logseq.settings || {};
  const mergedSettings = Object.assign({}, DEFAULT_SETTINGS, currentSettings);

  // 确保customPrompts字段存在且为数组
  if (!Array.isArray(mergedSettings.customPrompts)) {
    mergedSettings.customPrompts = [...DEFAULT_SETTINGS.customPrompts];
  }

  // 确保新添加的字段有默认值
  if (mergedSettings.temperature === undefined) {
    mergedSettings.temperature = DEFAULT_SETTINGS.temperature;
  }

  if (mergedSettings.maxTokens === undefined) {
    mergedSettings.maxTokens = DEFAULT_SETTINGS.maxTokens;
  }

  if (mergedSettings.lastSelectedPrompt === undefined) {
    mergedSettings.lastSelectedPrompt = DEFAULT_SETTINGS.lastSelectedPrompt;
  }

  // 确保API请求设置字段有默认值
  if (mergedSettings.requestTimeout === undefined) {
    mergedSettings.requestTimeout = DEFAULT_SETTINGS.requestTimeout;
  }

  if (mergedSettings.maxRetries === undefined) {
    mergedSettings.maxRetries = DEFAULT_SETTINGS.maxRetries;
  }

  if (mergedSettings.retryDelay === undefined) {
    mergedSettings.retryDelay = DEFAULT_SETTINGS.retryDelay;
  }

  // 初始化API配置
  if (!Array.isArray(mergedSettings.apiConfigs) || mergedSettings.apiConfigs.length === 0) {
    console.log('初始化API配置列表');
    mergedSettings.apiConfigs = [];

    // 如果有旧版本的API设置，创建一个新的配置
    if (mergedSettings.apiUrl && mergedSettings.apiKey) {
      const defaultConfig = {
        id: 'default',
        name: 'OpenAI',
        apiUrl: mergedSettings.apiUrl,
        apiKey: mergedSettings.apiKey,
        modelName: mergedSettings.modelName || 'gpt-3.5-turbo',
        followUpModelName: mergedSettings.modelName || 'gpt-3.5-turbo', // 初始化为与主模型相同
        provider: 'openai', // 默认使用OpenAI作为提供商
        isDefault: true
      };
      mergedSettings.apiConfigs.push(defaultConfig);
      mergedSettings.currentApiConfigId = 'default';
    } else {
      // 否则使用默认配置
      mergedSettings.apiConfigs = [...DEFAULT_SETTINGS.apiConfigs];
      mergedSettings.currentApiConfigId = DEFAULT_SETTINGS.currentApiConfigId;
    }
  } else {
    // 确保每个配置都有ID
    mergedSettings.apiConfigs = mergedSettings.apiConfigs.map(config => {
      // 确保有ID
      if (!config.id) {
        config.id = generateUniqueId();
      }

      // 确保有followUpModelName
      if (!config.followUpModelName) {
        config.followUpModelName = config.modelName;
      }

      return config;
    });
  }

  // 确保有当前选中的配置
  if (!mergedSettings.currentApiConfigId || !mergedSettings.apiConfigs.some(c => c.id === mergedSettings.currentApiConfigId)) {
    // 如果没有选中的配置或选中的配置不存在，选择默认配置或第一个
    const defaultConfig = mergedSettings.apiConfigs.find(c => c.isDefault);
    if (defaultConfig) {
      mergedSettings.currentApiConfigId = defaultConfig.id;
    } else if (mergedSettings.apiConfigs.length > 0) {
      mergedSettings.currentApiConfigId = mergedSettings.apiConfigs[0].id;
    }
  }

  // 确保兼容旧版本的API设置
  const currentConfig = mergedSettings.apiConfigs.find(c => c.id === mergedSettings.currentApiConfigId);
  if (currentConfig) {
    mergedSettings.apiUrl = currentConfig.apiUrl;
    mergedSettings.apiKey = currentConfig.apiKey;
    mergedSettings.modelName = currentConfig.modelName;
  }

  // 确保快捷键设置存在
  if (!mergedSettings.keybindings) {
    mergedSettings.keybindings = DEFAULT_SETTINGS.keybindings;
  } else {
    // 确保各个快捷键配置存在
    if (!mergedSettings.keybindings.openChat) {
      mergedSettings.keybindings.openChat = DEFAULT_SETTINGS.keybindings.openChat;
    }
    if (!mergedSettings.keybindings.quickReply) {
      mergedSettings.keybindings.quickReply = DEFAULT_SETTINGS.keybindings.quickReply;
    }
  }

  // 确保上下文设置存在
  if (!mergedSettings.contextSettings) {
    mergedSettings.contextSettings = DEFAULT_SETTINGS.contextSettings;
  } else {
    // 确保各个上下文设置字段存在
    if (mergedSettings.contextSettings.contextRange === undefined) {
      mergedSettings.contextSettings.contextRange = DEFAULT_SETTINGS.contextSettings.contextRange;
    }
    if (mergedSettings.contextSettings.includePageTitle === undefined) {
      mergedSettings.contextSettings.includePageTitle = DEFAULT_SETTINGS.contextSettings.includePageTitle;
    }
    if (mergedSettings.contextSettings.enableThemeDetection === undefined) {
      mergedSettings.contextSettings.enableThemeDetection = DEFAULT_SETTINGS.contextSettings.enableThemeDetection;
    }
  }

  // 确保UI设置存在
  if (!mergedSettings.uiSettings) {
    mergedSettings.uiSettings = DEFAULT_SETTINGS.uiSettings;
  } else {
    // 确保各个UI设置字段存在
    if (mergedSettings.uiSettings.rememberWindowSize === undefined) {
      mergedSettings.uiSettings.rememberWindowSize = DEFAULT_SETTINGS.uiSettings.rememberWindowSize;
    }
    if (mergedSettings.uiSettings.rememberWindowPosition === undefined) {
      mergedSettings.uiSettings.rememberWindowPosition = DEFAULT_SETTINGS.uiSettings.rememberWindowPosition;
    }
    if (mergedSettings.uiSettings.defaultWidth === undefined) {
      mergedSettings.uiSettings.defaultWidth = DEFAULT_SETTINGS.uiSettings.defaultWidth;
    }
    if (mergedSettings.uiSettings.defaultHeight === undefined) {
      mergedSettings.uiSettings.defaultHeight = DEFAULT_SETTINGS.uiSettings.defaultHeight;
    }
    if (mergedSettings.uiSettings.enableDetachedWindow === undefined) {
      mergedSettings.uiSettings.enableDetachedWindow = DEFAULT_SETTINGS.uiSettings.enableDetachedWindow;
    }
    if (mergedSettings.uiSettings.theme === undefined) {
      mergedSettings.uiSettings.theme = DEFAULT_SETTINGS.uiSettings.theme;
    }
    if (mergedSettings.uiSettings.customThemeColor === undefined) {
      mergedSettings.uiSettings.customThemeColor = DEFAULT_SETTINGS.uiSettings.customThemeColor;
    }
    if (mergedSettings.uiSettings.showToolbar === undefined) {
      mergedSettings.uiSettings.showToolbar = DEFAULT_SETTINGS.uiSettings.showToolbar;
    }
    if (mergedSettings.uiSettings.autoResponseOnOpen === undefined) {
      mergedSettings.uiSettings.autoResponseOnOpen = DEFAULT_SETTINGS.uiSettings.autoResponseOnOpen;
    }
    if (mergedSettings.uiSettings.autoRefreshContext === undefined) {
      mergedSettings.uiSettings.autoRefreshContext = DEFAULT_SETTINGS.uiSettings.autoRefreshContext;
    }
  }

  // 保存合并后的设置
  await logseq.updateSettings(mergedSettings);

  return mergedSettings;
}

/**
 * 生成唯一ID
 * @returns 唯一ID字符串
 */
export function generateUniqueId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

/**
 * 更新插件设置
 * @param settings 要更新的设置对象或部分设置
 */
export async function updateSettings(settings: Partial<PluginSettings>): Promise<void> {
  // 获取当前设置
  const currentSettings = logseq.settings || {};
  // 合并设置
  const mergedSettings = { ...currentSettings, ...settings };
  // 更新设置
  await logseq.updateSettings(mergedSettings);
  // 打印日志，方便调试
  console.log("Settings updated:", mergedSettings);
}

/**
 * 获取当前插件设置
 * @returns 当前设置对象
 */
export async function getSettings(): Promise<PluginSettings> {
  // 获取当前设置，使用兼容的API
  const settings = logseq.settings || {};
  const mergedSettings = Object.assign({}, DEFAULT_SETTINGS, settings) as PluginSettings;

  return mergedSettings;
}