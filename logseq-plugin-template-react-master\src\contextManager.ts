import { getSettings } from './settings';

/**
 * 上下文源类型
 */
export enum ContextSourceType {
  SELECTION = 'selection',      // 选中文本
  CURRENT_BLOCK = 'block',      // 当前块
  SELECTED_BLOCKS = 'blocks',   // 选中的多个块
  NONE = 'none'                 // 无上下文
}

/**
 * 上下文数据接口
 */
export interface ContextData {
  type: ContextSourceType;      // 上下文类型
  content: string;              // 上下文内容文本
  blockUUIDs: string[];         // 相关的块UUID列表
  pageTitle?: string;           // 页面标题（可选）
  theme?: string;               // 页面主题（可选）
  isInsertAsSibling?: boolean;  // 是否作为同级插入（可选）
}

/**
 * 编辑器光标位置接口
 */
interface CursorPosition {
  start: number;
  end: number;
}

/**
 * 获取当前编辑器中的选中文本
 * @returns 选中的文本，如果没有则返回空字符串
 */
async function getSelectedText(): Promise<string | null> {
  try {
    const position = await logseq.Editor.getEditingCursorPosition();
    if (position && position.pos) {
      const currentBlock = await logseq.Editor.getCurrentBlock();
      if (currentBlock) {
        // 提取选中的文本内容
        const content = currentBlock.content;
        // 处理位置对象
        const pos = position.pos as unknown as CursorPosition;
        // 如果有选中范围，返回选中的文本
        if (pos.start !== pos.end) {
          return content.substring(pos.start, pos.end);
        }
      }
    }
    return null;
  } catch (error) {
    console.error("获取选中文本失败:", error);
    return null;
  }
}

/**
 * 获取选中的多个块
 * @returns 选中的块数组，如果没有则返回空数组
 */
async function getSelectedBlocks() {
  try {
    const blocks = await logseq.Editor.getSelectedBlocks();
    return blocks || [];
  } catch (error) {
    console.error("获取选中块失败:", error);
    return [];
  }
}

/**
 * 获取当前块
 * @returns 当前块对象，如果没有则返回null
 */
async function getCurrentBlock() {
  try {
    return await logseq.Editor.getCurrentBlock();
  } catch (error) {
    console.error("获取当前块失败:", error);
    return null;
  }
}

/**
 * 获取当前页面标题
 * @returns 页面标题，如果无法获取则返回空字符串
 */
async function getPageTitle(): Promise<string> {
  try {
    const currentPage = await logseq.Editor.getCurrentPage();
    return currentPage?.originalName?.toString() || "";
  } catch (error) {
    console.error("获取页面标题失败:", error);
    return "";
  }
}

/**
 * 检测页面主题
 * @param pageTitle 页面标题
 * @param content 页面内容
 * @returns 检测到的主题
 */
function detectTheme(pageTitle: string, content: string): string {
  // 简单的主题检测逻辑，可以根据需要扩展
  const keywords = [
    { theme: "学习", keywords: ["学习", "笔记", "课程", "教育", "知识", "考试"] },
    { theme: "工作", keywords: ["工作", "项目", "会议", "报告", "审核", "计划"] },
    { theme: "生活", keywords: ["生活", "日记", "旅行", "饮食", "健康", "休闲"] },
    { theme: "阅读", keywords: ["阅读", "书籍", "文章", "文学", "诗歌", "小说"] },
    { theme: "编程", keywords: ["编程", "code", "代码", "function", "class", "API", "bug", "开发"] }
  ];

  // 合并标题和内容进行检测
  const fullText = `${pageTitle} ${content}`.toLowerCase();

  // 计算每个主题的匹配分数
  const scores = keywords.map(theme => {
    let score = 0;
    theme.keywords.forEach(keyword => {
      // 在标题中出现的关键词权重更高
      const titleMatches = (pageTitle.toLowerCase().match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
      // 在内容中出现的关键词
      const contentMatches = (fullText.match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;

      score += titleMatches * 3 + contentMatches;
    });
    return { theme: theme.theme, score };
  });

  // 按分数排序并返回最高分的主题
  scores.sort((a, b) => b.score - a.score);

  // 如果最高分大于0，返回该主题，否则返回空字符串
  return scores[0].score > 0 ? scores[0].theme : "";
}







/**
 * 获取块的前后文内容（简化版本）
 * @param blockUUID 块的UUID
 * @param range 前后范围（块数）
 * @returns 包含前后文的块数组
 */
async function getBlockContext(blockUUID: string, range: number) {
  try {
    console.log(`获取块上下文，UUID: ${blockUUID}, 范围: ${range}`);

    // 如果范围为0，只返回当前块
    if (range === 0) {
      const currentBlock = await logseq.Editor.getBlock(blockUUID);
      if (!currentBlock) {
        console.log("未找到当前块，返回空数组");
        return [];
      }
      console.log("范围为0，直接返回当前块");
      return [currentBlock];
    }

    // 简单的前后文获取逻辑
    const currentBlock = await logseq.Editor.getBlock(blockUUID);
    if (!currentBlock) {
      console.log("未找到当前块，返回空数组");
      return [];
    }

    // 获取页面所有块
    const page = await logseq.Editor.getPage(currentBlock.page.id);
    if (!page) {
      console.log("未找到页面，只返回当前块");
      return [currentBlock];
    }

    const pageBlocks = await logseq.Editor.getPageBlocksTree(page.name);
    const flatBlocks = flattenBlocks(pageBlocks || []);

    // 找到当前块的索引
    const currentIndex = flatBlocks.findIndex(block => block.uuid === blockUUID);
    if (currentIndex === -1) {
      console.log("在页面中未找到当前块，只返回当前块");
      return [currentBlock];
    }

    // 获取前后文块
    const startIndex = Math.max(0, currentIndex - range);
    const endIndex = Math.min(flatBlocks.length - 1, currentIndex + range);
    const contextBlocks = flatBlocks.slice(startIndex, endIndex + 1);

    console.log(`获取上下文完成，返回 ${contextBlocks.length} 个块`);
    return contextBlocks;

  } catch (error) {
    console.error("获取块上下文失败:", error);
    // 降级到简单模式
    try {
      const currentBlock = await logseq.Editor.getBlock(blockUUID);
      return currentBlock ? [currentBlock] : [];
    } catch (fallbackError) {
      console.error("降级获取也失败:", fallbackError);
      return [];
    }
  }
}

/**
 * 将块树展平为一维数组
 */
function flattenBlocks(blocks: any[]): any[] {
  const result: any[] = [];

  function flatten(blockList: any[]) {
    for (const block of blockList) {
      result.push(block);
      if (block.children && block.children.length > 0) {
        flatten(block.children);
      }
    }
  }

  flatten(blocks);
  return result;
}

/**
 * 获取简单上下文
 * @param blockUUID 块的UUID
 * @param contextRange 上下文范围
 * @param includePageTitle 是否包含页面标题
 * @param enableThemeDetection 是否启用主题检测
 * @returns 上下文数据
 */
async function getSimpleContext(
  blockUUID: string,
  contextRange: number,
  includePageTitle: boolean,
  enableThemeDetection: boolean
): Promise<ContextData> {
  console.log(`获取简单上下文，UUID: ${blockUUID}, 范围: ${contextRange}, 包含标题: ${includePageTitle}, 主题检测: ${enableThemeDetection}`);

  // 获取当前块
  const currentBlock = await logseq.Editor.getBlock(blockUUID);
  if (!currentBlock) {
    console.log("未找到当前块，返回空上下文");
    return {
      type: ContextSourceType.NONE,
      content: "",
      blockUUIDs: []
    };
  }

  console.log(`当前块内容: ${currentBlock.content.substring(0, 50)}...`);

  // 获取上下文块
  const contextBlocks = await getBlockContext(blockUUID, contextRange);
  const combinedContent = contextBlocks.map(block => block.content).join("\n\n");

  console.log(`组合后的内容长度: ${combinedContent.length}字符`);

  // 获取页面标题
  const pageTitle = includePageTitle ? await getPageTitle() : "";
  if (pageTitle) {
    console.log(`页面标题: ${pageTitle}`);
  }

  // 初始化上下文数据
  const contextData: ContextData = {
    type: contextRange === 0 ? ContextSourceType.CURRENT_BLOCK : ContextSourceType.SELECTED_BLOCKS,
    content: combinedContent,
    blockUUIDs: contextBlocks.map(block => block.uuid)
  };

  console.log(`初始化上下文数据，类型: ${contextData.type}, 块数量: ${contextData.blockUUIDs.length}`);

  // 添加页面标题
  if (pageTitle) {
    contextData.pageTitle = pageTitle;
    // 在内容前面添加页面标题
    contextData.content = `页面: ${pageTitle}\n\n${contextData.content}`;
  }

  // 检测主题
  if (enableThemeDetection) {
    const theme = detectTheme(pageTitle, combinedContent);
    if (theme) {
      contextData.theme = theme;
      // 在内容前面添加主题信息
      contextData.content = `主题: ${theme}\n\n${contextData.content}`;
    }
  }

  return contextData;
}

/**
 * 获取当前上下文
 * @param blockUUID 可选的块UUID，如果提供则使用该块作为上下文来源
 * @returns 上下文数据对象
 */
export async function getContext(blockUUID?: string): Promise<ContextData> {
  // 获取设置
  const settings = await getSettings();
  const {
    contextRange,
    includePageTitle,
    enableThemeDetection
  } = settings.contextSettings;

  // 添加详细的调试信息
  console.log("=== getContext 调试信息 ===");
  console.log("blockUUID:", blockUUID);
  console.log("contextRange:", contextRange);
  console.log("includePageTitle:", includePageTitle);
  console.log("enableThemeDetection:", enableThemeDetection);
  console.log("完整设置对象:", JSON.stringify(settings.contextSettings, null, 2));

  // 如果提供了块UUID，直接使用该块
  if (blockUUID) {
    const block = await logseq.Editor.getBlock(blockUUID);
    if (block) {
      console.log("找到指定块，内容:", block.content.substring(0, 50) + "...");
      console.log("使用简单上下文模式");
      return await getSimpleContext(
        blockUUID,
        contextRange,
        includePageTitle,
        enableThemeDetection
      );
    }
  }

  // 检查是否有选中的文本
  const selectedText = await getSelectedText();
  if (selectedText) {
    const currentBlock = await getCurrentBlock();
    // 只返回选中的文本
    return {
      type: ContextSourceType.SELECTION,
      content: selectedText,
      blockUUIDs: currentBlock ? [currentBlock.uuid] : []
    };
  }

  // 检查是否有选中的多个块
  const selectedBlocks = await getSelectedBlocks();
  if (selectedBlocks && selectedBlocks.length > 0) {
    if (selectedBlocks.length === 1) {
      // 如果只选中了一个块，获取简单上下文
      return await getSimpleContext(
        selectedBlocks[0].uuid,
        contextRange,
        includePageTitle,
        enableThemeDetection
      );
    } else {
      // 组合选中块的内容
      const combinedContent = selectedBlocks.map(block => block.content).join("\n\n");
      return {
        type: ContextSourceType.SELECTED_BLOCKS,
        content: combinedContent,
        blockUUIDs: selectedBlocks.map(block => block.uuid)
      };
    }
  }

  // 如果没有选中的内容，使用当前块
  const currentBlock = await getCurrentBlock();
  if (currentBlock) {
    // 获取简单上下文
    return await getSimpleContext(
      currentBlock.uuid,
      contextRange,
      includePageTitle,
      enableThemeDetection
    );
  }

  // 如果以上都没有，返回空上下文
  return {
    type: ContextSourceType.NONE,
    content: "",
    blockUUIDs: []
  };
}