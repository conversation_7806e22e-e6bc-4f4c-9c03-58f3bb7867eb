# 同级块读取问题修复说明

## 🐛 问题描述

用户反馈：在读取同级块时，每次都要关闭软件重新打开，才能获取同级块。

## 🔍 问题根因分析

### 1. 缓存问题
- `logseq.Editor.getPageBlocksTree()` API 存在缓存，不会实时反映页面最新状态
- 新添加的同级块无法被及时检测到
- 块的索引位置可能不准确

### 2. 块检测器缓存
- `BlockDetector` 类中的 `lastKnownBlock` 缓存时间过长（60秒）
- 缓存的块信息可能已过期，导致检测不准确

### 3. 上下文获取策略问题
- 原有的获取策略过于依赖页面级别的块树
- 没有充分利用父子关系来获取同级块

## 🛠️ 修复方案

### 1. 改进的上下文获取策略

**新增 `getContextBlocksImproved` 函数：**
- **方法1**: 通过父块获取同级块（更准确）
- **方法2**: 强制刷新页面块数据
- **方法3**: 多种API尝试获取最新数据
- **降级方案**: 确保至少返回当前块

### 2. 优化块检测器

**改进的检测策略：**
- 调整策略优先级（编辑块 > 选中块 > 鼠标位置 > 焦点块）
- 缩短缓存时间（从60秒改为10秒）
- 添加缓存清除机制

### 3. 强制刷新机制

**新增功能：**
- 上下文缓存管理（5秒缓存周期）
- `forceRefresh` 参数支持
- 手动刷新命令：`/刷新AI上下文`

## 🎯 修复的关键改进

### 1. 智能同级块检测
```typescript
// 优先通过父块获取同级块
if (currentBlock.parent && currentBlock.parent.id) {
  const parentBlock = await logseq.Editor.getBlock(currentBlock.parent.id, { includeChildren: true });
  if (parentBlock && parentBlock.children) {
    const siblings = parentBlock.children;
    // 在同级块中找到当前块的位置
    const currentIndex = siblings.findIndex(block => block.uuid === currentBlock.uuid);
    // 返回指定范围的同级块
  }
}
```

### 2. 强制数据刷新
```typescript
// 多种方式尝试获取最新页面块
let pageBlocks = await logseq.Editor.getPageBlocksTree(page.name);
if (!pageBlocks && page.uuid) {
  pageBlocks = await logseq.Editor.getPageBlocksTree(page.uuid);
}
```

### 3. 缓存优化
```typescript
// 缩短缓存时间，避免过期数据
if (this.lastKnownBlock && Date.now() - this.lastKnownBlock.timestamp < 10000) {
  // 使用缓存（从60秒改为10秒）
}
```

## 🧪 测试方法

### 1. 基本测试
1. 在 Logseq 中创建几个同级块
2. 在其中一个块中使用 `Ctrl+G` 打开AI聊天
3. 设置上下文范围为1-2
4. 检查是否能正确获取前后同级块

### 2. 动态测试
1. 在现有块之间插入新的同级块
2. 立即使用AI聊天功能
3. 检查新块是否被正确检测

### 3. 刷新测试
1. 使用 `/刷新AI上下文` 命令
2. 检查上下文是否更新
3. 验证同级块获取是否正常

## 🔧 新增的调试功能

### 1. 手动刷新命令
- 斜杠命令：`/刷新AI上下文`
- 强制清除缓存并重新获取上下文
- 显示获取到的块数量

### 2. 详细日志
- 增加了详细的调试日志
- 显示每个检测策略的结果
- 记录同级块获取过程

### 3. 多重降级机制
- 确保在任何情况下都能获取到至少当前块
- 多种API调用方式作为备选方案

## 📋 使用建议

### 1. 遇到同级块问题时
1. 首先尝试使用 `/刷新AI上下文` 命令
2. 如果问题持续，检查控制台日志
3. 确认上下文范围设置是否合理

### 2. 最佳实践
- 建议上下文范围设置为1-2（平衡性能和功能）
- 在大量编辑后可以手动刷新上下文
- 注意观察控制台的调试信息

### 3. 性能考虑
- 新的缓存机制减少了不必要的API调用
- 智能检测策略提高了准确性
- 降级机制确保了稳定性

## ✅ 预期效果

修复后，用户应该能够：
1. **实时检测同级块** - 无需重启软件
2. **准确获取上下文** - 包括新添加的块
3. **稳定的性能** - 优化的缓存和检测机制
4. **更好的调试体验** - 详细的日志和手动刷新功能

这个修复方案解决了同级块读取的核心问题，提供了更可靠和用户友好的体验。
