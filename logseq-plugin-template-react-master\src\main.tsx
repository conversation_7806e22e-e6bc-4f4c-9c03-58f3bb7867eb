import "@logseq/libs";

import React from "react";
import * as ReactDOM from "react-dom/client";
import App from "./App";
import "./index.css";
import { initializeSettings, getSettings } from "./settings";
import { getContext, ContextData, ContextSourceType } from "./contextManager";
import { sendChatRequest, ApiResponseHandlers } from "./api";
import { getBlockUnderMouse } from "./utils";

import { logseq as PL } from "../package.json";

// @ts-expect-error
const css = (t, ...args) => String.raw(t, ...args);

const pluginId = PL.id;
const root = ReactDOM.createRoot(document.getElementById("app")!);

// 当前上下文状态
let currentContext: ContextData | undefined;
// 标记是否是新会话
let isNewSession: boolean = true;

async function main() {
  console.info(`#${pluginId}: MAIN`);

  // 初始化插件设置
  await initializeSettings();

  // 检测并适应Logseq主题
  await setupThemeDetection();

  // 确保设置页面正确注册
  logseq.useSettingsSchema([
    // API设置部分
    {
      key: "apiSettings",
      title: "API设置",
      type: "heading",
      default: "",
      description: "API相关配置"
    },
    {
      key: "apiUrl",
      type: "string",
      default: "https://api.openai.com/v1/chat/completions",
      title: "API URL",
      description: "AI服务的API地址（兼容旧版本）"
    },
    {
      key: "apiKey",
      type: "string",
      default: "",
      title: "API Key",
      description: "访问AI服务所需的API密钥（兼容旧版本）"
    },
    {
      key: "modelName",
      type: "string",
      default: "gpt-3.5-turbo",
      title: "模型名称",
      description: "使用的AI模型名称（兼容旧版本）"
    },
    {
      key: "temperature",
      type: "number",
      default: 0.7,
      title: "温度",
      description: "控制生成文本的随机性，范围从0（确定性高）到2（多样性高）"
    },
    {
      key: "maxTokens",
      type: "number",
      default: 2000,
      title: "最大输出标记数",
      description: "模型将生成的最大标记数量"
    },
    {
      key: "requestTimeout",
      type: "number",
      default: 60,
      title: "API请求超时时间",
      description: "API请求的超时时间（秒），增加该值可减少超时错误"
    },
    {
      key: "maxRetries",
      type: "number",
      default: 3,
      title: "最大重试次数",
      description: "API请求失败时的最大重试次数"
    },
    {
      key: "retryDelay",
      type: "number",
      default: 1,
      title: "重试延迟",
      description: "重试之间的延迟时间（秒）"
    },
    {
      key: "enableHistory",
      type: "boolean",
      default: false,
      title: "启用历史记录",
      description: "是否保存聊天历史记录"
    },
    {
      key: "uiSettings.autoResponseOnOpen",
      type: "boolean",
      default: false,
      title: "激活窗口时自动响应",
      description: "当使用快捷键激活聊天窗口时，是否自动发送请求"
    },

    // 上下文设置部分
    {
      key: "contextSettingsHeading",
      title: "上下文设置",
      type: "heading",
      default: "",
      description: "上下文获取相关配置"
    },

    {
      key: "contextSettings.contextRange",
      type: "number",
      default: 0,
      title: "上下文范围",
      description: "获取当前块前后的块数量（设置为0时只获取当前块内容，不包含前后文）"
    },

    {
      key: "contextSettings.includePageTitle",
      type: "boolean",
      default: true,
      title: "包含页面标题",
      description: "在上下文中包含当前页面的标题"
    },
    {
      key: "contextSettings.enableThemeDetection",
      type: "boolean",
      default: true,
      title: "启用主题检测",
      description: "自动识别当前页面的主题"
    },

    // UI设置部分
    {
      key: "uiSettingsHeading",
      title: "UI设置",
      type: "heading",
      default: "",
      description: "用户界面相关配置"
    },
    {
      key: "uiSettings.rememberWindowSize",
      type: "boolean",
      default: true,
      title: "记住窗口大小",
      description: "在会话之间记住对话窗口的大小"
    },
    {
      key: "uiSettings.rememberWindowPosition",
      type: "boolean",
      default: true,
      title: "记住窗口位置",
      description: "在会话之间记住对话窗口的位置"
    },
    {
      key: "uiSettings.defaultWidth",
      type: "number",
      default: 600,
      title: "默认窗口宽度",
      description: "对话窗口的默认宽度（像素）"
    },
    {
      key: "uiSettings.defaultHeight",
      type: "number",
      default: 500,
      title: "默认窗口高度",
      description: "对话窗口的默认高度（像素）"
    },
    {
      key: "uiSettings.enableDetachedWindow",
      type: "boolean",
      default: true,
      title: "启用分离式窗口",
      description: "允许将对话窗口分离为独立窗口"
    },
    {
      key: "uiSettings.theme",
      type: "enum",
      default: "system",
      title: "主题",
      description: "选择聊天界面的主题",
      enumChoices: ["system", "light", "dark", "custom"],
      enumPicker: "select"
    },
    {
      key: "uiSettings.customThemeColor",
      type: "string",
      default: "#0077ff",
      title: "自定义主题颜色",
      description: "当选择自定义主题时的主色调（十六进制颜色代码）"
    },
    {
      key: "uiSettings.showToolbar",
      type: "boolean",
      default: true,
      title: "显示工具栏",
      description: "在对话窗口中显示快捷工具栏"
    },


    // 知识库设置部分
    {
      key: "knowledgeBaseSettingsHeading",
      title: "知识库设置",
      type: "heading",
      default: "",
      description: "知识库集成相关配置"
    },
    {
      key: "knowledgeBaseSettings.enableKnowledgeBase",
      type: "boolean",
      default: true,
      title: "启用知识库集成",
      description: "允许AI访问您的Logseq知识库，使回答更加个性化和相关"
    },
    {
      key: "knowledgeBaseSettings.searchLimit",
      type: "number",
      default: 5,
      title: "搜索结果数量限制",
      description: "每次查询返回的知识库搜索结果数量"
    },
    {
      key: "knowledgeBaseSettings.includePages",
      type: "boolean",
      default: true,
      title: "包含页面",
      description: "在搜索结果中包含页面"
    },
    {
      key: "knowledgeBaseSettings.excludeArchived",
      type: "boolean",
      default: true,
      title: "排除归档页面",
      description: "从搜索结果中排除归档页面"
    },
    {
      key: "knowledgeBaseSettings.includeRecentContent",
      type: "boolean",
      default: true,
      title: "包含最近内容",
      description: "在搜索结果中包含最近编辑的内容"
    },
    {
      key: "knowledgeBaseSettings.recentContentLimit",
      type: "number",
      default: 3,
      title: "最近内容数量限制",
      description: "返回的最近编辑内容数量"
    }
  ]);

  renderApp();

  function createModel() {
    return {
      show() {
        // 确保每次点击图标时都显示设置页面
        currentContext = undefined;
        // 强制重新渲染应用程序
        renderApp();
        logseq.showMainUI();
      },
      // 处理AI聊天命令
      async openAIChat() {
        // 添加设置调试信息
        const currentSettings = await getSettings();
        console.log("=== openAIChat 当前设置 ===");
        console.log("contextSettings:", JSON.stringify(currentSettings.contextSettings, null, 2));

        // 先获取鼠标下方的块
        const mouseBlock = await getBlockUnderMouse();
        console.log("鼠标下方的块:", mouseBlock);

        // 如果找到了鼠标下方的块，则使用该块的上下文
        let context: ContextData | undefined;
        if (mouseBlock.uuid) {
          // 获取块的详细信息
          const blockInfo = await logseq.Editor.getBlock(mouseBlock.uuid);
          if (blockInfo) {
            // 构建上下文数据
            context = await getContext(mouseBlock.uuid);

            console.log("使用鼠标下方块的上下文:", context);
          }
        }

        // 如果没有找到鼠标下方的块，则使用默认方式获取上下文
        if (!context) {
          context = await getContext();
          console.log("使用默认方式获取的上下文:", context);
        }

        // 设置当前上下文并打开UI
        currentContext = context;
        renderApp();
        logseq.showMainUI();
      },
      // 快速AI回复 - 直接处理当前上下文并插入回复
      async quickAIReply() {
        // 标记为新会话，强制重置聊天状态
        isNewSession = true;
        console.log("设置为新会话，强制重置聊天状态");

        // 先获取鼠标下方的块
        const mouseBlock = await getBlockUnderMouse();
        console.log("鼠标下方的块 (快速AI回复):", mouseBlock);

        // 如果找到了鼠标下方的块，则使用该块的上下文
        let context: ContextData | undefined;
        if (mouseBlock.uuid) {
          // 获取块的详细信息
          const blockInfo = await logseq.Editor.getBlock(mouseBlock.uuid);
          if (blockInfo) {
            // 构建上下文数据
            context = await getContext(mouseBlock.uuid);

            console.log("使用鼠标下方块的上下文 (快速AI回复):", context);
          }
        }

        // 如果没有找到鼠标下方的块，则使用默认方式获取上下文
        if (!context) {
          context = await getContext();
          console.log("使用默认方式获取的上下文 (快速AI回复):", context);
        }

        // 更新全局上下文变量，确保使用最新的上下文
        console.log("更新全局上下文变量，旧值:", currentContext);
        currentContext = context;
        console.log("更新全局上下文变量，新值:", currentContext);

        if (!context || !context.content) {
          logseq.UI.showMsg("没有找到有效的上下文内容", "warning");
          return;
        }

        // 更新全局上下文变量，确保使用最新的上下文
        currentContext = context;
        console.log("已更新全局上下文变量 (快速AI回复):", currentContext);

        // 显示加载提示
        logseq.UI.showMsg("AI正在思考中...", "info");

        try {
          let responseText = "";

          // 定义响应处理器
          const responseHandlers: ApiResponseHandlers = {
            onChunk: (chunk: string) => {
              responseText += chunk;
            },
            onComplete: async (fullResponse: string) => {
              // 获取第一个块的UUID
              // 确保使用全局上下文变量，而不是局部context变量
              if (currentContext && currentContext.blockUUIDs && currentContext.blockUUIDs.length > 0) {
                const parentUUID = currentContext.blockUUIDs[0];
                console.log('使用全局上下文的父块UUID:', parentUUID);

                // 插入AI回复作为子块
                if (parentUUID) {
                  await logseq.Editor.insertBlock(parentUUID, fullResponse, { sibling: false });
                  logseq.UI.showMsg("AI回复已插入", "success");
                } else {
                  logseq.UI.showMsg("无法插入回复：找不到父块", "warning");
                }
              } else {
                logseq.UI.showMsg("无法插入回复：上下文不完整", "warning");
              }
            },
            onError: (errorMsg: string) => {
              logseq.UI.showMsg(`AI回复失败: ${errorMsg}`, "error");
            }
          };

          // 发送请求到API - 使用全局上下文变量
          // 确保使用最新的上下文
          console.log("发送请求使用的上下文:", currentContext);
          await sendChatRequest(
            currentContext.content,
            currentContext,
            responseHandlers,
            "请用中文简洁地回复以下内容：" // 默认系统提示
          );
        } catch (error) {
          console.error('快速回复出错:', error);
          logseq.UI.showMsg("AI回复失败，请重试", "error");
        }
      },
    };
  }

  const model = createModel();
  logseq.provideModel(model);
  logseq.setMainUIInlineStyle({
    zIndex: 9,
  });

  // 临时添加：重置上下文设置的命令（用于调试）
  logseq.Editor.registerSlashCommand("重置上下文设置", async () => {
    try {
      // 强制重置上下文设置为默认值
      await logseq.updateSettings({
        contextSettings: {
          contextRange: 0,
          includePageTitle: true,
          enableThemeDetection: true
        }
      });
      logseq.UI.showMsg("上下文设置已重置为默认值（contextRange=0）", "success");
      console.log("上下文设置已重置");
    } catch (error) {
      console.error("重置设置失败:", error);
      logseq.UI.showMsg("重置设置失败", "error");
    }
  });

  // 添加：强制刷新上下文的命令（修复同级块问题）
  logseq.Editor.registerSlashCommand("刷新AI上下文", async () => {
    try {
      console.log("手动刷新AI上下文...");

      // 清除所有缓存
      currentContext = undefined;

      // 重新获取上下文
      const refreshedContext = await getContext(undefined, true); // 强制刷新
      currentContext = refreshedContext;

      logseq.UI.showMsg(`上下文已刷新，获取到 ${refreshedContext.blockUUIDs.length} 个块`, "success");
      console.log("上下文刷新完成:", refreshedContext);
    } catch (error) {
      console.error("刷新上下文失败:", error);
      logseq.UI.showMsg("刷新上下文失败", "error");
    }
  });

  // 注册斜杠命令
  logseq.Editor.registerSlashCommand("AI聊天", async () => {
    return model.openAIChat();
  });

  // 注册快速AI回复斜杠命令
  logseq.Editor.registerSlashCommand("快速AI回复", async () => {
    return model.quickAIReply();
  });

  // 从设置中获取快捷键配置
  const settings = await getSettings();

  // 注册快捷键 - 打开AI聊天
  logseq.App.registerCommandPalette({
    key: "open-ai-chat",
    label: "打开AI聊天",
    keybinding: {
      binding: settings.keybindings.openChat.binding,
      mac: settings.keybindings.openChat.mac
    }
  }, async () => {
    return model.openAIChat();
  });

  // 注册快捷键 - 快速AI回复
  logseq.App.registerCommandPalette({
    key: "quick-ai-reply",
    label: "快速AI回复",
    keybinding: {
      binding: settings.keybindings.quickReply.binding,
      mac: settings.keybindings.quickReply.mac
    }
  }, async () => {
    return model.quickAIReply();
  });

  // 保存当前快捷键设置以便比较
  let currentKeybindings = JSON.stringify(settings.keybindings);
  let isFirstLoad = true;

  // 监听设置变化
  logseq.onSettingsChanged(async (newSettings) => {
    // 跳过插件初始加载时的设置变更
    if (isFirstLoad) {
      isFirstLoad = false;
      return;
    }

    // 检查快捷键是否变化
    if (newSettings.keybindings) {
      const newKeybindings = JSON.stringify(newSettings.keybindings);

      // 只在快捷键设置实际变化时才显示提示
      if (newKeybindings !== currentKeybindings) {
        console.log('快捷键设置已更新，请重启 Logseq 以应用新的快捷键设置');
        // 显示提示消息，建议用户重启 Logseq
        logseq.UI.showMsg('快捷键设置已更新，请重启 Logseq 以应用新的快捷键设置', 'info', {timeout: 5000});

        // 更新当前快捷键设置
        currentKeybindings = newKeybindings;
      }
    }
  });

  const openIconName = "ai-chat-plugin-open";

  logseq.provideStyle(css`
    .${openIconName} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${openIconName}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `);

  logseq.App.registerUIItem("toolbar", {
    key: openIconName,
    template: `
    <a data-on-click="show">
        <div class="${openIconName}">🤖</div>
    </a>
`,
  });
}

// 设置主题检测
async function setupThemeDetection() {
  // 获取当前Logseq主题模式
  const theme = await logseq.App.getStateFromStore("ui/theme");
  const isDark = theme === "dark";

  // 为根元素添加类名，以支持暗色模式
  if (isDark) {
    document.documentElement.classList.add("dark");
  } else {
    document.documentElement.classList.remove("dark");
  }

  // 监听主题变化
  logseq.App.onThemeModeChanged(({mode}) => {
    if (mode === "dark") {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  });
}

// 渲染应用程序
function renderApp() {
  root.render(
    <React.StrictMode>
      <App chatContext={currentContext} isNewSession={isNewSession} />
    </React.StrictMode>
  );

  // 重置新会话标志，避免重复重置
  if (isNewSession) {
    isNewSession = false;
  }
}

logseq.ready(main).catch(console.error);
