import React, { useState, useEffect } from 'react';
import { ApiConfig, generateUniqueId } from '../settings';
import { testApiConnection } from '../api';

interface ApiConfigManagerProps {
  apiConfigs: ApiConfig[];
  currentApiConfigId: string;
  onApiConfigsChange: (configs: ApiConfig[]) => void;
  onCurrentApiConfigChange: (configId: string) => void;
}

/**
 * API配置管理组件
 */
export const ApiConfigManager: React.FC<ApiConfigManagerProps> = ({
  apiConfigs,
  currentApiConfigId,
  onApiConfigsChange,
  onCurrentApiConfigChange
}) => {
  // 当前编辑的配置
  const [editingConfig, setEditingConfig] = useState<ApiConfig | null>(null);
  // 是否处于编辑模式
  const [isEditing, setIsEditing] = useState(false);
  // 测试连接状态
  const [testStatus, setTestStatus] = useState<{
    configId?: string;
    loading: boolean;
    success?: boolean;
    message?: string;
  }>({ loading: false });

  // 初始化编辑配置
  useEffect(() => {
    if (isEditing && !editingConfig) {
      // 如果是编辑模式但没有编辑配置，使用当前选中的配置
      const currentConfig = apiConfigs.find(config => config.id === currentApiConfigId);
      if (currentConfig) {
        setEditingConfig({ ...currentConfig });
      }
    }
  }, [isEditing, editingConfig, apiConfigs, currentApiConfigId]);

  // 处理添加新配置
  const handleAddConfig = () => {
    setEditingConfig({
      id: generateUniqueId(),
      name: '',
      apiUrl: '',
      apiKey: '',
      modelName: 'gpt-3.5-turbo',
      provider: 'openai', // 默认使用OpenAI
      isDefault: apiConfigs.length === 0 // 如果是第一个配置，设为默认
    });
    setIsEditing(true);
  };

  // 处理编辑配置
  const handleEditConfig = (configId: string) => {
    const config = apiConfigs.find(c => c.id === configId);
    if (config) {
      setEditingConfig({ ...config });
      setIsEditing(true);
    }
  };

  // 处理删除配置
  const handleDeleteConfig = (configId: string) => {
    if (apiConfigs.length <= 1) {
      alert('至少需要保留一个API配置');
      return;
    }

    if (window.confirm('确定要删除此API配置吗？')) {
      const newConfigs = apiConfigs.filter(c => c.id !== configId);
      
      // 如果删除的是当前选中的配置，选择第一个配置
      if (configId === currentApiConfigId && newConfigs.length > 0) {
        onCurrentApiConfigChange(newConfigs[0].id);
      }
      
      // 确保至少有一个默认配置
      if (newConfigs.length > 0 && !newConfigs.some(c => c.isDefault)) {
        newConfigs[0].isDefault = true;
      }
      
      onApiConfigsChange(newConfigs);
    }
  };

  // 处理设为默认配置
  const handleSetDefault = (configId: string) => {
    const newConfigs = apiConfigs.map(config => ({
      ...config,
      isDefault: config.id === configId
    }));
    onApiConfigsChange(newConfigs);
  };

  // 处理选择配置
  const handleSelectConfig = (configId: string) => {
    onCurrentApiConfigChange(configId);
  };

  // 处理保存配置
  const handleSaveConfig = () => {
    if (!editingConfig) return;

    // 验证必填字段
    if (!editingConfig.name || !editingConfig.apiUrl || !editingConfig.apiKey) {
      alert('配置名称、API URL和API Key不能为空');
      return;
    }

    // 检查名称是否重复（除了当前编辑的配置）
    const nameExists = apiConfigs.some(
      c => c.name === editingConfig.name && c.id !== editingConfig.id
    );
    if (nameExists) {
      alert(`配置名称 "${editingConfig.name}" 已存在，请使用不同的名称`);
      return;
    }

    // 创建新的配置列表
    let newConfigs: ApiConfig[];
    const existingIndex = apiConfigs.findIndex(c => c.id === editingConfig.id);

    if (existingIndex >= 0) {
      // 更新现有配置
      newConfigs = apiConfigs.map(c => 
        c.id === editingConfig.id ? { ...editingConfig } : c
      );
    } else {
      // 添加新配置
      newConfigs = [...apiConfigs, { ...editingConfig }];
    }

    // 如果设置为默认，更新其他配置
    if (editingConfig.isDefault) {
      newConfigs = newConfigs.map(c => ({
        ...c,
        isDefault: c.id === editingConfig.id
      }));
    } else if (!newConfigs.some(c => c.isDefault)) {
      // 确保至少有一个默认配置
      if (newConfigs.length > 0) {
        newConfigs[0].isDefault = true;
      }
    }

    // 更新配置列表
    onApiConfigsChange(newConfigs);
    
    // 如果是新配置或当前没有选中的配置，选择这个配置
    if (existingIndex < 0 || !currentApiConfigId) {
      onCurrentApiConfigChange(editingConfig.id);
    }

    // 退出编辑模式
    setIsEditing(false);
    setEditingConfig(null);
  };

  // 处理取消编辑
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingConfig(null);
  };

  // 处理测试连接
  const handleTestConnection = async (config: ApiConfig) => {
    setTestStatus({ configId: config.id, loading: true });

    try {
      const result = await testApiConnection(config);
      setTestStatus({
        configId: config.id,
        loading: false,
        success: result.success,
        message: result.message
      });
    } catch (error) {
      setTestStatus({
        configId: config.id,
        loading: false,
        success: false,
        message: `测试连接时出错: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };

  // 渲染配置列表
  const renderConfigList = () => {
    if (apiConfigs.length === 0) {
      return <div className="text-gray-500 dark:text-gray-400 italic">没有API配置，请添加一个</div>;
    }

    return (
      <div className="space-y-2">
        {apiConfigs.map(config => (
          <div 
            key={config.id} 
            className={`border rounded-lg p-3 ${
              config.id === currentApiConfigId 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-300 dark:border-gray-600'
            }`}
          >
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">
                  {config.name} 
                  {config.isDefault && <span className="ml-2 text-xs text-blue-500 dark:text-blue-400">(默认)</span>}
                </h4>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <div>URL: {config.apiUrl}</div>
                  <div>API Key: {config.apiKey.substring(0, 5)}***{config.apiKey.substring(config.apiKey.length - 5)}</div>
                  <div>模型: {config.modelName}</div>
                  {config.followUpModelName && config.followUpModelName !== config.modelName && (
                    <div>追问模型: {config.followUpModelName}</div>
                  )}
                  <div>提供商: {config.provider}</div>
                </div>
              </div>
              
              <div className="flex space-x-2">
                {config.id !== currentApiConfigId && (
                  <button 
                    onClick={() => handleSelectConfig(config.id)}
                    className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    使用
                  </button>
                )}
                
                <button 
                  onClick={() => handleEditConfig(config.id)}
                  className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                >
                  编辑
                </button>
                
                <button 
                  onClick={() => handleDeleteConfig(config.id)}
                  className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                >
                  删除
                </button>
                
                {!config.isDefault && (
                  <button 
                    onClick={() => handleSetDefault(config.id)}
                    className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    设为默认
                  </button>
                )}
              </div>
            </div>
            
            <div className="mt-2">
              <button
                onClick={() => handleTestConnection(config)}
                className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
                disabled={testStatus.loading && testStatus.configId === config.id}
              >
                {testStatus.loading && testStatus.configId === config.id ? '测试中...' : '测试连接'}
              </button>
              
              {testStatus.configId === config.id && testStatus.message && (
                <span className={`ml-2 text-xs ${testStatus.success ? 'text-green-500' : 'text-red-500'}`}>
                  {testStatus.message}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // 渲染编辑表单
  const renderEditForm = () => {
    if (!editingConfig) return null;

    return (
      <div className="bg-white dark:bg-gray-800 p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
        <h3 className="text-lg font-medium mb-4">{editingConfig.id ? '编辑API配置' : '添加新的API配置'}</h3>
        
        <div className="space-y-4">
          {/* 配置名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              配置名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={editingConfig.name}
              onChange={(e) => setEditingConfig({...editingConfig, name: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="例如: OpenAI API"
              required
            />
          </div>
          
          {/* API提供商选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              API提供商 <span className="text-red-500">*</span>
            </label>
            <select
              value={editingConfig.provider}
              onChange={(e) => setEditingConfig({...editingConfig, provider: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              required
            >
              <option value="openai">OpenAI</option>
              <option value="gemini">Google Gemini</option>
              <option value="anthropic">Anthropic (Claude)</option>
              <option value="azure">Azure OpenAI</option>
            </select>
          </div>
          
          {/* API URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              API URL <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={editingConfig.apiUrl}
              onChange={(e) => setEditingConfig({...editingConfig, apiUrl: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="例如: https://api.openai.com/v1/chat/completions"
              required
            />
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {editingConfig.provider === 'openai' && 'OpenAI API地址: https://api.openai.com/v1/chat/completions'}
              {editingConfig.provider === 'gemini' && 'Google Gemini API地址: https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent'}
              {editingConfig.provider === 'anthropic' && 'Anthropic API地址: https://api.anthropic.com/v1/complete'}
            </div>
          </div>
          
          {/* API密钥 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              API Key <span className="text-red-500">*</span>
            </label>
            <input
              type="password"
              value={editingConfig.apiKey}
              onChange={(e) => setEditingConfig({...editingConfig, apiKey: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="输入API Key"
              required
            />
          </div>
          
          {/* 模型名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              模型名称
            </label>
            <input
              type="text"
              value={editingConfig.modelName}
              onChange={(e) => setEditingConfig({...editingConfig, modelName: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder={
                editingConfig.provider === 'openai' ? 'gpt-3.5-turbo' : 
                editingConfig.provider === 'gemini' ? 'gemini-pro' : 
                editingConfig.provider === 'anthropic' ? 'claude-2' : 
                'model-name'
              }
            />
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {editingConfig.provider === 'openai' && '例如: gpt-3.5-turbo, gpt-4, gpt-4-turbo'}
              {editingConfig.provider === 'gemini' && '例如: gemini-pro, gemini-pro-vision'}
              {editingConfig.provider === 'anthropic' && '例如: claude-2, claude-instant-1'}
            </div>
          </div>
          
          {/* 追问模型名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              追问模型名称
            </label>
            <input
              type="text"
              value={editingConfig.followUpModelName || editingConfig.modelName}
              onChange={(e) => setEditingConfig({...editingConfig, followUpModelName: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder={
                editingConfig.provider === 'openai' ? 'gpt-4' : 
                editingConfig.provider === 'gemini' ? 'gemini-pro' : 
                editingConfig.provider === 'anthropic' ? 'claude-2' : 
                'model-name'
              }
            />
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              追问时使用的模型（可以与主模型不同，如使用功能更强的模型）。留空则使用主模型。
            </div>
          </div>
          
          {/* 设为默认选项 */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isDefault"
              checked={editingConfig.isDefault}
              onChange={(e) => setEditingConfig({...editingConfig, isDefault: e.target.checked})}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              设为默认配置
            </label>
          </div>
          
          {/* 按钮 */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={handleCancelEdit}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              取消
            </button>
            <button
              onClick={handleSaveConfig}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              保存
            </button>
            <button
              onClick={() => handleTestConnection(editingConfig)}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              disabled={!editingConfig.apiUrl || !editingConfig.apiKey}
            >
              测试连接
            </button>
          </div>
          
          {/* 测试连接状态 */}
          {testStatus.configId === editingConfig.id && (
            <div className={`mt-4 p-3 rounded ${
              testStatus.success ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200' : 
              'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200'
            }`}>
              {testStatus.loading ? (
                <div className="flex items-center">
                  <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  测试连接中...
                </div>
              ) : (
                <div>{testStatus.message}</div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="api-config-manager">
      {renderEditForm()}
      
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold">API配置</h3>
        {!isEditing && (
          <button
            onClick={handleAddConfig}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            添加配置
          </button>
        )}
      </div>
      
      {renderConfigList()}
    </div>
  );
};
