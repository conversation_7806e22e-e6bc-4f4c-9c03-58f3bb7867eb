import React, { useState, useEffect } from "react";
import { PluginSettings, DEFAULT_SETTINGS, updateSettings, getSettings } from "../settings";
import { ApiConfigManager } from "./ApiConfigManager";

interface SettingsUIProps {
  onClose: () => void;
}

export const SettingsUI: React.FC<SettingsUIProps> = ({ onClose }) => {
  // 设置状态
  const [settings, setSettings] = useState<PluginSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState("");
  const [keybindingConflict, setKeybindingConflict] = useState<{message: string, action: string, platform: string} | null>(null);

  // 新提示词状态
  const [newPrompt, setNewPrompt] = useState({ name: "", prompt: "" });
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  // 管理折叠面板状态
  const [expandedSections, setExpandedSections] = useState({
    api: true,
    history: false,
    keybindings: false,
    prompts: false,
    ui: false
  });

  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const loadedSettings = await getSettings();
        setSettings(loadedSettings);
      } catch (error) {
        console.error("加载设置失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // 处理关闭
  const handleClose = () => {
    onClose();
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({ ...prev, [name]: value }));
  };

  // 处理切换变化
  const handleToggleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSettings(prev => ({ ...prev, [name]: checked }));
  };

  // 处理UI设置变化
  const handleUISettingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      uiSettings: {
        ...prev.uiSettings,
        [name]: checked
      }
    }));
  };

  // 处理上下文设置变化
  const handleContextSettingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked, type, value } = e.target;
    const newValue = type === 'checkbox' ? checked : (type === 'number' ? parseInt(value) : value);

    setSettings(prev => ({
      ...prev,
      contextSettings: {
        ...prev.contextSettings,
        [name]: newValue
      }
    }));
  };

  // 处理快捷键变化
  const handleKeybindingChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    action: 'openChat' | 'quickReply',
    platform: 'binding' | 'mac'
  ) => {
    const value = e.target.value;
    setSettings(prev => ({
      ...prev,
      keybindings: {
        ...prev.keybindings,
        [action]: {
          ...prev.keybindings[action],
          [platform]: value
        }
      }
    }));
  };

  // 处理快捷键按键事件
  const handleKeybindingKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    action: 'openChat' | 'quickReply',
    platform: 'binding' | 'mac'
  ) => {
    // 阻止默认行为，防止输入框接收字符
    e.preventDefault();

    // 忽略单独的修饰键
    if (['Control', 'Alt', 'Shift', 'Meta'].includes(e.key)) {
      return;
    }

    // 构建快捷键字符串
    let shortcut = [];
    if (e.ctrlKey) shortcut.push('ctrl');
    if (e.altKey) shortcut.push('alt');
    if (e.shiftKey) shortcut.push('shift');
    if (e.metaKey) {
      if (platform === 'mac') {
        shortcut.push('cmd');
      } else {
        shortcut.push('meta');
      }
    }

    // 添加主键（非修饰键）
    if (!['Control', 'Alt', 'Shift', 'Meta'].includes(e.key)) {
      shortcut.push(e.key.toLowerCase());
    }

    // 设置快捷键值
    const shortcutString = shortcut.join('+');

    // 检查快捷键冲突
    const conflictInfo = checkKeybindingConflict(shortcutString, platform);

    // 更新设置
    setSettings(prev => ({
      ...prev,
      keybindings: {
        ...prev.keybindings,
        [action]: {
          ...prev.keybindings[action],
          [platform]: shortcutString
        }
      }
    }));

    // 如果有冲突，显示提示
    if (conflictInfo) {
      setKeybindingConflict({
        message: `快捷键冲突: ${shortcutString} 已被 ${conflictInfo} 使用`,
        action,
        platform
      });
    } else {
      // 清除冲突提示（如果当前没有冲突）
      if (keybindingConflict && keybindingConflict.action === action && keybindingConflict.platform === platform) {
        setKeybindingConflict(null);
      }
    }
  };

  // 检查快捷键冲突
  const checkKeybindingConflict = (shortcut: string, platform: 'binding' | 'mac'): string | null => {
    // 检查是否与系统快捷键冲突
    const systemShortcuts = {
      'ctrl+c': '复制',
      'ctrl+v': '粘贴',
      'ctrl+x': '剪切',
      'ctrl+z': '撤销',
      'ctrl+y': '重做',
      'ctrl+a': '全选',
      'ctrl+s': '保存',
      'ctrl+f': '查找',
      'ctrl+n': '新建',
      'ctrl+o': '打开',
      'ctrl+p': '打印',
      'ctrl+w': '关闭',
      'alt+f4': '关闭应用',
      'alt+tab': '切换应用',
      // Mac 特定快捷键
      'cmd+c': '复制',
      'cmd+v': '粘贴',
      'cmd+x': '剪切',
      'cmd+z': '撤销',
      'cmd+a': '全选',
      'cmd+s': '保存',
      'cmd+f': '查找',
      'cmd+n': '新建',
      'cmd+o': '打开',
      'cmd+p': '打印',
      'cmd+w': '关闭',
      'cmd+q': '退出应用'
    };

    // 检查是否与系统快捷键冲突
    if (systemShortcuts[shortcut as keyof typeof systemShortcuts]) {
      return `系统快捷键 (${systemShortcuts[shortcut as keyof typeof systemShortcuts]})`;
    }

    // 检查是否与其他 Logseq 快捷键冲突
    // 这里可以添加 Logseq 常用快捷键的检查
    const logseqShortcuts = {
      'ctrl+enter': '创建新块',
      'shift+enter': '创建新行',
      'ctrl+shift+enter': '创建任务',
      'ctrl+o': '打开文件',
      'ctrl+p': '命令面板',
      'ctrl+shift+f': '全局搜索',
      'ctrl+shift+g': '图谱视图',
      'cmd+enter': '创建新块(Mac)',
      'cmd+shift+enter': '创建任务(Mac)',
      'cmd+o': '打开文件(Mac)',
      'cmd+p': '命令面板(Mac)',
      'cmd+shift+f': '全局搜索(Mac)'
    };

    if (logseqShortcuts[shortcut as keyof typeof logseqShortcuts]) {
      return `Logseq 快捷键 (${logseqShortcuts[shortcut as keyof typeof logseqShortcuts]})`;
    }

    // 检查是否与插件内其他快捷键冲突
    const { openChat, quickReply } = settings.keybindings;

    if (platform === 'binding') {
      if (shortcut === quickReply.binding && shortcut !== openChat.binding) {
        return '快速AI回复';
      } else if (shortcut === openChat.binding && shortcut !== quickReply.binding) {
        return '打开AI聊天';
      }
    } else if (platform === 'mac') {
      if (shortcut === quickReply.mac && shortcut !== openChat.mac) {
        return '快速AI回复';
      } else if (shortcut === openChat.mac && shortcut !== quickReply.mac) {
        return '打开AI聊天';
      }
    }

    return null;
  };

  // 处理新提示词变化
  const handleNewPromptChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewPrompt(prev => ({ ...prev, [name]: value }));
  };

  // 添加新提示词
  const handleAddPrompt = () => {
    if (!newPrompt.name || !newPrompt.prompt) return;

    setSettings(prev => ({
      ...prev,
      customPrompts: [...prev.customPrompts, { ...newPrompt }]
    }));

    setNewPrompt({ name: "", prompt: "" });
  };

  // 删除提示词
  const handleDeletePrompt = (index: number) => {
    setSettings(prev => ({
      ...prev,
      customPrompts: prev.customPrompts.filter((_, i) => i !== index)
    }));
  };

  // 开始编辑提示词
  const handleStartEditing = (index: number) => {
    setEditingIndex(index);
  };

  // 编辑提示词变化
  const handleEditPromptChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number
  ) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      customPrompts: prev.customPrompts.map((prompt, i) =>
        i === index ? { ...prompt, [name]: value } : prompt
      )
    }));
  };

  // 保存编辑
  const handleSaveEditing = () => {
    setEditingIndex(null);
  };

  // 取消编辑
  const handleCancelEditing = () => {
    setEditingIndex(null);
  };

  // 保存设置
  const handleSaveSettings = async () => {
    setIsSaving(true);
    setSaveMessage("");

    try {
      await updateSettings(settings);
      setSaveMessage("设置已保存");
      setTimeout(() => setSaveMessage(""), 3000);
    } catch (error) {
      console.error("保存设置失败:", error);
      setSaveMessage("保存失败，请重试");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="p-4 text-center">加载中...</div>;
  }

  // 切换折叠面板
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section as keyof typeof prev]
    }));
  };

  return (
    <div className="p-4 max-w-2xl mx-auto text-gray-800 dark:text-gray-200 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 40px)' }}>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">AI 聊天设置</h1>
        <button
          onClick={handleClose}
          className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          title="关闭设置"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* API配置 */}
      <div className="mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div
          className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer"
          onClick={() => toggleSection('api')}
        >
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">API 配置</h2>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 transition-transform ${expandedSections.api ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        {expandedSections.api && (
          <div className="p-3">
            <ApiConfigManager
              apiConfigs={settings.apiConfigs || []}
              currentApiConfigId={settings.currentApiConfigId || ''}
              onApiConfigsChange={(configs) => {
                setSettings(prev => {
                  const newSettings = { ...prev, apiConfigs: configs };

                  // 如果有默认配置，确保当前选中的是默认配置
                  const defaultConfig = configs.find(c => c.isDefault);
                  if (defaultConfig && newSettings.currentApiConfigId !== defaultConfig.id) {
                    newSettings.currentApiConfigId = defaultConfig.id;
                  }

                  // 更新兼容旧版本的API设置
                  const currentConfig = configs.find(c => c.id === newSettings.currentApiConfigId);
                  if (currentConfig) {
                    newSettings.apiUrl = currentConfig.apiUrl;
                    newSettings.apiKey = currentConfig.apiKey;
                    newSettings.modelName = currentConfig.modelName;
                  }

                  // 立即保存设置
                  updateSettings(newSettings).catch(err => console.error('保存API配置时出错:', err));
                  return newSettings;
                });
              }}
              onCurrentApiConfigChange={(configId) => {
                setSettings(prev => {
                  // 找到选中的配置
                  const selectedConfig = prev.apiConfigs?.find(c => c.id === configId);
                  if (!selectedConfig) return prev;

                  const newSettings = {
                    ...prev,
                    currentApiConfigId: configId,
                    // 更新兼容旧版本的API设置
                    apiUrl: selectedConfig.apiUrl,
                    apiKey: selectedConfig.apiKey,
                    modelName: selectedConfig.modelName
                  };

                  // 立即保存设置
                  updateSettings(newSettings).catch(err => console.error('保存当前API配置时出错:', err));
                  return newSettings;
                });
              }}
            />

            <div className="mb-4">
              <label className="block mb-2 font-medium">温度</label>
              <input
                type="number"
                name="temperature"
                value={settings.temperature}
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  if (!isNaN(value) && value >= 0 && value <= 2) {
                    setSettings(prev => ({ ...prev, temperature: value }));
                  }
                }}
                min="0"
                max="2"
                step="0.1"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">较低的值使输出更确定，较高的值使输出更多样化。范围: 0-2</p>
            </div>

            <div className="mb-4">
              <label className="block mb-2 font-medium">最大输出标记数</label>
              <input
                type="number"
                name="maxTokens"
                value={settings.maxTokens}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value > 0) {
                    setSettings(prev => ({ ...prev, maxTokens: value }));
                  }
                }}
                min="1"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">模型将生成的最大标记数量。</p>
            </div>
          </div>
        )}
      </div>

      {/* 历史记录设置 */}
      <div className="mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div
          className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer"
          onClick={() => toggleSection('history')}
        >
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">历史记录设置</h2>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 transition-transform ${expandedSections.history ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        {expandedSections.history && (
          <div className="p-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableHistory"
                name="enableHistory"
                checked={settings.enableHistory}
                onChange={handleToggleChange}
                className="mr-2"
              />
              <label htmlFor="enableHistory" className="font-medium">
                启用会话历史记录
              </label>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              启用后，将在同一聊天会话中保存历史对话记录。
            </p>
          </div>
        )}
      </div>

      {/* 快捷键设置 */}
      <div className="mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div
          className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer"
          onClick={() => toggleSection('keybindings')}
        >
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">快捷键设置</h2>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 transition-transform ${expandedSections.keybindings ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        {expandedSections.keybindings && (
          <div className="p-3">
            <div className="mb-4">
              <label className="block mb-2 font-medium">打开AI聊天</label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block mb-1 text-sm">Windows/Linux</label>
                  <input
                    type="text"
                    value={settings.keybindings.openChat.binding}
                    onChange={(e) => handleKeybindingChange(e, 'openChat', 'binding')}
                    onKeyDown={(e) => handleKeybindingKeyDown(e, 'openChat', 'binding')}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                    placeholder="ctrl+g"
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm">Mac</label>
                  <input
                    type="text"
                    value={settings.keybindings.openChat.mac}
                    onChange={(e) => handleKeybindingChange(e, 'openChat', 'mac')}
                    onKeyDown={(e) => handleKeybindingKeyDown(e, 'openChat', 'mac')}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                    placeholder="cmd+g"
                  />
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                格式示例：ctrl+g, ctrl+shift+g, alt+g
              </p>
              {keybindingConflict && keybindingConflict.action === 'openChat' && (
                <p className="text-sm text-red-500 mt-1">
                  {keybindingConflict.message}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label className="block mb-2 font-medium">快速AI回复</label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block mb-1 text-sm">Windows/Linux</label>
                  <input
                    type="text"
                    value={settings.keybindings.quickReply.binding}
                    onChange={(e) => handleKeybindingChange(e, 'quickReply', 'binding')}
                    onKeyDown={(e) => handleKeybindingKeyDown(e, 'quickReply', 'binding')}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                    placeholder="ctrl+shift+g"
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm">Mac</label>
                  <input
                    type="text"
                    value={settings.keybindings.quickReply.mac}
                    onChange={(e) => handleKeybindingChange(e, 'quickReply', 'mac')}
                    onKeyDown={(e) => handleKeybindingKeyDown(e, 'quickReply', 'mac')}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                    placeholder="cmd+shift+g"
                  />
                </div>
              </div>
              {keybindingConflict && keybindingConflict.action === 'quickReply' && (
                <p className="text-sm text-red-500 mt-1">
                  {keybindingConflict.message}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 自定义提示 */}
      <div className="mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div
          className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer"
          onClick={() => toggleSection('prompts')}
        >
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">自定义提示词</h2>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 transition-transform ${expandedSections.prompts ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        {expandedSections.prompts && (
          <div className="p-3">
            {/* 添加新提示 */}
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded mb-4">
              <h3 className="font-medium mb-2">添加新提示词</h3>
              <div className="mb-3">
                <label className="block mb-1 text-sm">提示词名称</label>
                <input
                  type="text"
                  name="name"
                  value={newPrompt.name}
                  onChange={handleNewPromptChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                  placeholder="提示名称，如：总结、翻译等"
                />
              </div>
              <div className="mb-3">
                <label className="block mb-1 text-sm">提示词内容</label>
                <textarea
                  name="prompt"
                  value={newPrompt.prompt}
                  onChange={handleNewPromptChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                  rows={3}
                  placeholder="提示词内容，如：请总结以下内容的要点："
                />
              </div>
              <button
                onClick={handleAddPrompt}
                disabled={!newPrompt.name || !newPrompt.prompt}
                className="bg-blue-500 text-white px-3 py-1 rounded disabled:bg-gray-300 dark:disabled:bg-gray-700"
              >
                添加提示词
              </button>
            </div>

            {/* 现有提示词列表 */}
            <div>
              <h3 className="font-medium mb-2">现有提示词</h3>
              {settings.customPrompts.length === 0 ? (
                <p className="text-gray-500">暂无自定义提示词</p>
              ) : (
                <div className="space-y-3">
                  {settings.customPrompts.map((prompt, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                      {editingIndex === index ? (
                        // 编辑模式
                        <div className="p-3 bg-white dark:bg-gray-800">
                          <div className="mb-2">
                            <label className="block mb-1 text-sm">提示词名称</label>
                            <input
                              type="text"
                              name="name"
                              value={prompt.name}
                              onChange={(e) => handleEditPromptChange(e, index)}
                              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                            />
                          </div>
                          <div className="mb-2">
                            <label className="block mb-1 text-sm">提示词内容</label>
                            <textarea
                              name="prompt"
                              value={prompt.prompt}
                              onChange={(e) => handleEditPromptChange(e, index)}
                              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                              rows={3}
                            />
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={handleSaveEditing}
                              className="px-2 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600"
                            >
                              完成
                            </button>
                            <button
                              onClick={handleCancelEditing}
                              className="px-2 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 text-sm rounded hover:bg-gray-400 dark:hover:bg-gray-500"
                            >
                              取消
                            </button>
                          </div>
                        </div>
                      ) : (
                        // 查看模式
                        <div className="flex justify-between p-3 bg-white dark:bg-gray-800">
                          <div>
                            <div className="font-medium text-gray-800 dark:text-gray-200">{prompt.name}</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">{prompt.prompt}</div>
                          </div>
                          <div className="flex items-start space-x-2">
                            <button
                              onClick={() => handleStartEditing(index)}
                              className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                              title="编辑提示词"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              onClick={() => handleDeletePrompt(index)}
                              className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                              title="删除提示词"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* UI设置 */}
      <div className="mb-6 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div
          className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer"
          onClick={() => toggleSection('ui')}
        >
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">界面设置</h2>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 transition-transform ${expandedSections.ui ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        {expandedSections.ui && (
          <div className="p-3 space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoResponseOnOpen"
                name="autoResponseOnOpen"
                checked={settings.uiSettings?.autoResponseOnOpen || false}
                onChange={handleUISettingChange}
                className="mr-2"
              />
              <label htmlFor="autoResponseOnOpen" className="font-medium">
                打开聊天窗口时自动响应
              </label>
            </div>
            <p className="text-sm text-gray-500 ml-6">
              启用后，打开AI聊天窗口时会自动发送当前上下文给AI并获取回复。
            </p>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoRefreshContext"
                name="autoRefreshContext"
                checked={settings.uiSettings?.autoRefreshContext || false}
                onChange={handleUISettingChange}
                className="mr-2"
              />
              <label htmlFor="autoRefreshContext" className="font-medium">
                启用自动刷新上下文
              </label>
            </div>
            <p className="text-sm text-gray-500 ml-6">
              启用后，对话框会自动检测块变化并刷新上下文内容，无需手动操作。
            </p>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="showToolbar"
                name="showToolbar"
                checked={settings.uiSettings?.showToolbar || false}
                onChange={handleUISettingChange}
                className="mr-2"
              />
              <label htmlFor="showToolbar" className="font-medium">
                显示工具栏
              </label>
            </div>
            <p className="text-sm text-gray-500 ml-6">
              在对话窗口中显示快捷工具栏，提供复制、格式化等常用操作。
            </p>

            {/* 上下文设置分组 */}
            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">上下文设置</h3>

              <div className="space-y-4">
                <div>
                  <label htmlFor="contextRange" className="block text-sm font-medium mb-2">
                    上下文范围（块数）
                  </label>
                  <input
                    type="number"
                    id="contextRange"
                    name="contextRange"
                    value={settings.contextSettings?.contextRange || 0}
                    onChange={handleContextSettingChange}
                    min="0"
                    max="10"
                    className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    获取当前块前后的块数量。0表示只获取当前块，1表示前后各1个块。
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="includePageTitle"
                    name="includePageTitle"
                    checked={settings.contextSettings?.includePageTitle || false}
                    onChange={handleContextSettingChange}
                    className="mr-2"
                  />
                  <label htmlFor="includePageTitle" className="font-medium">
                    包含页面标题
                  </label>
                </div>
                <p className="text-sm text-gray-500 ml-6">
                  在上下文中包含当前页面的标题信息。
                </p>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableThemeDetection"
                    name="enableThemeDetection"
                    checked={settings.contextSettings?.enableThemeDetection || false}
                    onChange={handleContextSettingChange}
                    className="mr-2"
                  />
                  <label htmlFor="enableThemeDetection" className="font-medium">
                    启用主题检测
                  </label>
                </div>
                <p className="text-sm text-gray-500 ml-6">
                  自动识别当前页面的主题，为AI提供更准确的上下文信息。
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 保存按钮 */}
      <div className="mt-6 flex items-center">
        <button
          onClick={handleSaveSettings}
          disabled={isSaving}
          className="bg-green-500 text-white px-4 py-2 rounded font-medium disabled:bg-gray-300 dark:disabled:bg-gray-700"
        >
          {isSaving ? "保存中..." : "保存设置"}
        </button>

        {saveMessage && (
          <span className="ml-3 text-green-600">{saveMessage}</span>
        )}
      </div>
    </div>
  );
};
