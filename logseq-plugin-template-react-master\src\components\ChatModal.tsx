import React, { useState, useEffect, useRef, useCallback } from 'react';
import ReactDOM from 'react-dom';
import { getSettings, PluginSettings, updateSettings, ApiConfig } from '../settings';
import { ContextData } from '../contextManager';
import { sendChatRequest, ApiResponseHandlers, getCurrentApiConfig } from '../api';
import { formatResponseToBlocks, prepareBlocksForInsertion, BlockEntity } from '../formatter';
// 导入package.json中的版本信息
import { version } from '../../package.json';

// 聊天历史记录接口
interface ChatHistoryItem {
  type: 'question' | 'answer' | 'followup' | 'followup-answer';
  content: string;
  timestamp: number;
}

interface ChatModalProps {
  context: ContextData;
  onClose: () => void;
  onReplace: (content: string) => void;
  onInsert: (content: string) => void;
  onRefreshContext?: () => Promise<void>;
  forceReset?: boolean;
}

interface Position {
  x: number;
  y: number;
}

interface Size {
  width: number;
  height: number;
}

/**
 * 聊天模态框组件
 */
export const ChatModal: React.FC<ChatModalProps> = ({ context, onClose, onReplace, onInsert, onRefreshContext, forceReset }) => {
  // AI响应内容
  const [aiResponse, setAiResponse] = useState('');
  // 追问模式下的原始响应
  const [originalResponse, setOriginalResponse] = useState('');
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 错误信息
  const [error, setError] = useState('');
  // 插件设置
  const [settings, setSettings] = useState<PluginSettings | null>(null);
  // 选中的提示
  const [selectedPrompt, setSelectedPrompt] = useState('default'); // 默认选择默认提示词
  // 自定义提示词
  const [customPrompt, setCustomPrompt] = useState('');
  // 是否显示自定义输入
  const [showCustomInput, setShowCustomInput] = useState(true); // 默认显示自定义输入
  // 流式输出的当前位置
  const [streamPosition, setStreamPosition] = useState(0);
  // 匹配的提示词列表
  const [matchedPrompts, setMatchedPrompts] = useState<Array<{name: string, prompt: string}>>([]);
  // 当前选中的提示词建议索引
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  // 是否显示提示词建议
  const [showSuggestions, setShowSuggestions] = useState(false);
  // 当前问题内容 - 用于重新回答
  const [currentQuestion, setCurrentQuestion] = useState<string>('');
  // 追问模式状态
  const [isFollowUpMode, setIsFollowUpMode] = useState(false);
  // 追问内容
  const [followUpQuestion, setFollowUpQuestion] = useState('');
  // 追问输入引用
  const followUpInputRef = useRef<HTMLInputElement>(null);
  // 阅读模式状态
  const [isReadingMode, setIsReadingMode] = useState(false);
  // 是否使用同级插入模式（而不是子块插入）
  const [insertAsSibling, setInsertAsSibling] = useState(false);
  // 聊天历史记录
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);
  // 右键菜单状态
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    show: boolean;
    selectedText: string; // 添加选中文本状态
  }>({
    x: 0,
    y: 0,
    show: false,
    selectedText: '',
  });

  // 窗口相关状态
  const [windowPosition, setWindowPosition] = useState<Position>({ x: 0, y: 0 });
  const [windowSize, setWindowSize] = useState<Size>({ width: 600, height: 500 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 });
  const [isDetached, setIsDetached] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [showToolbar, setShowToolbar] = useState(true);
  const [currentTheme, setCurrentTheme] = useState('system');

  // API配置相关状态
  const [currentApiConfig, setCurrentApiConfig] = useState<ApiConfig | null>(null);
  const [apiConfigs, setApiConfigs] = useState<ApiConfig[]>([]);
  const [currentApiConfigId, setCurrentApiConfigId] = useState<string>("");
  // 追问对话框中使用的模型 ID
  const [followupModelId, setFollowupModelId] = useState<string>("");
  // 自动刷新相关状态
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState<boolean>(false);
  const [isAutoRefreshing, setIsAutoRefreshing] = useState<boolean>(false);

  // 模态框引用
  const modalRef = useRef<HTMLDivElement>(null);
  // 自定义输入引用
  const promptInputRef = useRef<HTMLInputElement>(null);
  // 响应容器引用
  const responseRef = useRef<HTMLDivElement>(null);
  // 提示词建议容器引用
  const suggestionsRef = useRef<HTMLDivElement>(null);
  // 标题栏引用
  const titlebarRef = useRef<HTMLDivElement>(null);
  // 工具栏引用
  const toolbarRef = useRef<HTMLDivElement>(null);

  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const loadedSettings = await getSettings();
        console.log("Loaded settings:", loadedSettings);
        setSettings(loadedSettings);

        // 设置窗口大小和UI设置
        if (loadedSettings.uiSettings) {
          setWindowSize({
            width: loadedSettings.uiSettings.defaultWidth,
            height: loadedSettings.uiSettings.defaultHeight
          });
          setShowToolbar(loadedSettings.uiSettings.showToolbar);
          setCurrentTheme(loadedSettings.uiSettings.theme);
          // 从设置中读取自动刷新状态
          setAutoRefreshEnabled(loadedSettings.uiSettings.autoRefreshContext || false);
        }

        // 加载上次选择的提示词
        if (loadedSettings.lastSelectedPrompt) {
          console.log("Last selected prompt from settings:", loadedSettings.lastSelectedPrompt);
          setSelectedPrompt(loadedSettings.lastSelectedPrompt);
        } else {
          console.log("No lastSelectedPrompt found in settings, using default");
          setSelectedPrompt('default');
        }

        // 加载API配置
        console.log('加载API配置');

        // 设置API配置列表
        if (Array.isArray(loadedSettings.apiConfigs)) {
          setApiConfigs(loadedSettings.apiConfigs);
          setCurrentApiConfigId(loadedSettings.currentApiConfigId || '');

          // 初始化追问模型 ID，如果没有存储过，则使用当前模型 ID
          if (loadedSettings.followupModelId) {
            setFollowupModelId(loadedSettings.followupModelId);
          } else {
            setFollowupModelId(loadedSettings.currentApiConfigId || '');
          }
        }

        // 获取当前API配置
        try {
          const apiConfig = await getCurrentApiConfig();
          console.log('获取到的当前配置:', apiConfig);
          setCurrentApiConfig(apiConfig);
        } catch (error) {
          console.error('加载API配置失败:', error);
        }
      } catch (error) {
        console.error('加载设置出错:', error);
        setError('无法加载插件设置，请检查配置');
      }
    };

    loadSettings();
  }, []);

  // 处理窗口拖动
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (titlebarRef.current && titlebarRef.current.contains(e.target as Node)) {
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && modalRef.current) {
      const dx = e.clientX - dragStart.x;
      const dy = e.clientY - dragStart.y;

      setWindowPosition(prev => ({
        x: prev.x + dx,
        y: prev.y + dy
      }));

      setDragStart({ x: e.clientX, y: e.clientY });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 添加鼠标事件监听
  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  // 切换分离模式
  const toggleDetachedMode = () => {
    setIsDetached(!isDetached);
    if (!isDetached) {
      // 当切换到分离模式时，设置初始位置
      setWindowPosition({ x: 50, y: 50 });
    }
  };

  // 防止初始加载时自动发送请求的标志
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // 检查是否开启了自动响应功能
  const autoResponseEnabled = settings?.uiSettings?.autoResponseOnOpen || false;

  // 当选择提示变化时自动发送请求
  useEffect(() => {
    // 如果是初始加载，判断是否自动响应
    if (isInitialLoad) {
      // 如果开启了自动响应功能且有内容，则自动发送请求
      if (autoResponseEnabled && context?.content) {
        // 保存当前问题内容
        setCurrentQuestion(context.content);
        handleSendMessage();
      }
      setIsInitialLoad(false);
      return;
    }

    // 当选择提示词变化时，自动发送请求
    if (selectedPrompt !== 'default' && selectedPrompt !== 'custom' && context?.content) {
      // 如果有当前问题内容，使用它重新回答
      if (currentQuestion) {
        handleSendMessage();
      } else {
        // 否则使用上下文内容
        setCurrentQuestion(context.content);
        handleSendMessage();
      }
    }

    // 如果选择了自定义提示，聚焦到输入框
    if (selectedPrompt === 'custom') {
      setShowCustomInput(true);
      setTimeout(() => {
        promptInputRef.current?.focus();
      }, 50);
    } else {
      setShowCustomInput(false);
    }
  }, [selectedPrompt]);

  // 初始化后聚焦到自定义输入框
  useEffect(() => {
    if (showCustomInput) {
      setTimeout(() => {
        promptInputRef.current?.focus();
      }, 50);
    }
  }, []);

  // 当AI响应更新时，重置追问模式
  useEffect(() => {
    if (aiResponse && isFollowUpMode) {
      setIsFollowUpMode(false);
      setFollowUpQuestion('');
    }
  }, [aiResponse]);

  // 处理强制重置聊天状态
  useEffect(() => {
    if (forceReset) {
      console.log("ChatModal组件检测到强制重置标志，重置聊天状态");
      // 重置所有聊天相关状态
      setAiResponse('');
      setOriginalResponse('');
      setError('');
      setIsLoading(false);
      setCurrentQuestion('');
      setIsFollowUpMode(false);
      setFollowUpQuestion('');
      setIsReadingMode(false);
      setChatHistory([]);
      setContextMenu({
        x: 0,
        y: 0,
        show: false,
        selectedText: '',
      });

      // 如果开启了自动响应功能且有内容，则自动发送请求
      if (autoResponseEnabled && context?.content) {
        // 保存当前问题内容
        setCurrentQuestion(context.content);
        // 短暂延迟确保状态已重置
        setTimeout(() => {
          handleSendMessage();
        }, 100);
      }
    }
  }, [forceReset, context]);

  // 点击页面其他区域时隐藏提示词建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 自动刷新上下文的定时器
  useEffect(() => {
    if (!autoRefreshEnabled || !onRefreshContext) return;

    let lastCheckedBlockUUID: string | null = null;
    let lastCheckedContent: string | null = null;

    const checkAndRefreshContext = async () => {
      try {
        // 获取当前编辑的块或光标所在的块
        const currentBlock = await logseq.Editor.getCurrentBlock();
        if (!currentBlock) return;

        const currentBlockUUID = currentBlock.uuid;
        const currentContent = currentBlock.content;
        const contextBlockUUIDs = context?.blockUUIDs || [];

        // 检查是否需要刷新：
        // 1. 当前块UUID发生变化
        // 2. 当前块不在上下文块列表中
        // 3. 当前块内容发生变化（针对同一个块）
        const blockChanged = lastCheckedBlockUUID !== currentBlockUUID;
        const blockNotInContext = !contextBlockUUIDs.includes(currentBlockUUID);
        const contentChanged = lastCheckedBlockUUID === currentBlockUUID &&
                              lastCheckedContent !== currentContent;

        if (blockChanged || blockNotInContext || contentChanged) {
          console.log('检测到变化，自动刷新上下文:', {
            blockChanged,
            blockNotInContext,
            contentChanged,
            currentBlockUUID,
            contextBlockUUIDs
          });

          await handleRefreshContext(true);

          // 更新检查状态
          lastCheckedBlockUUID = currentBlockUUID;
          lastCheckedContent = currentContent;
        } else {
          // 更新检查状态但不刷新
          lastCheckedBlockUUID = currentBlockUUID;
          lastCheckedContent = currentContent;
        }
      } catch (error) {
        console.error('自动刷新检查失败:', error);
      }
    };

    // 每1.5秒检查一次，提高响应速度
    const interval = setInterval(checkAndRefreshContext, 1500);

    // 立即执行一次检查
    checkAndRefreshContext();

    return () => {
      clearInterval(interval);
    };
  }, [autoRefreshEnabled, context, onRefreshContext]);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果正在输入或处于加载状态，不处理快捷键
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        isLoading
      ) {
        return;
      }

      // 'r' 键打开阅读模式
      if (e.key === 'r' && aiResponse && !isReadingMode) {
        e.preventDefault();
        handleOpenReadingMode();
      }

      // ESC 键关闭阅读模式
      if (e.key === 'Escape' && isReadingMode) {
        e.preventDefault();
        handleCloseReadingMode();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [aiResponse, isLoading, isReadingMode]);

  // 处理提示选择变化
  const handlePromptChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newPrompt = e.target.value;
    setSelectedPrompt(newPrompt);

    // 保存选择的提示词到设置
    if (settings) {
      try {
        console.log("Saving lastSelectedPrompt:", newPrompt);
        // 只更新 lastSelectedPrompt 字段
        await updateSettings({ lastSelectedPrompt: newPrompt });
        console.log("lastSelectedPrompt saved successfully");
      } catch (error) {
        console.error("Failed to save lastSelectedPrompt:", error);
      }
    }

    // 如果选择了预设提示词（非默认和自定义），且有问题内容，自动回答
    if (newPrompt !== 'default' && newPrompt !== 'custom') {
      const questionContent = currentQuestion || context?.content;
      if (questionContent && !isLoading) {
        // 短暂延迟确保状态已更新
        setTimeout(() => {
          handleSendMessage();
        }, 100);
      }
    }
  };

  // 处理自定义提示变化
  const handleCustomPromptChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setCustomPrompt(inputValue);

    // 匹配提示词
    if (settings && inputValue.trim() !== '') {
      const matchedItems = settings.customPrompts.filter(
        prompt => prompt.name.toLowerCase().includes(inputValue.toLowerCase())
      );
      setMatchedPrompts(matchedItems);
      setShowSuggestions(matchedItems.length > 0);
      setSelectedSuggestionIndex(-1);
    } else {
      setMatchedPrompts([]);
      setShowSuggestions(false);
    }
  };

  // 处理提示词建议选择
  const handleSuggestionSelect = async (promptName: string, promptContent: string) => {
    // 设置视觉反馈，显示正在使用的提示词名称
    const promptDisplay = `使用提示词: ${promptName}`;
    setCustomPrompt(promptDisplay);
    setSelectedPrompt('custom');
    setShowSuggestions(false);

    // 保存选择的提示词到设置
    if (settings) {
      try {
        console.log("Saving lastSelectedPrompt: 'custom'");
        // 只更新 lastSelectedPrompt 字段
        await updateSettings({ lastSelectedPrompt: 'custom' });
        console.log("lastSelectedPrompt saved successfully");
      } catch (error) {
        console.error("Failed to save lastSelectedPrompt:", error);
      }
    }

    // 自动发送消息，不需要用户手动点击发送
    setTimeout(() => {
      handleSendMessage(promptContent);
    }, 100);
  };

  // 处理键盘导航提示词建议
  const handlePromptKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 处理ESC键 - 隐藏提示词建议
    if (e.key === 'Escape') {
      setShowSuggestions(false);
      return;
    }

    // 如果没有显示提示词建议，则处理回车键发送消息
    if (!showSuggestions) {
      if (e.key === 'Enter' && customPrompt.trim() && !isLoading) {
        e.preventDefault();
        handleSendMessage();
      }
      return;
    }

    // 处理上下键导航提示词建议
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev =>
        prev < matchedPrompts.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => (prev > 0 ? prev - 1 : 0));
    }
    // 处理回车键选择提示词建议
    else if (e.key === 'Enter' && selectedSuggestionIndex >= 0) {
      e.preventDefault();
      const selectedPrompt = matchedPrompts[selectedSuggestionIndex];
      handleSuggestionSelect(selectedPrompt.name, selectedPrompt.prompt);
    }
  };

  // 处理自定义提示发送
  const handleCustomPromptSend = () => {
    if (customPrompt.trim() && !isLoading) {
      handleSendMessage();
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && customPrompt.trim() && !isLoading && !showSuggestions) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 处理发送消息
  const handleSendMessage = async (promptOverride?: string) => {
    if (isLoading) return;

    try {
      // 重置可能的错误状态
      setError('');
      setIsLoading(true);

      // 构建提示词
      let prompt = '';
      if (promptOverride) {
        prompt = promptOverride;
      } else if (selectedPrompt === 'custom' && customPrompt) {
        prompt = customPrompt;
      } else if (selectedPrompt === 'default') {
        // 使用默认提示词
        prompt = '你是一个有用的助手。';
      } else {
        const selectedPromptObj = settings?.customPrompts.find(p => p.name === selectedPrompt);
        if (selectedPromptObj) {
          prompt = selectedPromptObj.prompt;
        } else {
          prompt = '你是一个有用的助手。';
        }
      }

      // 如果提示词为空，使用默认提示词
      if (!prompt) {
        prompt = '你是一个有用的助手。';
      }

      // 保存当前提示词用于重新生成
      setCurrentQuestion(prompt);

      // 添加问题到聊天历史
      setChatHistory(prev => [
        ...prev,
        {
          type: 'question',
          content: prompt,
          timestamp: Date.now()
        }
      ]);

      // 重置响应状态
      setStreamPosition(0);
      setAiResponse('');

      // 获取当前API配置
      const apiConfig = currentApiConfig || await getCurrentApiConfig();
      if (!apiConfig) {
        throw new Error('无法获取API配置');
      }

      // 设置响应处理函数
      const responseHandlers: ApiResponseHandlers = {
        onChunk: (chunk: string) => {
          // 流式响应的处理
          console.log('接收到分片:', chunk);
          setAiResponse(prev => {
            let newResponse;
            if (!prev) {
              newResponse = chunk;
            } else {
              newResponse = prev + chunk;
            }
            return newResponse;
          });
        },
        onComplete: (fullResponse: string) => {
          console.log('响应完成');
          setIsLoading(false);

          // 添加回答到聊天历史
          setChatHistory(prev => [
            ...prev,
            {
              type: 'answer',
              content: fullResponse,
              timestamp: Date.now()
            }
          ]);

          // 保存最近选择的提示词
          if (selectedPrompt && settings) {
            updateSettings({
              ...settings,
              lastSelectedPrompt: selectedPrompt
            });
          }
        },
        onError: (errorMsg: string) => {
          console.error('接收响应出错:', errorMsg);
          setError(`请求失败: ${errorMsg}`);
          setIsLoading(false);
        }
      };

      // 发送请求到 API - 使用系统提示作为第四个参数
      await sendChatRequest(prompt, context, responseHandlers, prompt);
    } catch (error) {
      console.error('发送消息出错:', error);
      setError(`发送消息失败: ${error instanceof Error ? error.message : String(error)}`);
      setIsLoading(false);
    }
  };

  // 处理重新回答
  const handleRegenerate = async () => {
    if (!currentQuestion || isLoading) {
      return;
    }

    // 显示正在重新回答的提示
    setError('正在重新生成回答...');

    // 获取当前使用的提示词
    let systemPrompt: string | undefined;

    if (selectedPrompt === 'custom') {
      systemPrompt = customPrompt;
    } else if (selectedPrompt !== 'default' && settings) {
      const selectedPromptObj = settings.customPrompts.find(p => p.name === selectedPrompt);
      if (selectedPromptObj) {
        systemPrompt = selectedPromptObj.prompt;
      }
    }

    // 添加一个小提示让AI知道这是重新回答的请求
    if (systemPrompt) {
      systemPrompt = `${systemPrompt}\n\n注意：请重新思考并给出另一个角度的回答。`;
    } else {
      systemPrompt = '请重新思考并给出另一个角度的回答。';
    }

    // 调用发送消息函数，使用当前问题和修改后的提示词
    handleSendMessage(systemPrompt);
  };

  // 处理追问
  const handleFollowUp = (selectedText?: string) => {
    if (!aiResponse || isLoading) {
      return;
    }

    // 进入追问模式
    setIsFollowUpMode(true);

    // 如果有选中的文本，自动填充到追问输入框
    if (selectedText) {
      // 如果选中文本较长，自动添加引号并加上提问前缀
      if (selectedText.length > 50) {
        setFollowUpQuestion(`关于"${selectedText.substring(0, 50)}..."，能详细解释一下吗？`);
      } else {
        setFollowUpQuestion(`关于"${selectedText}"，能详细解释一下吗？`);
      }
    }

    // 聚焦到追问输入框
    setTimeout(() => {
      followUpInputRef.current?.focus();
    }, 50);
  };

  // 处理追问输入变化
  const handleFollowUpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFollowUpQuestion(e.target.value);
  };

  // 处理发送追问
  const handleSendFollowUp = async () => {
    if (!followUpQuestion.trim() || isLoading) return;

    // 保存之前的响应内容
    const previousResponse = aiResponse;

    // 准备追问环境
    setIsLoading(true);

    // 创建追问的上下文
    const followUpContext = `${followUpQuestion}\n\n上下文：\n${previousResponse}`;

    // 将原来的回答和追问内容组合在一起，使用分割线
    // 使用更明显的分割线和格式化，确保追问和回答清晰可见
    const combinedResponse = `${previousResponse}\n\n---\n\n**追问：**${followUpQuestion}\n\n**回答：**_正在生成回答..._`;

    // 更新当前显示的回答，将追问添加到原回答下方
    setAiResponse(combinedResponse);

    // 添加追问到聊天历史
    setChatHistory(prev => [
      ...prev,
      {
        type: 'followup',
        content: followUpQuestion,
        timestamp: Date.now()
      }
    ]);

    // 保存当前问题内容，用于后续重新回答
    setCurrentQuestion(followUpContext);

    // 重置追问模式
    setIsFollowUpMode(false);
    setFollowUpQuestion('');

    // 获取当前使用的提示词
    let systemPrompt: string | undefined;

    if (selectedPrompt === 'custom') {
      systemPrompt = customPrompt;
    } else if (selectedPrompt !== 'default' && settings) {
      const selectedPromptObj = settings.customPrompts.find(p => p.name === selectedPrompt);
      if (selectedPromptObj) {
        systemPrompt = selectedPromptObj.prompt;
      }
    }

    // 添加一个小提示让AI知道这是追问
    // 检查追问内容是否包含引号，如果包含则可能是基于阅读内容的追问
    const isContentBasedQuestion = followUpQuestion.includes('"') || followUpQuestion.includes('"');

    if (systemPrompt) {
      if (isContentBasedQuestion) {
        systemPrompt = `${systemPrompt}\n\n注意：这是一个基于阅读内容的追问，请重点关注引号中的内容进行回答。`;
      } else {
        systemPrompt = `${systemPrompt}\n\n注意：这是一个追问，请基于之前的对话继续回答。`;
      }
    } else {
      if (isContentBasedQuestion) {
        systemPrompt = '这是一个基于阅读内容的追问，请重点关注引号中的内容进行回答。';
      } else {
        systemPrompt = '这是一个追问，请基于之前的对话继续回答。';
      }
    }

    try {
      // 获取追问模型
      let followUpModel: string | undefined;

      // 使用追问对话框中选择的模型
      if (followupModelId && apiConfigs.length > 0) {
        const selectedConfig = apiConfigs.find(config => config.id === followupModelId);
        if (selectedConfig) {
          followUpModel = selectedConfig.modelName;
          console.log('使用追问对话框选择的模型:', followUpModel);
        }
      }
      // 如果没有选择模型，则使用当前 API 配置的追问模型
      else if (currentApiConfig) {
        followUpModel = currentApiConfig.followUpModelName;
        console.log('使用默认追问模型:', followUpModel || '默认模型');
      }

      // 定义响应处理函数特别针对追问
      const responseHandlers: ApiResponseHandlers = {
        onChunk: (chunk: string) => {
          setAiResponse(prev => {
            // 追问模式，替换"正在生成回答..."
            if (prev.includes('_正在生成回答..._')) {
              return prev.replace('_正在生成回答..._', chunk);
            } else if (prev.includes('**回答：**')) {
              const answerMarkerPos = prev.lastIndexOf('**回答：**');
              const answerStartPos = answerMarkerPos + '**回答：**'.length;
              const beforeAnswer = prev.substring(0, answerStartPos);
              const existingAnswer = prev.substring(answerStartPos);
              return beforeAnswer + existingAnswer + chunk;
            } else {
              return prev + chunk;
            }
          });
        },
        onComplete: (fullResponse: string) => {
          setIsLoading(false);

          // 提取生成的回答部分
          let finalResponse = fullResponse;
          const combinedResponse = aiResponse;

          if (combinedResponse.includes('**回答：**')) {
            const answerMarkerPos = combinedResponse.lastIndexOf('**回答：**');
            const answerStartPos = answerMarkerPos + '**回答：**'.length;
            finalResponse = combinedResponse.substring(answerStartPos).replace('_正在生成回答..._', '');
          }

          // 添加回答到聊天历史
          setChatHistory(prev => [
            ...prev,
            {
              type: 'followup-answer',
              content: finalResponse.trim(),
              timestamp: Date.now()
            }
          ]);
        },
        onError: (errorMsg: string) => {
          console.error('接收追问响应出错:', errorMsg);
          setError(`追问失败: ${errorMsg}`);
          setIsLoading(false);
        }
      };

      // 发送请求，传入追问模型覆盖默认模型
      // 使用选定的追问模型配置ID
      await sendChatRequest(followUpContext, context, responseHandlers, systemPrompt, followUpModel, followupModelId);

    } catch (error) {
      console.error('发送追问出错:', error);
      setError(`追问失败: ${error instanceof Error ? error.message : String(error)}`);
      setIsLoading(false);
    }
  };

  // 处理取消追问
  const handleCancelFollowUp = () => {
    setIsFollowUpMode(false);
    setFollowUpQuestion('');
  };

  // 处理追问键盘事件
  const handleFollowUpKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && followUpQuestion.trim() && !isLoading) {
      e.preventDefault();
      handleSendFollowUp();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelFollowUp();
    }
  };

  // 处理重发追问
  const handleResendFollowUp = (followUpContent: string) => {
    if (isLoading) return;

    // 设置追问内容并触发发送
    setFollowUpQuestion(followUpContent);
    setIsFollowUpMode(true);

    // 使用setTimeout确保状态更新后再发送
    setTimeout(() => {
      if (followUpInputRef.current) {
        followUpInputRef.current.focus();
        // 自动触发发送
        handleSendFollowUp();
      }
    }, 50);
  };

  // 处理打开阅读模式
  const handleOpenReadingMode = () => {
    if (!aiResponse) return;
    setIsReadingMode(true);
  };

  // 处理关闭阅读模式
  const handleCloseReadingMode = () => {
    setIsReadingMode(false);
    // 同时关闭右键菜单
    setContextMenu({ ...contextMenu, show: false });
  };

  // 处理右键菜单打开
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    console.log("右键菜单事件触发", { isReadingMode, hasResponse: !!aiResponse });

    // 始终显示右键菜单，不再限制只在阅读模式下
    if (aiResponse) {
      // 获取选中的文本
      const selection = window.getSelection();
      const selectedText = selection ? selection.toString() : '';

      console.log("选中的文本:", selectedText);

      // 计算菜单位置，确保不超出视口
      const x = Math.min(e.clientX, window.innerWidth - 200); // 200是菜单的估计宽度
      const y = Math.min(e.clientY, window.innerHeight - 300); // 300是菜单的估计高度

      console.log("设置右键菜单位置", { x, y });

      setContextMenu({
        x,
        y,
        show: true,
        selectedText,
      });

      // 只在选中文本时显示提示，不再显示右键菜单已显示的提示
      if (selectedText) {
        logseq.UI.showMsg("已选中部分内容，可以进行操作", "info");
      }
    }
  };

  // 处理右键菜单关闭
  const handleCloseContextMenu = () => {
    setContextMenu({ ...contextMenu, show: false });
  };

  // 处理刷新上下文
  const handleRefreshContext = async (isAutoRefresh = false) => {
    if (!onRefreshContext) return;

    try {
      setError('');
      if (isAutoRefresh) {
        setIsAutoRefreshing(true);
      }
      await onRefreshContext();
      if (!isAutoRefresh) {
        logseq.UI.showMsg("上下文已刷新", "success");
      }
    } catch (error) {
      console.error('刷新上下文失败:', error);
      if (!isAutoRefresh) {
        setError('刷新上下文失败，请重试');
        logseq.UI.showMsg("刷新上下文失败", "error");
      }
    } finally {
      if (isAutoRefresh) {
        setIsAutoRefreshing(false);
      }
    }
  };

  // 切换自动刷新
  const toggleAutoRefresh = async () => {
    const newAutoRefreshState = !autoRefreshEnabled;
    setAutoRefreshEnabled(newAutoRefreshState);

    // 保存状态到设置中
    try {
      if (settings) {
        const updatedSettings = {
          ...settings,
          uiSettings: {
            ...settings.uiSettings,
            autoRefreshContext: newAutoRefreshState
          }
        };
        await updateSettings(updatedSettings);
        console.log('自动刷新状态已保存:', newAutoRefreshState);
      }
    } catch (error) {
      console.error('保存自动刷新状态失败:', error);
    }

    // 显示用户反馈
    if (newAutoRefreshState) {
      logseq.UI.showMsg("已启用自动刷新上下文", "success");
    } else {
      logseq.UI.showMsg("已关闭自动刷新上下文", "info");
    }
  };

  // 处理点击文档其他区域关闭右键菜单
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (contextMenu.show) {
        const target = e.target as HTMLElement;
        if (!target.closest('.context-menu')) {
          setContextMenu({ ...contextMenu, show: false });
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [contextMenu]);

  // 渲染流式响应
  const renderStreamingResponse = () => {
    return (
      <div
        ref={responseRef}
        className="p-3 max-h-[350px] overflow-y-auto rounded-lg bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 mb-10 ai-response typewriter-text relative cursor-pointer"
        style={{ paddingBottom: '40px' }} // 为悬浮按钮留出空间
        onClick={handleOpenReadingMode} // 点击内容区域直接触发阅读模式
        title="点击打开阅读模式"
      >
        {aiResponse ? (
          <div className="relative">
            {aiResponse}
            {isLoading && <span className="blinking-cursor" />}
            <div className="reading-hint">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '4px' }}>
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              点击阅读
            </div>
          </div>
        ) : (
          <span className="text-gray-500 dark:text-gray-400 italic">
            {isLoading ? '思考中...' : '响应将显示在这里'}
          </span>
        )}
      </div>
    );
  };

  // 渲染提示选项
  const renderPromptOptions = () => {
    return (
      <select
        value={selectedPrompt}
        onChange={handlePromptChange}
        className="apple-input apple-select w-full p-2 mb-2 text-gray-700 dark:text-gray-200"
      >
        <option value="default">默认提示词</option>
        <option value="custom">自定义提示词</option>
        {settings?.customPrompts.map((prompt, index) => (
          <option key={index} value={prompt.name}>
            {prompt.name}
          </option>
        ))}
      </select>
    );
  };

  // 渲染提示词建议
  const renderPromptSuggestions = () => {
    if (!showSuggestions || matchedPrompts.length === 0) return null;

    return (
      <div
        ref={suggestionsRef}
        className="absolute top-full left-0 right-0 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden mt-1"
      >
        {matchedPrompts.map((prompt, index) => (
          <div
            key={index}
            className={`p-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${
              index === selectedSuggestionIndex ? 'bg-blue-50 dark:bg-blue-900' : ''
            }`}
            onClick={() => handleSuggestionSelect(prompt.name, prompt.prompt)}
          >
            <div className="font-medium text-gray-800 dark:text-gray-200">{prompt.name}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{prompt.prompt}</div>
          </div>
        ))}
      </div>
    );
  };

  // 处理替换原文
  const handleReplace = async (responseText: string) => {
    try {
      console.log('开始替换原文，响应文本长度:', responseText.length);

      // 格式化响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(responseText);
      console.log('格式化后的块数量:', formattedBlocks.length);
      console.log('格式化后的块内容示例:', formattedBlocks.slice(0, 2));
      console.log('当前上下文块UUID数量:', context.blockUUIDs.length);
      console.log('当前上下文块UUID示例:', context.blockUUIDs[0]);

      if (formattedBlocks.length === 0 || context.blockUUIDs.length === 0) {
        const errorMsg = '无法替换原文：无有效块数据或无原始块 UUID';
        console.error(errorMsg);
        setError(errorMsg);
        return;
      }

      // 执行替换操作
      console.log('调用onReplace回调函数');
      onReplace(responseText);
      console.log('替换原文操作完成');
    } catch (error) {
      console.error('替换原文出错:', error);
      setError('替换原文失败，请重试');
    }
  };

  // 处理插入子块
  const handleInsert = async (responseText: string) => {
    try {
      console.log('开始插入子块，响应文本长度:', responseText.length);
      console.log('插入模式:', insertAsSibling ? '同级插入' : '子块插入');

      // 格式化响应为 Logseq 块格式
      const formattedBlocks = formatResponseToBlocks(responseText);
      console.log('格式化后的块数量:', formattedBlocks.length);
      console.log('格式化后的块内容示例:', formattedBlocks.slice(0, 2));
      console.log('当前上下文块UUID数量:', context.blockUUIDs.length);
      console.log('当前上下文块UUID示例:', context.blockUUIDs[0]);
      console.log('当前上下文类型:', context.type);

      if (formattedBlocks.length === 0 || context.blockUUIDs.length === 0) {
        const errorMsg = '无法插入子块：无有效块数据或无原始块 UUID';
        console.error(errorMsg);
        setError(errorMsg);
        return;
      }

      // 创建一个新的上下文对象，使用深拷贝以避免原始对象被修改
      const contextWithMode = JSON.parse(JSON.stringify(context));

      // 强制设置插入模式为布尔值，确保类型一致性
      contextWithMode.isInsertAsSibling = insertAsSibling === true;

      // 输出更多debug信息
      console.log('修改后的上下文对象:', contextWithMode);
      console.log('isInsertAsSibling值:', insertAsSibling);
      console.log('isInsertAsSibling类型:', typeof insertAsSibling);
      console.log('强制转换后的isInsertAsSibling值:', contextWithMode.isInsertAsSibling);
      console.log('强制转换后的isInsertAsSibling类型:', typeof contextWithMode.isInsertAsSibling);

      // 执行插入操作
      console.log('调用onInsert回调函数, 使用模式:', insertAsSibling ? '同级插入' : '子块插入');

      // 使用类型安全的方式替换context对象
      // 先清除原始对象的所有属性
      context.type = contextWithMode.type;
      context.content = contextWithMode.content;
      context.blockUUIDs = [...contextWithMode.blockUUIDs];

      // 复制可选属性
      if (contextWithMode.pageTitle !== undefined) {
        context.pageTitle = contextWithMode.pageTitle;
      }

      if (contextWithMode.theme !== undefined) {
        context.theme = contextWithMode.theme;
      }

      // 确保isInsertAsSibling是布尔值
      context.isInsertAsSibling = contextWithMode.isInsertAsSibling === true;
      // 使用App.tsx中的handleInsert函数
      onInsert(responseText);
      console.log('插入操作完成');
    } catch (error) {
      console.error('插入子块出错:', error);
      setError('插入失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 切换插入模式
  const toggleInsertMode = () => {
    const newMode = !insertAsSibling;
    setInsertAsSibling(newMode);
    setError(`已切换到${newMode ? '同级' : '子块'}插入模式`);
    setTimeout(() => setError(''), 2000);
  };

  // 追问按钮渲染
  const renderFollowUpButton = () => {
    if (!aiResponse || isLoading) return null;

    // 获取当前API配置的追问模型
    const followUpModel = currentApiConfig?.followUpModelName
      ? (currentApiConfig.followUpModelName !== currentApiConfig.modelName
          ? `${currentApiConfig.followUpModelName}`
          : null)
      : null;

    return (
      <div className="relative">
        <button
          onClick={() => handleFollowUp()}
          className="flex items-center space-x-1 py-1 px-2 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-300 rounded text-sm"
          title="发送追问"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
          <span className="whitespace-nowrap">
            追问
            {followUpModel && (
              <span className="ml-1 text-xs opacity-70">({followUpModel})</span>
            )}
          </span>
        </button>
      </div>
    );
  };

  // 渲染模型选择器
  const renderModelSelector = () => {
    if (isReadingMode) return null;

    return (
      <div className="absolute bottom-[40px] left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-2 flex justify-between items-center text-sm">
        <div className="flex items-center space-x-2 w-full">
          <label className="text-xs text-gray-500 dark:text-gray-400">模型:</label>
          <select
            value={currentApiConfigId}
            onChange={(e) => {
              const newConfigId = e.target.value;
              setCurrentApiConfigId(newConfigId);

              // 更新当前API配置
              const selectedConfig = apiConfigs.find(config => config.id === newConfigId);
              if (selectedConfig) {
                setCurrentApiConfig(selectedConfig);

                // 保存选择到设置
                if (settings) {
                  updateSettings({
                    ...settings,
                    currentApiConfigId: newConfigId
                  });
                }

                // 如果已有响应，自动重新生成
                if (aiResponse && !isLoading) {
                  setTimeout(() => {
                    handleRegenerate();
                  }, 100);
                }
              }
            }}
            className="apple-input apple-select flex-grow p-1 text-sm text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
          >
            {apiConfigs.length > 0 ? (
              apiConfigs.map((config) => (
                <option key={config.id} value={config.id}>
                  {config.name} ({config.modelName})
                </option>
              ))
            ) : (
              <option value="">未找到API配置</option>
            )}
          </select>
        </div>
      </div>
    );
  };

  // 渲染工具栏
  const renderToolbar = () => {
    if (!showToolbar || isReadingMode) return null;

    return (
      <div
        ref={toolbarRef}
        className="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-2 flex justify-between items-center text-sm"
      >
        <div className="flex space-x-2">
          {/* 清除按钮 */}
          <button
            onClick={() => {
              setAiResponse('');
              setError('');
              setChatHistory([]);
            }}
            className="toolbar-button flex items-center space-x-1"
            title="清除对话"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>

          {/* 设置按钮 */}
          <button
            onClick={() => {
              // 打开设置
              logseq.showSettingsUI();
            }}
            className="toolbar-button flex items-center space-x-1"
            title="设置"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>

          {/* 格式化按钮 */}
          {aiResponse && (
            <button
              onClick={() => {
                // 格式化响应内容
                try {
                  // 简单的格式化，可以根据需要扩展
                  const formattedResponse = aiResponse
                    .replace(/\n\n/g, '\n') // 减少多余的空行
                    .replace(/\n\n\n+/g, '\n\n') // 最多保留一个空行
                    .trim();
                  setAiResponse(formattedResponse);
                  setError('内容已格式化');
                  setTimeout(() => setError(''), 2000);
                } catch (error) {
                  console.error('格式化失败:', error);
                  setError('格式化失败');
                  setTimeout(() => setError(''), 2000);
                }
              }}
              className="toolbar-button flex items-center space-x-1"
              title="格式化内容"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
              </svg>
            </button>
          )}
        </div>

        <div className="flex space-x-2">
          {/* 阅读模式按钮 */}
          {aiResponse && (
            <button
              onClick={handleOpenReadingMode}
              className="toolbar-button flex items-center space-x-1"
              title="阅读模式"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </button>
          )}

          {/* 重新生成按钮 */}
          {aiResponse && !isLoading && (
            <button
              onClick={handleRegenerate}
              className="toolbar-button flex items-center space-x-1"
              title="重新生成"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          )}

          {/* 追问按钮已移除，只在阅读模式中使用 */}

          {/* 复制按钮 */}
          {aiResponse && (
            <button
              onClick={() => {
                navigator.clipboard.writeText(aiResponse);
                setError('已复制到剪贴板');
                setTimeout(() => setError(''), 2000);
              }}
              className="toolbar-button flex items-center space-x-1"
              title="复制内容"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
              </svg>
            </button>
          )}

          {/* 替换按钮 */}
          {aiResponse && (
            <button
              onClick={() => handleReplace(aiResponse)}
              className="toolbar-button flex items-center space-x-1"
              title="替换选中内容"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
              </svg>
            </button>
          )}

          {/* 切换插入模式按钮 */}
          {aiResponse && (
            <button
              onClick={toggleInsertMode}
              className={`toolbar-button flex items-center space-x-1 ${insertAsSibling ? 'bg-blue-100 dark:bg-blue-900' : ''}`}
              title={insertAsSibling ? "切换为子块插入模式" : "切换为同级插入模式"}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {insertAsSibling ? (
                  // 同级插入图标 - 水平箭头
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                ) : (
                  // 子块插入图标 - 嵌套箭头
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7M5 12h14" />
                )}
              </svg>
            </button>
          )}

          {/* 插入按钮 */}
          {aiResponse && (
            <button
              onClick={() => handleInsert(aiResponse)}
              className="toolbar-button flex items-center space-x-1"
              title={insertAsSibling ? "作为同级块插入" : "作为子块插入"}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          )}
        </div>
      </div>
    );
  };

  // 渲染主要UI
  return (
    <div
      ref={modalRef}
      className={`apple-modal z-50 ${isDetached ? 'detached-window' : 'fixed top-1/4 left-1/2 transform -translate-x-1/2'} ${currentTheme !== 'system' ? `theme-${currentTheme}` : ''}`}
      style={{
        width: `${windowSize.width}px`,
        height: `${windowSize.height}px`,
        ...(isDetached ? { left: `${windowPosition.x}px`, top: `${windowPosition.y}px` } : {})
      }}
      onMouseDown={handleMouseDown}
    >
      {/* 标题栏 */}
      <div
        ref={titlebarRef}
        className="titlebar apple-titlebar cursor-move flex justify-between items-center p-2 header-gradient rounded-t-lg"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center space-x-2">
          <span className="text-base font-medium text-gray-800 dark:text-gray-100">
            AI 聊天助手 <span className="text-xs font-semibold bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300 px-1.5 py-0.5 rounded-md ml-1">v{version}</span>
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* 切换分离模式 */}
          <button
            className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            onClick={toggleDetachedMode}
            title={isDetached ? "重新嵌入窗口" : "分离为独立窗口"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              {isDetached ? (
                // 嵌入图标
                <path d="M9 3v18"></path>
              ) : (
                // 分离图标
                <>
                  <line x1="15" y1="3" x2="15" y2="9"></line>
                  <line x1="9" y1="15" x2="15" y2="9"></line>
                </>
              )}
            </svg>
          </button>

          {/* 关闭按钮 */}
          <button
            className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            onClick={onClose}
            title="关闭"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <div className="p-4 overflow-y-auto" style={{ height: 'calc(100% - 100px)' }}>

      {/* 上下文显示 */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-1">
          <div className="flex items-center gap-2">
            <p className="text-sm text-gray-500 dark:text-gray-400">上下文:</p>
            {autoRefreshEnabled && (
              <span className="text-xs bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300 px-2 py-0.5 rounded-full flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                自动刷新
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {context?.type === 'smart' && (
              <span className="text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-0.5 rounded-full">
                上下文
              </span>
            )}
            {onRefreshContext && (
              <div className="flex items-center gap-1">
                {/* 自动刷新切换按钮 */}
                <button
                  onClick={toggleAutoRefresh}
                  className={`p-1 rounded transition-colors ${
                    autoRefreshEnabled
                      ? 'bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300'
                      : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                  }`}
                  title={autoRefreshEnabled ? "关闭自动刷新" : "启用自动刷新"}
                  disabled={isLoading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 2v6l3-3-3-3zM12 18v6l3-3-3-3zM4.93 4.93l4.24 4.24-1.41 1.42L4.93 7.76l-1.42 1.41zM16.24 16.24l4.24 4.24-1.42 1.42-2.83-2.83-1.41 1.41z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                </button>

                {/* 手动刷新按钮 */}
                <button
                  onClick={() => handleRefreshContext(false)}
                  className={`p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors ${
                    isAutoRefreshing ? 'animate-spin' : ''
                  }`}
                  title="手动刷新上下文"
                  disabled={isLoading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"></path>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 页面标题和主题信息 */}
        {(context?.pageTitle || context?.theme) && (
          <div className="flex flex-wrap gap-2 mb-2">
            {context.pageTitle && (
              <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                页面: {context.pageTitle}
              </span>
            )}
            {context.theme && (
              <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                主题: {context.theme}
              </span>
            )}
          </div>
        )}

        {/* 上下文内容 */}
        <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded text-gray-800 dark:text-gray-200 text-sm max-h-[100px] overflow-y-auto">
          {context?.content || "无上下文"}
        </div>

        {/* 块数量信息 */}
        {context?.blockUUIDs && context.blockUUIDs.length > 0 && (
          <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            包含 {context.blockUUIDs.length} 个块
          </div>
        )}
      </div>

      {/* 提示选择 */}
      <div className="mb-4">
        {renderPromptOptions()}

        {/* 自定义提示输入 */}
        {showCustomInput && (
          <div className="relative">
            <div className="flex items-center">
              <input
                ref={promptInputRef}
                type="text"
                value={customPrompt}
                onChange={handleCustomPromptChange}
                onKeyDown={handlePromptKeyDown}
                placeholder="输入提示或搜索已保存的提示词..."
                className="apple-input w-full p-2 text-gray-700 dark:text-gray-200"
              />
              <button
                onClick={handleCustomPromptSend}
                disabled={isLoading || !customPrompt.trim()}
                className={`ml-2 apple-button px-4 py-2 ${
                  isLoading || !customPrompt.trim()
                    ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                } rounded transition-colors`}
              >
                发送
              </button>
            </div>
            {renderPromptSuggestions()}
          </div>
        )}

      </div>

      {/* AI响应区域 */}
      {renderStreamingResponse()}

      {/* 错误显示 */}
      {error && (
        <div className="mb-4 p-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded">
          {error}
        </div>
      )}

      </div>

      {/* 底部操作区 */}
      <div className="floating-actions">
        {/* 模型选择器 */}
        {renderModelSelector()}

        {/* 工具栏 */}
        {renderToolbar()}
      </div>

      {/* 可调整大小的手柄 */}
      <div className="resize-handle"></div>

      {/* 阅读模式 - 使用Portal确保正确渲染在最上层 */}
      {isReadingMode && (
        <div
          className="reading-mode-overlay"
          onClick={(e) => {
            // 点击背景关闭
            if (e.target === e.currentTarget) {
              handleCloseReadingMode();
            }
          }}
        >
          <div className="reading-mode-container">
            <div className="reading-mode-header">
              <div className="reading-mode-title">阅读内容</div>
              <div
                className="reading-mode-close"
                onClick={handleCloseReadingMode}
                role="button"
                tabIndex={0}
                aria-label="关闭"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </div>
            </div>

            <div
              className="reading-mode-content"
              title="右键点击显示操作菜单"
            >
              <div className="reading-mode-content-tip">
                选中部分内容后右键点击可操作选中内容，或右键点击操作全部内容
              </div>
              <div
                className="reading-mode-content-inner"
                onContextMenu={handleContextMenu}
                style={{ minHeight: '100%', maxHeight: '70vh', overflow: 'auto' }}
              >
                {chatHistory.length > 0 ? (
                  <div className="chat-history-container">
                    {chatHistory.map((item, index) => {
                      // 根据类型渲染不同的历史项
                      switch (item.type) {
                        case 'question':
                          return (
                            <div key={`${item.type}-${index}`} className="history-item question-item">
                              <div className="history-item-header">问题:</div>
                              <div className="history-item-content">{item.content}</div>
                              {index < chatHistory.length - 1 &&
                                chatHistory[index + 1].type === 'answer' &&
                                <hr className="history-divider" />}
                            </div>
                          );
                        case 'answer':
                          return (
                            <div key={`${item.type}-${index}`} className="history-item answer-item">
                              <div className="history-item-header">回答:</div>
                              <div className="history-item-content">{item.content}</div>
                              {index < chatHistory.length - 1 &&
                                chatHistory[index + 1].type === 'followup' &&
                                <hr className="history-divider" />}
                            </div>
                          );
                        case 'followup':
                          return (
                            <div key={`${item.type}-${index}`} className="history-item followup-item">
                              <div className="history-item-header flex justify-between items-center">
                                <span>追问:</span>
                                <button
                                  className="refresh-button p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
                                  onClick={() => handleResendFollowUp(item.content)}
                                  title="重新发送这个追问"
                                  disabled={isLoading}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"></path>
                                  </svg>
                                </button>
                              </div>
                              <div className="history-item-content">{item.content}</div>
                              {index < chatHistory.length - 1 &&
                                chatHistory[index + 1].type === 'followup-answer' &&
                                <hr className="history-divider" />}
                            </div>
                          );
                        case 'followup-answer':
                          return (
                            <div key={`${item.type}-${index}`} className="history-item followup-answer-item">
                              <div className="history-item-header">回答:</div>
                              <div className="history-item-content">{item.content}</div>
                              {index < chatHistory.length - 1 &&
                                (chatHistory[index + 1].type === 'followup' || chatHistory[index + 1].type === 'question') &&
                                <hr className="history-divider" />}
                            </div>
                          );
                        default:
                          return null;
                      }
                    })}
                    {isLoading && (
                      <div className="history-item loading-item">
                        <div className="history-item-content">思考中...</div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="no-history-message">
                    {aiResponse || (isLoading ? "正在加载内容..." : "无对话历史")}
                  </div>
                )}
              </div>
            </div>

            {/* 右键菜单 - 使用portal确保正确显示 */}
            {ReactDOM.createPortal(
              contextMenu.show && (
                <div
                  className="context-menu"
                  style={{
                    top: contextMenu.y,
                    left: contextMenu.x
                  }}
                >
                  <div
                    className={`context-menu-item ${!aiResponse || isLoading || isFollowUpMode ? 'disabled' : ''}`}
                    onClick={() => {
                      if (!aiResponse || isLoading || isFollowUpMode) return;
                      handleCloseContextMenu();
                      // 直接将选中文本传递给handleFollowUp函数
                      handleFollowUp(contextMenu.selectedText);
                    }}
                  >
                    <div className="context-menu-item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                      </svg>
                    </div>
                    追问
                  </div>

                  <div
                    className={`context-menu-item ${!aiResponse || isLoading || !currentQuestion ? 'disabled' : ''}`}
                    onClick={() => {
                      if (!aiResponse || isLoading || !currentQuestion) return;
                      handleCloseContextMenu();
                      handleRegenerate();
                    }}
                  >
                    <div className="context-menu-item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"></path>
                      </svg>
                    </div>
                    重新回答
                  </div>

                  <div
                    className={`context-menu-item ${!aiResponse || isLoading ? 'disabled' : ''}`}
                    onClick={() => {
                      if (!aiResponse || isLoading) return;
                      handleCloseContextMenu();
                      // 如果有选中文本，则只替换选中的部分
                      const textToReplace = contextMenu.selectedText || aiResponse;
                      handleReplace(textToReplace);
                      handleCloseReadingMode();
                    }}
                  >
                    <div className="context-menu-item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                      </svg>
                    </div>
                    {contextMenu.selectedText ? '替换选中内容' : '替换原文'}
                  </div>

                  <div
                    className={`context-menu-item ${!aiResponse || isLoading || !currentQuestion ? 'disabled' : ''}`}
                    onClick={() => {
                      if (!aiResponse || isLoading || !currentQuestion) return;
                      handleCloseContextMenu();
                      handleRegenerate();
                    }}
                  >
                    <div className="context-menu-item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="17 1 21 5 17 9"></polyline>
                        <path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
                        <polyline points="7 23 3 19 7 15"></polyline>
                        <path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
                      </svg>
                    </div>
                    {contextMenu.selectedText ? '重答' : '重答'}
                  </div>

                  <div
                    className={`context-menu-item ${!aiResponse || isLoading ? 'disabled' : ''}`}
                    onClick={() => {
                      if (!aiResponse || isLoading) return;
                      handleCloseContextMenu();
                      // 如果有选中文本，则只插入选中的部分
                      const textToInsert = contextMenu.selectedText || aiResponse;
                      handleInsert(textToInsert);
                      handleCloseReadingMode();
                    }}
                  >
                    <div className="context-menu-item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                    </div>
                    {contextMenu.selectedText ? '插入选中内容' : '插入子块'}
                  </div>

                  <div className="context-menu-divider"></div>

                  <div
                    className="context-menu-item"
                    onClick={() => {
                      handleCloseContextMenu();
                      // 如果有选中文本，则只复制选中的部分
                      if (contextMenu.selectedText || aiResponse) {
                        const textToCopy = contextMenu.selectedText || aiResponse;
                        navigator.clipboard.writeText(textToCopy);
                        logseq.UI.showMsg(contextMenu.selectedText ? '已复制选中内容' : '已复制全部内容', 'success');
                      }
                    }}
                  >
                    <div className="context-menu-item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                      </svg>
                    </div>
                    {contextMenu.selectedText ? '复制选中内容' : '复制全部内容'}
                  </div>
                </div>
              ),
              document.body
            )}

            {/* 追问输入区域 - 独立对话框 */}
            {ReactDOM.createPortal(
              isFollowUpMode && (
                <div className="followup-dialog-overlay" onClick={(e) => {
                  if (e.target === e.currentTarget) {
                    handleCancelFollowUp();
                  }
                }}>
                  <div className="followup-dialog bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-lg">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">追问问题</h3>
                      <button
                        onClick={handleCancelFollowUp}
                        className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                    </div>

                    <div className="flex-grow mb-3">
                      <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">输入追问内容</label>
                      <input
                        ref={followUpInputRef}
                        type="text"
                        value={followUpQuestion}
                        onChange={handleFollowUpChange}
                        onKeyDown={handleFollowUpKeyDown}
                        placeholder="输入你的追问..."
                        className="apple-input w-full p-2 text-gray-700 dark:text-gray-200"
                        autoFocus
                      />
                    </div>

                    {/* 模型选择下拉框 */}
                    <div className="mb-4">
                      <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">模型</label>
                      <select
                        value={followupModelId}
                        onChange={(e) => {
                          const newConfigId = e.target.value;
                          setFollowupModelId(newConfigId);

                          // 更新设置
                          const updateApiConfig = async () => {
                            try {
                              const settings = await getSettings();
                              const newSettings = {
                                ...settings,
                                followupModelId: newConfigId // 保存追问模型 ID
                              };

                              await updateSettings(newSettings);

                              // 获取选中的配置
                              const selectedConfig = settings.apiConfigs?.find(c => c.id === newConfigId);
                              if (selectedConfig) {
                                // 更新当前追问模型配置，但不改变主界面的模型
                                console.log('追问对话框选择了模型:', selectedConfig.name);
                              }
                            } catch (error) {
                              console.error('更新追问模型设置失败:', error);
                              setError('更新追问模型设置失败，请重试');
                            }
                          };

                          updateApiConfig();
                        }}
                        className="apple-input apple-select w-full p-2 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
                      >
                        {apiConfigs.length > 0 ? (
                          apiConfigs.map((config) => (
                            <option key={config.id} value={config.id}>
                              {config.name} ({config.modelName})
                            </option>
                          ))
                        ) : (
                          <option value="">未找到API配置</option>
                        )}
                      </select>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={handleSendFollowUp}
                        disabled={isLoading || !followUpQuestion.trim()}
                        className={`apple-button px-4 py-2 ${
                          isLoading || !followUpQuestion.trim()
                            ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                            : 'bg-blue-500 hover:bg-blue-600 text-white'
                        } rounded transition-colors`}
                      >
                        发送
                      </button>
                      <button
                        onClick={handleCancelFollowUp}
                        className="apple-button px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded transition-colors"
                      >
                        取消
                      </button>
                    </div>
                  </div>
                </div>
              ),
              document.body
            )}
          </div>
        </div>
      )}
    </div>
  );
};