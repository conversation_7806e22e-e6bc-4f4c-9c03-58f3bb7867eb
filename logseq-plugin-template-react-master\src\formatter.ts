/**
 * 响应格式化工具
 * 处理 AI 响应文本到 Logseq 块格式的转换
 */

/**
 * Logseq 块数据结构
 */
export interface BlockEntity {
  content: string;
  children?: BlockEntity[];
}

/**
 * 将 AI 响应文本转换为 Logseq 块格式
 * @param text AI 响应文本，可能包含 Markdown 格式
 * @returns 转换后的 Logseq 块内容数组，每个元素代表一个块
 */
export function formatResponseToBlocks(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return [];
  }

  // 预处理文本，移除可能导致错误的Markdown格式
  text = text.replace(/^>+\s/gm, ''); // 移除引用符号
  
  // 分割文本为行
  const lines = text.split(/\r?\n/);
  const blocks: string[] = [];
  let currentBlock = '';
  let inCodeBlock = false;
  let codeBlockIndentation = '';
  
  // 缩进检测相关变量
  let indentationMap = new Map<string, number>(); // 存储不同缩进级别的映射
  let currentIndentLevel = 0; // 当前缩进级别
  
  // 首先检测文本中的缩进模式
  const indentationPattern = lines
    .filter(line => line.trim().length > 0)
    .map(line => {
      const match = line.match(/^(\s+)/);
      return match ? match[1].length : 0;
    })
    .filter(length => length > 0);

  // 如果有足够的缩进样本，确定最常见的缩进单位（2空格、4空格或制表符）
  let indentUnit = 2; // 默认缩进单位为2
  if (indentationPattern.length > 0) {
    // 找出最小的非零缩进
    const nonZeroIndents = indentationPattern.filter(len => len > 0);
    if (nonZeroIndents.length > 0) {
      const minIndent = Math.min(...nonZeroIndents);
      indentUnit = minIndent;
    }
  }

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测当前行的缩进级别
    const indentMatch = line.match(/^(\s+)/);
    const currentIndentation = indentMatch ? indentMatch[1].length : 0;
    
    // 计算当前行的缩进级别
    const indentLevel = Math.floor(currentIndentation / indentUnit);
    
    // 处理代码块
    if (line.trim().startsWith('```')) {
      if (!inCodeBlock) {
        // 开始新代码块，保存之前的块
        if (currentBlock.trim()) {
          // 在块内容前添加缩进标记
          const indentPrefix = currentIndentLevel > 0 ? '  '.repeat(currentIndentLevel) : '';
          blocks.push(`${indentPrefix}${currentBlock.trim()}`);
          currentBlock = '';
        }
        // 设置代码块缩进，保留语言标识
        inCodeBlock = true;
        codeBlockIndentation = '    '; // Logseq 代码块缩进为4个空格
        
        // 提取语言标识
        const langMatch = line.trim().match(/^```(.*)$/);
        const lang = langMatch ? langMatch[1].trim() : '';
        
        // 添加代码块标记，并保持当前缩进级别
        const indentPrefix = indentLevel > 0 ? '  '.repeat(indentLevel) : '';
        if (lang) {
          currentBlock = `\`\`\` ${lang}`;
        } else {
          currentBlock = '```';
        }
        
        blocks.push(`${indentPrefix}${currentBlock}`);
        currentBlock = '';
        currentIndentLevel = indentLevel; // 保存代码块的缩进级别
      } else {
        // 结束代码块，保持当前缩进级别
        inCodeBlock = false;
        const indentPrefix = currentIndentLevel > 0 ? '  '.repeat(currentIndentLevel) : '';
        blocks.push(`${indentPrefix}\`\`\``);
      }
      continue;
    }

    // 处理代码块内的行
    if (inCodeBlock) {
      const indentPrefix = currentIndentLevel > 0 ? '  '.repeat(currentIndentLevel) : '';
      blocks.push(`${indentPrefix}${codeBlockIndentation}${line.trimStart()}`);
      continue;
    }

    // 更新当前缩进级别
    if (line.trim().length > 0) {
      currentIndentLevel = indentLevel;
    }

    // 处理列表项
    if (line.trim().match(/^(\d+\.|-|\*|\+)\s/)) {
      // 如果当前有未保存的块，先保存
      if (currentBlock.trim()) {
        const indentPrefix = currentIndentLevel > 0 ? '  '.repeat(currentIndentLevel) : '';
        blocks.push(`${indentPrefix}${currentBlock.trim()}`);
        currentBlock = '';
      }
      // 添加列表项，去除开头的列表标记符号（如 - * + 1.），保持缩进
      const indentPrefix = indentLevel > 0 ? '  '.repeat(indentLevel) : '';
      // 移除列表标记，保留实际内容
      const contentWithoutMark = line.trim().replace(/^(\d+\.|-|\*|\+)\s+/, '');
      blocks.push(`${indentPrefix}${contentWithoutMark}`);
      continue;
    }

    // 处理标题，保留标题标记
    if (line.trim().match(/^#{1,6}\s/)) {
      // 如果当前有未保存的块，先保存
      if (currentBlock.trim()) {
        const indentPrefix = currentIndentLevel > 0 ? '  '.repeat(currentIndentLevel) : '';
        blocks.push(`${indentPrefix}${currentBlock.trim()}`);
        currentBlock = '';
      }
      // 添加标题，保持缩进
      const indentPrefix = indentLevel > 0 ? '  '.repeat(indentLevel) : '';
      blocks.push(`${indentPrefix}${line.trim()}`);
      continue;
    }

    // 处理空行 - 在 Logseq 中，空行表示块的结束
    if (!line.trim()) {
      if (currentBlock.trim()) {
        const indentPrefix = currentIndentLevel > 0 ? '  '.repeat(currentIndentLevel) : '';
        blocks.push(`${indentPrefix}${currentBlock.trim()}`);
        currentBlock = '';
      }
      continue;
    }

    // 其他情况，累积到当前块
    if (currentBlock) {
      currentBlock += ' ' + line.trim();
    } else {
      currentBlock = line.trim();
    }
  }

  // 保存最后的块
  if (currentBlock.trim()) {
    const indentPrefix = currentIndentLevel > 0 ? '  '.repeat(currentIndentLevel) : '';
    blocks.push(`${indentPrefix}${currentBlock.trim()}`);
  }

  return blocks;
}

/**
 * 准备用于插入到 Logseq 的块数据
 * @param blocks 格式化后的块内容数组
 * @param parentUUID 父块的 UUID（如果有）
 * @returns 适用于 insertBatchBlock 的块数据
 */
export function prepareBlocksForInsertion(blocks: string[], parentUUID?: string): BlockEntity | null {
  if (!blocks || blocks.length === 0) {
    return null;
  }

  // 如果只有一个块，直接返回其内容
  if (blocks.length === 1) {
    return {
      content: blocks[0]
    };
  }

  // 为多个块构建树形结构
  const firstBlock: BlockEntity = {
    content: blocks[0]
  };

  if (blocks.length > 1) {
    firstBlock.children = blocks.slice(1).map(block => ({
      content: block
    }));
  }

  return firstBlock;
} 