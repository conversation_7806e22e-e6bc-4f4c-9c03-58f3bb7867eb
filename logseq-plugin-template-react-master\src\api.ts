/**
 * API 集成功能模块
 * 处理与 AI 服务的通信
 */
import { PluginSettings, getSettings, ApiConfig, DEFAULT_SETTINGS } from './settings';
import { ContextData } from './contextManager';

/**
 * 聊天消息接口
 */
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * API 请求配置接口 - OpenAI格式
 */
interface OpenAIRequestConfig {
  messages: ChatMessage[];
  stream: boolean;
  model: string;
  temperature?: number;
  max_tokens?: number;
}

/**
 * API 请求配置接口 - Google Gemini格式
 */
interface GeminiRequestConfig {
  contents: Array<{role: string, parts: Array<{text: string}>}>;
  generationConfig: {
    temperature?: number;
    maxOutputTokens?: number;
  };
}

/**
 * API 请求配置接口 - 通用
 */
type ApiRequestConfig = OpenAIRequestConfig;

/**
 * API 响应处理接口
 */
export interface ApiResponseHandlers {
  onChunk: (chunk: string) => void;
  onComplete: (fullResponse: string) => void;
  onError: (error: string) => void;
}

/**
 * API 连接错误类
 */
class ApiConnectionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ApiConnectionError';
  }
}

/**
 * 创建聊天请求消息
 * @param userMessage 用户输入的消息
 * @param context 上下文数据
 * @param systemPrompt 系统提示（可选）
 * @returns 聊天消息数组
 */
async function createChatMessages(
  userMessage: string,
  context: ContextData,
  systemPrompt?: string
): Promise<ChatMessage[]> {
  const messages: ChatMessage[] = [];
  const settings = await getSettings();

  // 添加系统提示（如果有）
  if (systemPrompt) {
    messages.push({
      role: 'system',
      content: systemPrompt
    });
  } else {
    // 默认系统提示
    messages.push({
      role: 'system',
      content: '你是一个有用的助手，可以帮助用户处理和分析文本内容。提供简洁、准确的回答。'
    });
  }

  // 构建用户消息，包含上下文信息
  let userContent = userMessage;

  // 如果用户消息没有包含上下文内容，则添加上下文前缀
  if (context.content && !userMessage.includes(context.content)) {
    // 创建上下文前缀
    let contextPrefix = '';
    switch (context.type) {
      case 'selection':
        contextPrefix = '以下是选中的文本：\n\n';
        break;
      case 'block':
        contextPrefix = '以下是当前块的内容：\n\n';
        break;
      case 'blocks':
        contextPrefix = '以下是选中的多个块：\n\n';
        break;
      case 'smart':
        contextPrefix = '以下是上下文内容：\n\n';
        break;
      default:
        break;
    }

    userContent = `${contextPrefix}${context.content}\n\n${userMessage}`;
  }

  // 添加用户消息
  messages.push({
    role: 'user',
    content: userContent
  });

  return messages;
}

/**
 * 解析流式响应的数据块
 * @param chunk 响应数据块
 * @returns 提取的文本内容
 */
function parseStreamChunk(chunk: string): string {
  try {
    // 处理多行数据
    const lines = chunk
      .split('\n')
      .filter(line => line.trim() !== '' && line.trim() !== 'data: [DONE]');

    // 提取所有行中的内容
    let content = '';
    for (const line of lines) {
      // 如果以 data: 开头，则为 SSE 格式的数据
      if (line.startsWith('data: ')) {
        // 提取 data: 前缀之后的 JSON
        const jsonStr = line.replace(/^data: /, '');
        if (!jsonStr || jsonStr === '[DONE]') continue;

        try {
          const json = JSON.parse(jsonStr);

          // 支持不同 API 提供商的响应格式
          // 1. OpenAI 格式
          if (json.choices && json.choices[0]) {
            // OpenAI 流式格式
            if (json.choices[0].delta && json.choices[0].delta.content) {
              content += json.choices[0].delta.content;
            }
            // OpenAI 非流式格式
            else if (json.choices[0].message && json.choices[0].message.content) {
              content += json.choices[0].message.content;
            }
          }
          // 2. Google Gemini 格式
          else if (json.candidates && json.candidates[0]) {
            // Gemini 流式响应格式
            if (json.candidates[0].content && json.candidates[0].content.parts) {
              json.candidates[0].content.parts.forEach((part: any) => {
                if (part.text) {
                  content += part.text;
                }
              });
            }
          }
          // 3. 通用格式：直接包含内容的字段
          else if (json.content) {
            content += json.content;
          }
          // 4. 其他自定义 API 格式
          else if (json.text) {
            content += json.text;
          }
        } catch (e) {
          // 解析 JSON 失败，但我们不将原始 JSON 字符串添加到内容中
          console.warn('解析 JSON 失败:', jsonStr);
        }
      }
      // 不再处理非 SSE 格式的纯文本响应，以避免元数据混入
    }

    return content;
  } catch (error) {
    console.error('解析数据块失败:', error);
    return '';
  }
}

/**
 * 获取当前API配置
 * @returns 当前API配置
 */
export async function getCurrentApiConfig() {
  const settings = await getSettings();
  console.log('获取当前API配置');

  // 检查API配置是否存在
  if (!Array.isArray(settings.apiConfigs) || settings.apiConfigs.length === 0) {
    console.error('API 配置列表为空，请在设置中添加API配置');
    throw new ApiConnectionError('API 配置列表为空，请在设置中添加API配置');
  }

  // 获取当前选中的API配置
  const currentConfigId = settings.currentApiConfigId;
  let currentConfig = settings.apiConfigs.find(config => config.id === currentConfigId);

  // 如果找不到当前配置，使用第一个配置
  if (!currentConfig && settings.apiConfigs.length > 0) {
    currentConfig = settings.apiConfigs[0];
    console.log('找不到当前选中的配置，使用第一个配置:', currentConfig.name);
  }

  // 如果还是找不到配置，抛出错误
  if (!currentConfig) {
    console.error('API 未配置，请在设置中配置API');
    throw new ApiConnectionError('API 未配置，请在设置中配置API');
  }

  // 检查必要的字段
  if (!currentConfig.apiUrl || !currentConfig.apiKey) {
    console.error('当前API配置不完整，请检查API URL和API Key');
    throw new ApiConnectionError('当前API配置不完整，请检查API URL和API Key');
  }

  return currentConfig;
}

/**
 * 测试API连接
 * @param apiConfig API配置
 * @returns 测试结果
 */
export async function testApiConnection(apiConfig: ApiConfig): Promise<{success: boolean, message: string}> {
  try {
    // 验证配置
    if (!apiConfig.apiUrl || !apiConfig.apiKey) {
      return { success: false, message: 'API URL 或 API Key 不能为空' };
    }

    // 获取设置
    const settings = await getSettings();
    const timeout = settings.requestTimeout || 60; // 默认超时时间60秒

    // 准备测试请求
    const testMessage: ChatMessage[] = [
      { role: 'system', content: '你是一个有用的助手。' },
      { role: 'user', content: '你好，这是一个连接测试。请回复"连接成功"。' }
    ];

    let requestBody: any;

    // 根据不同的API提供商构建不同的请求体
    switch (apiConfig.provider) {
      case 'gemini':
        // 转换聊天消息为Gemini格式
        const geminiContents = testMessage.map(msg => {
          // Gemini使用不同的角色名称系统
          let geminiRole: string;
          if (msg.role === 'system') {
            // 将系统消息转换为用户消息，因为Gemini没有系统角色
            geminiRole = 'user';
          } else if (msg.role === 'assistant') {
            geminiRole = 'model'; // Gemini使用model作为助手角色
          } else {
            geminiRole = msg.role;
          }

          return {
            role: geminiRole,
            parts: [{ text: msg.content }]
          };
        });

        requestBody = {
          contents: geminiContents,
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 50
          }
        };
        break;

      case 'openai':
      default:
        // 使用OpenAI格式
        const requestConfig: ApiRequestConfig = {
          messages: testMessage,
          stream: false,
          model: apiConfig.modelName || 'gpt-3.5-turbo',
          temperature: 0.7,
          max_tokens: 50
        };

        requestBody = requestConfig;
        break;
    }

    // 设置超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout * 1000); // 转换为毫秒

    try {
      // 发送请求
      const response = await fetch(apiConfig.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiConfig.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      // 清除超时定时器
      clearTimeout(timeoutId);

      // 检查响应状态
      if (!response.ok) {
        let errorMessage = `API 请求失败: ${response.status} ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMessage = `${errorMessage}. ${errorData.error?.message || JSON.stringify(errorData)}`;
        } catch (e) {
          // 如果解析 JSON 失败，使用默认错误消息
        }
        return { success: false, message: errorMessage };
      }

      // 解析响应
      const data = await response.json();

      // 根据不同的API提供商检查响应
      switch (apiConfig.provider) {
        case 'gemini':
          if (data.candidates && data.candidates.length > 0) {
            return { success: true, message: '连接成功！Gemini API 正常工作。' };
          }
          break;

        case 'openai':
        default:
          if (data.choices && data.choices.length > 0) {
            return { success: true, message: '连接成功！OpenAI API 正常工作。' };
          }
          break;
      }

      // 如果没有匹配的响应格式
      return { success: false, message: '收到响应，但格式不正确。请检查API提供商设置是否正确。' };
    } catch (fetchError) {
      // 清除超时定时器
      clearTimeout(timeoutId);

      // 如果是超时错误
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        return { success: false, message: `API 请求超时（${timeout}秒），请在设置中增加超时时间或检查网络连接。` };
      }

      throw fetchError; // 将其他错误传递给外部catch
    }
  } catch (error) {
    return {
      success: false,
      message: `连接测试失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 发送聊天请求到 API
 * @param userMessage 用户输入消息
 * @param context 上下文数据
 * @param handlers 响应处理程序
 * @param systemPrompt 系统提示（可选）
 * @param modelOverride 覆盖默认模型（可选）
 * @param apiConfigId 指定的API配置ID（可选）
 */
export async function sendChatRequest(
  userMessage: string,
  context: ContextData,
  handlers: ApiResponseHandlers,
  systemPrompt?: string,
  modelOverride?: string,
  apiConfigId?: string
): Promise<void> {
  // 获取设置
  const settings = await getSettings();

  // 获取API配置，如果指定了配置ID，则使用指定的配置
  let apiConfig: ApiConfig;
  if (apiConfigId && settings.apiConfigs) {
    const specificConfig = settings.apiConfigs.find(config => config.id === apiConfigId);
    if (specificConfig) {
      apiConfig = specificConfig;
      console.log('使用指定的API配置:', apiConfig.name);
    } else {
      console.warn('未找到指定的API配置，使用默认配置');
      apiConfig = await getCurrentApiConfig();
    }
  } else {
    apiConfig = await getCurrentApiConfig();
  }

  // 获取超时和重试设置
  const timeout = settings.requestTimeout || 60; // 默认超时时间60秒
  const maxRetries = settings.maxRetries || 3;   // 默认最大重试次数3次
  const retryDelay = settings.retryDelay || 1;   // 默认重试延迟1秒

  // 创建消息数组
  const messages = await createChatMessages(userMessage, context, systemPrompt);

  // 准备请求主体
  let requestBody: any;

  // 根据不同的API提供商构建不同的请求体
  switch (apiConfig.provider) {
    case 'gemini':
      // 转换聊天消息为Gemini格式
      const geminiContents = messages.map(msg => {
        // Gemini使用不同的角色名称系统
        let geminiRole: string;
        if (msg.role === 'system') {
          // 将系统消息转换为用户消息，因为Gemini没有系统角色
          geminiRole = 'user';
        } else if (msg.role === 'assistant') {
          geminiRole = 'model';
        } else {
          geminiRole = msg.role;
        }

        return {
          role: geminiRole,
          parts: [{ text: msg.content }]
        };
      });

      requestBody = {
        contents: geminiContents,
        generationConfig: {}
      };

      // 添加可选参数
      if (settings.temperature !== undefined) {
        requestBody.generationConfig.temperature = settings.temperature;
      }

      if (settings.maxTokens !== undefined && settings.maxTokens > 0) {
        requestBody.generationConfig.maxOutputTokens = settings.maxTokens;
      }
      break;

    case 'openai':
    default:
      // 使用OpenAI格式
      const requestConfig: ApiRequestConfig = {
        messages,
        stream: true,
        model: modelOverride || apiConfig.modelName // 使用覆盖的模型（如果提供）
      };

      // 添加可选参数
      if (settings.temperature !== undefined) {
        requestConfig.temperature = settings.temperature;
      }

      if (settings.maxTokens !== undefined && settings.maxTokens > 0) {
        requestConfig.max_tokens = settings.maxTokens;
      }

      requestBody = requestConfig;
      break;
  }

  // 重试函数
  const makeRequest = async (attempt: number): Promise<Response> => {
    try {
      // 设置超时处理
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout * 1000); // 转换为毫秒

      console.log(`发送API请求（尝试 ${attempt}/${maxRetries}），超时时间: ${timeout}秒`);

      // 发送请求
      const response = await fetch(apiConfig.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiConfig.apiKey}`
        },
        body: JSON.stringify(requestBody), // 使用根据提供商创建的请求体
        signal: controller.signal
      });

      // 清除超时定时器
      clearTimeout(timeoutId);

      return response;
    } catch (error) {
      // 如果是超时错误且还有重试机会
      if (error instanceof Error && error.name === 'AbortError' && attempt < maxRetries) {
        console.log(`请求超时，${retryDelay}秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay * 1000));
        return makeRequest(attempt + 1);
      }
      throw error;
    }
  };

  try {
    // 开始请求，带重试
    const response = await makeRequest(1);

    // 检查响应状态
    if (!response.ok) {
      let errorMessage = `API 请求失败: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = `${errorMessage}. ${errorData.error?.message || JSON.stringify(errorData)}`;
      } catch (e) {
        // 如果解析 JSON 失败，使用默认错误消息
      }
      throw new ApiConnectionError(errorMessage);
    }

    // 处理流式响应
    if (response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let completeResponse = '';
      let buffer = '';
      let lastFlushTime = Date.now();
      const FLUSH_INTERVAL = 50; // 每50毫秒更新一次UI

      // 读取流
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          // 最后一次刷新
          if (buffer.length > 0) {
            const content = parseStreamChunk(buffer);
            if (content) {
              completeResponse += content;
              handlers.onChunk(content);
            }
          }
          handlers.onComplete(completeResponse);
          break;
        }

        // 将二进制数据解码为文本
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理完整的 SSE 消息
        // 查找所有完整的 data: 行，确保我们只处理完整的消息
        const dataLines = buffer.split('\n\n');

        // 保留最后一个可能不完整的块
        const lastChunk = dataLines.pop() || '';

        // 处理所有完整的块
        if (dataLines.length > 0) {
          const content = parseStreamChunk(dataLines.join('\n\n'));
          if (content) {
            completeResponse += content;
            handlers.onChunk(content);
          }
        }

        // 更新缓冲区为最后一个可能不完整的块
        buffer = lastChunk;

        // 如果自上次刷新以来已经过去了足够的时间，也处理缓冲区
        const now = Date.now();
        if (now - lastFlushTime >= FLUSH_INTERVAL && buffer.includes('data: ')) {
          const content = parseStreamChunk(buffer);
          if (content) {
            completeResponse += content;
            handlers.onChunk(content);
            buffer = ''; // 清空已处理的缓冲区
          }
          lastFlushTime = now;
        }
      }
    } else {
      throw new ApiConnectionError('API 响应中没有正文');
    }
  } catch (error) {
    console.error('API请求错误:', error);

    if (error instanceof ApiConnectionError) {
      handlers.onError(error.message);
    } else if (error instanceof Error && error.name === 'AbortError') {
      // 超时错误处理
      const timeoutMessage = `API请求超时（${timeout}秒）。已尝试 ${maxRetries} 次重试。\n\n建议操作：\n1. 在设置中增加超时时间\n2. 检查网络连接\n3. 确认API服务器状态`;
      handlers.onError(timeoutMessage);
    } else {
      // 其他错误
      const errorMessage = error instanceof Error ? error.message : String(error);
      handlers.onError(`请求失败: ${errorMessage}\n\n如果问题持续存在，请尝试在设置中增加超时时间或重试次数。`);
    }
  }
}